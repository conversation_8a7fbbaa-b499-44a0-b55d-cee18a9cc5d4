<?php

use App\Support\MoneyInput;

it('parses CLP with thousands and no decimals', function () {
    expect(MoneyInput::parseToMinor('2.500', 'CLP'))->toBe(2500)
        ->and(MoneyInput::parseToMinor(2500, 'CLP'))->toBe(2500);
});

it('parses USD with decimals and separators', function () {
    expect(MoneyInput::parseToMinor('1,234.56', 'USD'))->toBe(123456)
        ->and(MoneyInput::parseToMinor('1234.56', 'USD'))->toBe(123456)
        ->and(MoneyInput::parseToMinor(12.5, 'USD'))->toBe(1250);
});

