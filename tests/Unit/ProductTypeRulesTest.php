<?php

use App\Models\ProductType;
use App\Services\ProductTypeRules;
use Illuminate\Support\Facades\Validator;

it('enforces dynamic rules semantics with the validator', function () {
    $type = new ProductType([
        'reglas' => [
            'producto' => [
                'capacidad' => 'required',
                'interfaz' => ['in' => ['USB-A','USB-C']],
                'constante' => ['equals' => 500],
            ],
            'variante' => [
                'color' => 'required',
            ],
        ],
    ]);

    $productRules = ProductTypeRules::productAttributeRules($type);
    $variantRules = ProductTypeRules::variantAttributeRules($type);

    // Product valid
    $validProduct = Validator::make([
        'attributes' => ['capacidad' => 500, 'interfaz' => 'USB-A', 'constante' => 500],
    ], collect($productRules)->mapWithKeys(fn($r, $k) => ["attributes.$k" => $r])->all());
    expect($validProduct->fails())->toBeFalse();

    // Product invalid: missing required and invalid value
    $invalidProduct = Validator::make([
        'attributes' => ['interfaz' => 'HDMI', 'constante' => 400],
    ], collect($productRules)->mapWithKeys(fn($r, $k) => ["attributes.$k" => $r])->all());
    expect($invalidProduct->fails())->toBeTrue();

    // Variant valid
    $validVariant = Validator::make([
        'attributes' => ['color' => 'Negro'],
    ], collect($variantRules)->mapWithKeys(fn($r, $k) => ["attributes.$k" => $r])->all());
    expect($validVariant->fails())->toBeFalse();

    // Variant invalid: missing color
    $invalidVariant = Validator::make([
        'attributes' => [],
    ], collect($variantRules)->mapWithKeys(fn($r, $k) => ["attributes.$k" => $r])->all());
    expect($invalidVariant->fails())->toBeTrue();
});
