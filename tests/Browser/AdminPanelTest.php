<?php

use App\Models\Customer;
use App\Models\User;

describe('Admin Panel Navigation', function () {
    beforeEach(function () {
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');
    });

    test('admin can navigate to customers list', function () {
        $page = $this->actingAs($this->admin)->visit('/admin');

        $page->assertSee('Customers')
            ->assertNoJavascriptErrors()
            ->click('Clientes')  // Spanish label from Filament resource
            ->assertUrlIs('/admin/customers')
            ->assertSee('Clientes'); // Page title
    });

    test('admin can create new customer', function () {
        Customer::factory()->count(2)->create(); // Existing customers

        $page = $this->actingAs($this->admin)->visit('/admin/customers');

        $page->click('New Customer')
            ->assertUrlMatches('/admin/customers/create')
            ->fill('name', 'Test Customer')
            ->fill('email', '<EMAIL>')
            ->fill('phone', '+56 9 1234 5678')
            ->select('country', 'CL')
            ->click('Create')
            ->assertSee('Test Customer')
            ->assertNoJavascriptErrors();

        $this->assertDatabaseHas('customers', [
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'country' => 'CL',
        ]);
    });

    test('admin can search and filter customers', function () {
        Customer::factory()->create(['name' => 'TechCorp', 'country' => 'CL']);
        Customer::factory()->create(['name' => 'Another Corp', 'country' => 'AR']);
        Customer::factory()->create(['name' => 'Inactive Corp', 'is_active' => false]);

        $page = $this->actingAs($this->admin)->visit('/admin/customers');

        // Search by name
        $page->fill('tableSearch', 'TechCorp')
            ->wait(1000) // Wait for search to process
            ->assertSee('TechCorp')
            ->assertDontSee('Another Corp');

        // Clear search and filter by country
        $page->fill('tableSearch', '')
            ->wait(500)
            ->select('filters.country', 'CL')
            ->wait(1000)
            ->assertSee('TechCorp')
            ->assertDontSee('Another Corp');
    });

    test('admin can edit customer details', function () {
        $customer = Customer::factory()->create([
            'name' => 'Original Name',
            'email' => '<EMAIL>',
        ]);

        $page = $this->actingAs($this->admin)->visit("/admin/customers/{$customer->id}/edit");

        $page->fill('name', 'Updated Name')
            ->fill('email', '<EMAIL>')
            ->click('Save changes')
            ->assertSee('Updated Name')
            ->assertNoJavascriptErrors();

        $this->assertDatabaseHas('customers', [
            'id' => $customer->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
        ]);
    });
});

describe('Customer Quotes Admin Interface', function () {
    beforeEach(function () {
        $this->admin = User::factory()->create();
        $this->admin->assignRole('admin');
        $this->customer = Customer::factory()->create();
    });

    test('admin can view customer quotes relationship', function () {
        $this->customer->quotes()->create([
            'quote_number' => 'Q-2025-001',
            'customer_name' => $this->customer->name,
            'currency' => 'USD',
            'status' => 'borrador',
            'valid_until' => now()->addDays(30),
        ]);

        $page = $this->actingAs($this->admin)->visit("/admin/customers/{$this->customer->id}");

        $page->assertSee('Q-2025-001')
            ->assertSee('borrador')
            ->assertSee('USD')
            ->assertNoJavascriptErrors();
    });

    test('responsive design works on different viewports', function () {
        Customer::factory()->count(5)->create();

        // Test mobile viewport
        $page = $this->actingAs($this->admin)
            ->viewport(375, 667) // iPhone dimensions
            ->visit('/admin/customers');

        $page->assertNoJavascriptErrors()
            ->assertElementExists('.fi-sidebar-close-button'); // Mobile menu button

        // Test tablet viewport
        $page->viewport(768, 1024)
            ->refresh()
            ->assertNoJavascriptErrors();

        // Test desktop viewport
        $page->viewport(1920, 1080)
            ->refresh()
            ->assertNoJavascriptErrors();
    });
});

describe('Admin Panel Permissions', function () {
    test('regular user cannot access admin panel', function () {
        $user = User::factory()->create();
        $user->assignRole('panel_user'); // Role that can't access admin

        $page = $this->actingAs($user)->visit('/admin');

        $page->assertStatus(403)
            ->assertSee('403'); // Forbidden page
    });

    test('super admin has full access', function () {
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('super_admin');

        $page = $this->actingAs($superAdmin)->visit('/admin');

        $page->assertStatus(200)
            ->assertSee('Dashboard')
            ->assertNoJavascriptErrors();
    });
});
