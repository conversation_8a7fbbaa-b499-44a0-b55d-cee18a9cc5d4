<?php

declare(strict_types=1);

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductType;
use App\Models\User;
use Database\Seeders\RolesSeeder;

beforeEach(function () {
    $this->seed(RolesSeeder::class);
});

test('puede crear una taxonomía completa de productos', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // 1. Crear categoría
    $category = ProductCategory::factory()->create([
        'code' => 'PROM',
        'name' => 'Productos Promocionales',
        'metadata' => ['description' => 'Productos para promoción empresarial'],
    ]);

    // 2. Crear subcategoría
    $subcategory = ProductSubcategory::factory()->create([
        'category_id' => $category->id,
        'code' => 'TECH',
        'name' => 'Tecnología',
        'metadata' => ['target_audience' => 'profesionales'],
    ]);

    // 3. Crear tipo de producto
    $type = ProductType::factory()->create([
        'subcategory_id' => $subcategory->id,
        'code' => 'MEM',
        'name' => 'Memoria USB',
        'hs_code_sugerido' => '8471.70.00',
        'defaults' => ['capacity' => '16GB', 'interface' => 'USB-A'],
        'reglas' => ['min_capacity' => '8GB', 'max_capacity' => '128GB'],
    ]);

    // Verificar relaciones
    expect($category->subcategories)->toHaveCount(1);
    expect($subcategory->types)->toHaveCount(1);
    expect($type->subcategory->category->id)->toBe($category->id);
});

test('calcula correctamente los conteos en las tablas', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear estructura jerárquica
    $category = ProductCategory::factory()->create();
    $subcategory1 = ProductSubcategory::factory()->create(['category_id' => $category->id]);
    $subcategory2 = ProductSubcategory::factory()->create(['category_id' => $category->id]);

    $type1 = ProductType::factory()->create(['subcategory_id' => $subcategory1->id]);
    $type2 = ProductType::factory()->create(['subcategory_id' => $subcategory1->id]);
    $type3 = ProductType::factory()->create(['subcategory_id' => $subcategory2->id]);

    // Verificar conteos
    expect($category->subcategories()->count())->toBe(2);
    expect($subcategory1->types()->count())->toBe(2);
    expect($subcategory2->types()->count())->toBe(1);
});

test('elimina en cascada al eliminar categorías', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear estructura
    $category = ProductCategory::factory()->create();
    $subcategory = ProductSubcategory::factory()->create(['category_id' => $category->id]);
    $type = ProductType::factory()->create(['subcategory_id' => $subcategory->id]);

    // Eliminar categoría (debe eliminar en cascada)
    $category->delete();

    // Verificar que subcategoría y tipo se eliminaron en cascada
    expect(ProductSubcategory::find($subcategory->id))->toBeNull();
    expect(ProductType::find($type->id))->toBeNull();
});

test('valida unicidad de códigos en cada nivel', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear categoría con código único
    ProductCategory::factory()->create(['code' => 'UNIQUE']);

    // Intentar crear otra con el mismo código debe fallar
    expect(function () {
        ProductCategory::factory()->create(['code' => 'UNIQUE']);
    })->toThrow(Exception::class);
});

test('maneja correctamente metadatos JSON', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    $complexMetadata = [
        'features' => ['waterproof', 'wireless'],
        'specifications' => [
            'weight' => '150g',
            'dimensions' => ['width' => 10, 'height' => 5, 'depth' => 2],
        ],
        'target_market' => 'B2B',
    ];

    $category = ProductCategory::factory()->create(['metadata' => $complexMetadata]);

    expect($category->fresh()->metadata)->toBe($complexMetadata);
    expect($category->metadata['features'])->toContain('waterproof');
    expect($category->metadata['specifications']['weight'])->toBe('150g');
});
