<?php

use Database\Seeders\ProductTaxonomySeeder;

it('exposes the expected JSON shape without legacy fields', function () {
    $this->seed(ProductTaxonomySeeder::class);

    $quote = $this->postJson('/api/customer-quotes', [
        'customer_name' => 'Empresa',
        'currency' => 'CLP',
    ])->assertCreated()->json();

    $type = \App\Models\ProductType::query()->first();
    $product = $this->postJson("/api/customer-quotes/{$quote['id']}/products", [
        'type_id' => $type->id,
        'name' => 'Producto',
        'attributes' => ['capacidad' => 500, 'interfaz' => 'USB-A', 'material' => 'Acero 304'],
        'position' => 1,
    ])->assertCreated()->json();

    $this->postJson("/api/customer-quote-products/{$product['id']}/variants", [
        'label' => 'Var 1',
        'attributes' => ['color' => 'Negro'],
        'quantity' => 10,
        'price_currency' => 'CLP',
        'unit_price_minor' => 1000,
    ])->assertCreated();

    $res = $this->getJson("/api/customer-quotes/{$quote['id']}")
        ->assertOk()
        ->json('data');

    // Product fields
    $p = $res['products'][0];
    expect($p)->toHaveKeys([
        'type_id', 'type_code', 'type_name', 'subcategory_code', 'category_code', 'name', 'attributes', 'specs', 'hs_code', 'weight', 'volume', 'notes', 'position',
    ]);
    expect($p)->not->toHaveKeys(['product_id']);

    // Variant fields
    $v = $p['variants'][0];
    expect($v)->toHaveKeys([
        'label', 'attributes', 'quantity', 'unit_price_minor', 'price_currency', 'fx_rate_to_quote', 'position', 'notes',
    ]);
    expect($v)->not->toHaveKeys(['product_id', 'variant_id', 'variant_label']);
});
