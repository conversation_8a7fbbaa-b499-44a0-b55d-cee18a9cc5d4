<?php

declare(strict_types=1);

use App\Models\CustomerQuote;
use App\Models\EventLog;
use App\Models\User;

test('crea event log al crear una cotización', function () {
    $user = User::factory()->create();

    $response = $this->actingAs($user)
        ->postJson('/api/customer-quotes', [
            'customer_name' => 'Cliente Test',
            'currency' => 'USD',
            'country' => 'US',
            'valid_until' => now()->addDays(30)->toDateString(),
            'notes' => 'Cotización de prueba',
        ]);

    $response->assertSuccessful();

    $quote = CustomerQuote::latest()->first();

    expect(EventLog::where('entity', 'quote')
        ->where('entity_id', $quote->id)
        ->where('action', 'created')
        ->where('actor_id', $user->id)
        ->exists())->toBeTrue();

    $eventLog = EventLog::where('entity', 'quote')
        ->where('entity_id', $quote->id)
        ->where('action', 'created')
        ->first();

    expect($eventLog->payload)->toHaveKey('quote_number');
    expect($eventLog->payload)->toHaveKey('customer_name', 'Cliente Test');
    expect($eventLog->payload)->toHaveKey('currency', 'USD');
    expect($eventLog->payload)->toHaveKey('status', 'borrador');
});

test('crea event log al actualizar una cotización', function () {
    $user = User::factory()->create();
    $quote = CustomerQuote::factory()->create([
        'customer_name' => 'Cliente Original',
        'currency' => 'USD',
        'status' => 'borrador',
    ]);

    $response = $this->actingAs($user)
        ->putJson("/api/customer-quotes/{$quote->id}", [
            'customer_name' => 'Cliente Actualizado',
            'currency' => 'EUR',
            'status' => 'enviada',
        ]);

    $response->assertSuccessful();

    expect(EventLog::where('entity', 'quote')
        ->where('entity_id', $quote->id)
        ->where('action', 'updated')
        ->where('actor_id', $user->id)
        ->exists())->toBeTrue();

    $eventLog = EventLog::where('entity', 'quote')
        ->where('entity_id', $quote->id)
        ->where('action', 'updated')
        ->first();

    expect($eventLog->payload)->toHaveKey('previous_data');
    expect($eventLog->payload)->toHaveKey('new_data');
    expect($eventLog->payload)->toHaveKey('status_changed', true);
    expect($eventLog->payload)->toHaveKey('previous_status', 'borrador');
    expect($eventLog->payload)->toHaveKey('new_status', 'enviada');
});

test('crea event log al crear un producto de cotización', function () {
    $user = User::factory()->create();
    $quote = CustomerQuote::factory()->create();
    $productType = \App\Models\ProductType::factory()->create();

    $response = $this->actingAs($user)
        ->postJson("/api/customer-quotes/{$quote->id}/products", [
            'name' => 'Producto Test',
            'type_id' => $productType->id,
            'attributes' => ['color' => 'blue', 'size' => 'medium'],
            'notes' => 'Producto de prueba',
        ]);

    $response->assertSuccessful();

    $product = $quote->products()->latest()->first();

    expect(EventLog::where('entity', 'quote_product')
        ->where('entity_id', $product->id)
        ->where('action', 'created')
        ->where('actor_id', $user->id)
        ->exists())->toBeTrue();

    $eventLog = EventLog::where('entity', 'quote_product')
        ->where('entity_id', $product->id)
        ->where('action', 'created')
        ->first();

    expect($eventLog->payload)->toHaveKey('quote_id', $quote->id);
    expect($eventLog->payload)->toHaveKey('product_name', 'Producto Test');
    expect($eventLog->payload)->toHaveKey('type_id', $productType->id);
});

test('crea event log al actualizar un producto de cotización', function () {
    $user = User::factory()->create();
    $quote = CustomerQuote::factory()->create();
    $product = \App\Models\CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'name' => 'Producto Original',
    ]);

    $response = $this->actingAs($user)
        ->patchJson("/api/customer-quote-products/{$product->id}", [
            'name' => 'Producto Actualizado',
        ]);

    $response->assertSuccessful();

    expect(EventLog::where('entity', 'quote_product')
        ->where('entity_id', $product->id)
        ->where('action', 'updated')
        ->where('actor_id', $user->id)
        ->exists())->toBeTrue();

    $eventLog = EventLog::where('entity', 'quote_product')
        ->where('entity_id', $product->id)
        ->where('action', 'updated')
        ->first();

    expect($eventLog->payload)->toHaveKey('previous_data');
    expect($eventLog->payload)->toHaveKey('new_data');
    expect($eventLog->payload['previous_data'])->toHaveKey('name', 'Producto Original');
    expect($eventLog->payload['new_data'])->toHaveKey('name', 'Producto Actualizado');
});

test('crea event log al eliminar un producto de cotización', function () {
    $user = User::factory()->create();
    $quote = CustomerQuote::factory()->create();
    $product = \App\Models\CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'name' => 'Producto a Eliminar',
    ]);

    $response = $this->actingAs($user)
        ->deleteJson("/api/customer-quote-products/{$product->id}");

    $response->assertNoContent();

    expect(EventLog::where('entity', 'quote_product')
        ->where('entity_id', $product->id)
        ->where('action', 'deleted')
        ->where('actor_id', $user->id)
        ->exists())->toBeTrue();

    $eventLog = EventLog::where('entity', 'quote_product')
        ->where('entity_id', $product->id)
        ->where('action', 'deleted')
        ->first();

    expect($eventLog->payload)->toHaveKey('deleted_product_data');
    expect($eventLog->payload['deleted_product_data'])->toHaveKey('name', 'Producto a Eliminar');
});

test('crea event log al crear una variante de producto', function () {
    $user = User::factory()->create();
    $quote = CustomerQuote::factory()->create();
    $product = \App\Models\CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
    ]);

    $response = $this->actingAs($user)
        ->postJson("/api/customer-quote-products/{$product->id}/variants", [
            'label' => 'Variante Test',
            'attributes' => ['color' => 'blue', 'size' => 'medium'],
            'quantity' => 5,
            'unit_price_minor' => 10000,
            'price_currency' => 'USD',
            'fx_rate_to_quote' => 1,
        ]);

    $response->assertSuccessful();

    $variant = $product->variants()->latest()->first();

    expect(EventLog::where('entity', 'quote_variant')
        ->where('entity_id', $variant->id)
        ->where('action', 'created')
        ->where('actor_id', $user->id)
        ->exists())->toBeTrue();

    $eventLog = EventLog::where('entity', 'quote_variant')
        ->where('entity_id', $variant->id)
        ->where('action', 'created')
        ->first();

    expect($eventLog->payload)->toHaveKey('quote_id', $quote->id);
    expect($eventLog->payload)->toHaveKey('product_id', $product->id);
    expect($eventLog->payload)->toHaveKey('variant_label', 'Variante Test');
    expect($eventLog->payload)->toHaveKey('quantity', 5);
    expect($eventLog->payload)->toHaveKey('unit_price_minor', 10000);
});

test('crea event log al actualizar una variante de producto', function () {
    $user = User::factory()->create();
    $quote = CustomerQuote::factory()->create();
    $product = \App\Models\CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
    ]);
    $variant = \App\Models\CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'label' => 'Variante Original',
        'attributes' => ['color' => 'red', 'size' => 'large'],
        'quantity' => 3,
    ]);

    $response = $this->actingAs($user)
        ->patchJson("/api/customer-quote-product-variants/{$variant->id}", [
            'label' => 'Variante Actualizada',
            'quantity' => 8,
        ]);

    $response->assertSuccessful();

    expect(EventLog::where('entity', 'quote_variant')
        ->where('entity_id', $variant->id)
        ->where('action', 'updated')
        ->where('actor_id', $user->id)
        ->exists())->toBeTrue();

    $eventLog = EventLog::where('entity', 'quote_variant')
        ->where('entity_id', $variant->id)
        ->where('action', 'updated')
        ->first();

    expect($eventLog->payload)->toHaveKey('previous_data');
    expect($eventLog->payload)->toHaveKey('new_data');
    expect($eventLog->payload['previous_data'])->toHaveKey('label', 'Variante Original');
    expect($eventLog->payload['new_data'])->toHaveKey('label', 'Variante Actualizada');
});

test('crea event log al eliminar una variante de producto', function () {
    $user = User::factory()->create();
    $quote = CustomerQuote::factory()->create();
    $product = \App\Models\CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
    ]);
    $variant = \App\Models\CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'label' => 'Variante a Eliminar',
        'attributes' => ['color' => 'green', 'size' => 'small'],
    ]);

    $response = $this->actingAs($user)
        ->deleteJson("/api/customer-quote-product-variants/{$variant->id}");

    $response->assertNoContent();

    expect(EventLog::where('entity', 'quote_variant')
        ->where('entity_id', $variant->id)
        ->where('action', 'deleted')
        ->where('actor_id', $user->id)
        ->exists())->toBeTrue();

    $eventLog = EventLog::where('entity', 'quote_variant')
        ->where('entity_id', $variant->id)
        ->where('action', 'deleted')
        ->first();

    expect($eventLog->payload)->toHaveKey('deleted_variant_data');
    expect($eventLog->payload['deleted_variant_data'])->toHaveKey('label', 'Variante a Eliminar');
});
