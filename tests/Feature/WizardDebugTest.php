<?php

declare(strict_types=1);

use App\Filament\Resources\CustomerQuotes\Pages\CreateQuoteWizard;
use App\Models\Customer;
use App\Models\User;
use Livewire\Livewire;

test('wizard component renders without errors', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    // Crear algunos datos de prueba
    Customer::factory()->count(2)->create(['is_active' => true]);

    // Testear el componente Livewire directamente
    $component = Livewire::test(CreateQuoteWizard::class);
    
    // Verificar que el componente se renderiza sin errores
    $component->assertStatus(200);
    
    // Verificar que los pasos están presentes
    $component->assertSee('Cliente')
              ->assertSee('Configuración')
              ->assertSee('Productos')
              ->assertSee('Variantes');
});

test('wizard step 3 renders product selection view', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    Customer::factory()->count(2)->create(['is_active' => true]);

    $component = Livewire::test(CreateQuoteWizard::class);
    
    // Navegar al paso 3
    $component->set('activeStep', 2); // Paso 3 (índice 2)
    
    // Verificar que la vista de selección de productos se renderiza
    $component->assertSee('Buscar productos por nombre o código')
              ->assertSee('productos seleccionados')
              ->assertSee('product-selection');
});

test('wizard step 4 renders variant configuration view', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    Customer::factory()->count(2)->create(['is_active' => true]);

    $component = Livewire::test(CreateQuoteWizard::class);
    
    // Navegar al paso 4
    $component->set('activeStep', 3); // Paso 4 (índice 3)
    
    // Verificar que la vista de configuración de variantes se renderiza
    $component->assertSee('Configuración de Variantes')
              ->assertSee('variant-configuration');
});

test('wizard CSS classes are present in rendered HTML', function () {
    $user = User::factory()->create();
    $this->actingAs($user);

    Customer::factory()->count(2)->create(['is_active' => true]);

    $component = Livewire::test(CreateQuoteWizard::class);
    
    // Obtener el HTML renderizado
    $html = $component->html();
    
    // Verificar que las clases CSS están presentes
    expect($html)->toContain('w-3')
                 ->toContain('h-3')
                 ->toContain('w-4')
                 ->toContain('h-4')
                 ->toContain('product-selection')
                 ->toContain('variant-configuration');
    
    // Verificar que los iconos SVG tienen las clases correctas
    expect($html)->toContain('class="w-3 h-3"')
                 ->toContain('class="w-4 h-4"');
});
