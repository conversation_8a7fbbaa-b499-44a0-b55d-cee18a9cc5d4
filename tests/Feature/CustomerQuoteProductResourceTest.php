<?php

declare(strict_types=1);

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteGroup;
use App\Models\CustomerQuoteProduct;
use App\Models\User;
use Database\Seeders\ProductTaxonomySeeder;
use Database\Seeders\RolesSeeder;

beforeEach(function () {
    $this->seed([
        RolesSeeder::class,
        ProductTaxonomySeeder::class,
    ]);
});

test('puede ver la lista de productos de cotización', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1, // ID válido de product_types
    ]);

    $this->actingAs($user)
        ->get('/admin/customer-quote-products')
        ->assertSuccessful()
        ->assertSee($product->name)
        ->assertSee($quote->quote_number);
});

test('puede ver los detalles de un producto de cotización', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);

    $this->actingAs($user)
        ->get("/admin/customer-quote-products/{$product->id}")
        ->assertSuccessful()
        ->assertSee($product->name)
        ->assertSee($quote->quote_number);
});

test('puede crear un nuevo producto de cotización', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();

    $this->actingAs($user)
        ->get('/admin/customer-quote-products/create')
        ->assertSuccessful();
});

test('puede editar un producto de cotización', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);

    $this->actingAs($user)
        ->get("/admin/customer-quote-products/{$product->id}/edit")
        ->assertSuccessful()
        ->assertSee($product->name);
});

test('calcula correctamente el total de unidades de un producto', function () {
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);

    // Sin variantes, debe retornar 0
    expect($product->totalUnits())->toBe(0);
});

test('calcula correctamente el total de un producto', function () {
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);

    // Sin variantes, debe retornar 0
    expect($product->totalAmount())->toBe(0.0);
});

test('calcula correctamente el precio promedio por unidad', function () {
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);

    // Sin variantes, debe retornar null
    expect($product->avgUnitPrice())->toBeNull();
});

test('identifica correctamente si es parte de un kit con precio fijo', function () {
    $quote = CustomerQuote::factory()->create();
    $group = CustomerQuoteGroup::factory()->withFixedPrice()->create(['customer_quote_id' => $quote->id]);
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'group_id' => $group->id,
        'type_id' => 1,
    ]);

    expect($product->isPartOfKitWithFixedPrice())->toBeTrue();
});

test('identifica correctamente si no es parte de un kit con precio fijo', function () {
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);

    expect($product->isPartOfKitWithFixedPrice())->toBeFalse();
});

test('retorna las unidades por kit cuando es parte de un kit con precio fijo', function () {
    $quote = CustomerQuote::factory()->create();
    $group = CustomerQuoteGroup::factory()->withFixedPrice()->create(['customer_quote_id' => $quote->id]);
    $product = CustomerQuoteProduct::factory()->forKit()->create([
        'customer_quote_id' => $quote->id,
        'group_id' => $group->id,
        'type_id' => 1,
    ]);

    expect($product->getUnitsPerKit())->toBe($product->units_per_kit);
});

test('retorna null para unidades por kit cuando no es parte de un kit con precio fijo', function () {
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);

    expect($product->getUnitsPerKit())->toBeNull();
});
