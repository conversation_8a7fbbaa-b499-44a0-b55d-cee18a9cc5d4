<?php

use App\Models\Supplier;

test('puede crear un proveedor', function () {
    $supplierData = [
        'code' => 'SUP001',
        'name' => 'Proveedor Test',
        'contact' => [
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
        ],
        'active' => true,
        'notes' => 'Proveedor de prueba',
    ];

    $response = $this->postJson('/api/suppliers', $supplierData);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'id',
            'code',
            'name',
            'contact',
            'active',
            'notes',
            'created_at',
            'updated_at',
        ]);

    $this->assertDatabaseHas('suppliers', [
        'code' => 'SUP001',
        'name' => 'Proveedor Test',
        'active' => true,
    ]);
});

test('puede listar proveedores activos', function () {
    Supplier::factory()->create(['active' => true, 'name' => 'Proveedor Activo']);
    Supplier::factory()->create(['active' => false, 'name' => 'Proveedor Inactivo']);

    $response = $this->getJson('/api/suppliers');

    $response->assertSuccessful()
        ->assertJsonCount(1)
        ->assertJsonFragment(['name' => 'Proveedor Activo'])
        ->assertJsonMissing(['name' => 'Proveedor Inactivo']);
});

test('valida que el código sea único', function () {
    Supplier::factory()->create(['code' => 'SUP001']);

    $supplierData = [
        'code' => 'SUP001',
        'name' => 'Otro Proveedor',
    ];

    $response = $this->postJson('/api/suppliers', $supplierData);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['code']);
});

test('puede actualizar un proveedor', function () {
    $supplier = Supplier::factory()->create(['name' => 'Nombre Original']);

    $updateData = [
        'code' => $supplier->code,
        'name' => 'Nombre Actualizado',
        'active' => true,
    ];

    $response = $this->putJson("/api/suppliers/{$supplier->id}", $updateData);

    $response->assertSuccessful()
        ->assertJsonFragment(['name' => 'Nombre Actualizado']);

    $this->assertDatabaseHas('suppliers', [
        'id' => $supplier->id,
        'name' => 'Nombre Actualizado',
    ]);
});

test('puede eliminar un proveedor', function () {
    $supplier = Supplier::factory()->create();

    $response = $this->deleteJson("/api/suppliers/{$supplier->id}");

    $response->assertNoContent();

    $this->assertDatabaseMissing('suppliers', [
        'id' => $supplier->id,
    ]);
});
