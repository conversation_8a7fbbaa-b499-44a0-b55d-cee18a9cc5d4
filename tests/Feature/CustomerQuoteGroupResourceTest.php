<?php

declare(strict_types=1);

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteGroup;
use App\Models\User;
use Database\Seeders\RolesSeeder;

beforeEach(function () {
    $this->seed(RolesSeeder::class);
});

test('puede ver la lista de grupos de cotización', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $group = CustomerQuoteGroup::factory()->create(['customer_quote_id' => $quote->id]);

    $this->actingAs($user)
        ->get('/admin/customer-quote-groups')
        ->assertSuccessful()
        ->assertSee($group->name)
        ->assertSee($quote->quote_number);
});

test('puede crear un nuevo grupo de cotización', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();

    $this->actingAs($user)
        ->get('/admin/customer-quote-groups/create')
        ->assertSuccessful();

    $groupData = [
        'customer_quote_id' => $quote->id,
        'name' => 'Kit de Promoción',
        'notes' => 'Kit especial para promoción',
        'position' => 1,
        'quantity' => 10,
        'has_fixed_price' => true,
        'unit_price_minor' => 5000, // $50.00 en centavos
        'price_currency' => 'USD',
        'fx_rate_to_quote' => 1.0,
    ];

    $this->actingAs($user)
        ->post('/admin/customer-quote-groups', $groupData)
        ->assertRedirect();

    $this->assertDatabaseHas('client_quote_groups', [
        'name' => 'Kit de Promoción',
        'customer_quote_id' => $quote->id,
        'quantity' => 10,
        'unit_price_minor' => 5000,
        'price_currency' => 'USD',
    ]);
});

test('puede ver los detalles de un grupo de cotización', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $group = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'name' => 'Kit de Prueba',
        'quantity' => 5,
        'unit_price_minor' => 3000,
        'price_currency' => 'USD',
    ]);

    $this->actingAs($user)
        ->get("/admin/customer-quote-groups/{$group->id}")
        ->assertSuccessful()
        ->assertSee('Kit de Prueba')
        ->assertSee('5')
        ->assertSee('USD');
});

test('puede editar un grupo de cotización', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $group = CustomerQuoteGroup::factory()->create(['customer_quote_id' => $quote->id]);

    $this->actingAs($user)
        ->get("/admin/customer-quote-groups/{$group->id}/edit")
        ->assertSuccessful();

    $updatedData = [
        'name' => 'Kit Actualizado',
        'quantity' => 15,
        'notes' => 'Notas actualizadas',
    ];

    $this->actingAs($user)
        ->put("/admin/customer-quote-groups/{$group->id}", $updatedData)
        ->assertRedirect();

    $this->assertDatabaseHas('client_quote_groups', [
        'id' => $group->id,
        'name' => 'Kit Actualizado',
        'quantity' => 15,
        'notes' => 'Notas actualizadas',
    ]);
});

test('calcula correctamente el total del grupo con precio fijo', function () {
    $quote = CustomerQuote::factory()->create(['currency' => 'USD']);
    $group = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'quantity' => 10,
        'unit_price_minor' => 5000, // $50.00
        'price_currency' => 'USD',
        'fx_rate_to_quote' => 1.0,
    ]);

    expect($group->totalAmount())->toBe(500.0); // 10 * $50.00
    expect($group->avgUnitPrice())->toBe(50.0);
    expect($group->totalUnits())->toBe(10);
    expect($group->isKitWithFixedPrice())->toBeTrue();
});

test('calcula correctamente el total del grupo sin precio fijo', function () {
    $quote = CustomerQuote::factory()->create(['currency' => 'USD']);
    $group = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'quantity' => 5,
        'unit_price_minor' => null,
        'price_currency' => null,
    ]);

    expect($group->isKitWithFixedPrice())->toBeFalse();
    expect($group->totalUnits())->toBe(0); // Sin productos asociados
});
