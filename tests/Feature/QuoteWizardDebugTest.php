<?php

declare(strict_types=1);

use App\Filament\Pages\QuoteWizard;
use App\Models\User;
use Livewire\Livewire;

beforeEach(function () {
    $this->seed(\Database\Seeders\RolesSeeder::class);
});

test('debug quote wizard rendering', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $component = Livewire::actingAs($user)->test(QuoteWizard::class);
    
    // Imprimir el HTML renderizado para debugging
    echo "\n=== HTML RENDERED ===\n";
    echo $component->html();
    echo "\n=====================\n";
    
    // Verificar que la página se carga
    $component->assertOk();
});
