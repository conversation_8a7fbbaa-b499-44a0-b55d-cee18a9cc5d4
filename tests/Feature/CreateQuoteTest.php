<?php

it('creates a quote with minimal payload', function () {
    $res = $this->postJson('/api/customer-quotes', [
        'customer_name' => 'Empresa X',
        'currency' => 'CLP',
        'valid_until' => now()->addDay()->toDateString(),
        'notes' => 'Prueba',
    ]);

    $res->assertCreated()
        ->assertJsonPath('customer_name', 'Empresa X')
        ->assertJsonPath('currency', 'CLP')
        ->assertJsonPath('status', 'borrador');
});

it('rejects invalid currency and past valid_until', function () {
    $res = $this->postJson('/api/customer-quotes', [
        'customer_name' => 'Empresa X',
        'currency' => 'CL', // invalid size
        'valid_until' => now()->subDay()->toDateString(),
    ]);

    $res->assertUnprocessable();
    expect($res['errors'])->toHaveKeys(['currency', 'valid_until']);
});
