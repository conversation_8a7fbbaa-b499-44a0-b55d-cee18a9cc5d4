<?php

use App\Models\Customer;
use App\Models\CustomerQuote;

test('puede crear un cliente', function () {
    $client = Customer::factory()->create([
        'name' => 'Cliente de Prueba',
        'email' => '<EMAIL>',
        'country' => 'CL',
    ]);

    expect($client->name)->toBe('Cliente de Prueba');
    expect($client->email)->toBe('<EMAIL>');
    expect($client->country)->toBe('CL');
    expect($client->is_active)->toBeTrue();
});

test('puede obtener el nombre completo del cliente', function () {
    $client = Customer::factory()->individual()->create([
        'name' => '<PERSON>',
        'company' => null,
    ]);

    expect($client->full_name)->toBe('<PERSON>');

    $clientCompany = Customer::factory()->company()->create([
        'name' => 'Empresa Test',
        'company' => 'Empresa Test S.A.',
    ]);

    expect($clientCompany->full_name)->toBe('Empresa Test (Empresa Test S.A.)');
});

test('puede obtener la dirección completa', function () {
    $client = Customer::factory()->create([
        'address' => 'Av. Test 123',
        'city' => 'Santiago',
        'state' => 'RM',
        'postal_code' => '1234567',
        'country' => 'CL',
    ]);

    expect($client->full_address)->toBe('Av. Test 123, Santiago, RM, 1234567, CL');
});

test('puede filtrar clientes activos', function () {
    Customer::factory()->count(3)->create(['is_active' => true]);
    Customer::factory()->count(2)->inactive()->create();

    $activeClients = Customer::active()->get();

    expect($activeClients)->toHaveCount(3);
    expect($activeClients->every(fn ($client) => $client->is_active))->toBeTrue();
});

test('puede buscar clientes por nombre o empresa', function () {
    Customer::factory()->create(['name' => 'TechCorp Solutions', 'company' => 'TechCorp']);
    Customer::factory()->create(['name' => 'Juan Pérez', 'company' => null]);
    Customer::factory()->create(['name' => 'Otra Empresa', 'company' => 'Otra Corp']);

    $results = Customer::search('TechCorp')->get();

    expect($results)->toHaveCount(1);
    expect($results->first()->name)->toBe('TechCorp Solutions');
});

test('puede filtrar clientes por país', function () {
    Customer::factory()->count(3)->chilean()->create();
    Customer::factory()->count(2)->argentine()->create();

    $chileanClients = Customer::byCountry('CL')->get();

    expect($chileanClients)->toHaveCount(3);
    expect($chileanClients->every(fn ($client) => $client->country === 'CL'))->toBeTrue();
});

test('puede relacionar cliente con cotizaciones', function () {
    $client = Customer::factory()->create();

    $quote = CustomerQuote::create([
        'quote_number' => 'COT-2025-001',
        'customer_id' => $client->id,
        'customer_name' => $client->name,
        'currency' => 'USD',
        'country' => $client->country,
        'valid_until' => now()->addDays(30),
        'status' => 'borrador',
    ]);

    expect($client->quotes)->toHaveCount(1);
    expect($client->quotes_count)->toBe(1);
    expect($client->hasQuotes())->toBeTrue();
    expect($client->last_quote->quote_number)->toBe('COT-2025-001');
});

test('puede obtener información del cliente desde la cotización', function () {
    $client = Customer::factory()->create([
        'name' => 'Cliente Test',
        'company' => 'Empresa Test',
        'country' => 'CL',
    ]);

    $quote = CustomerQuote::create([
        'quote_number' => 'COT-2025-002',
        'customer_id' => $client->id,
        'customer_name' => $client->name,
        'currency' => 'USD',
        'country' => 'AR', // Diferente al cliente
        'valid_until' => now()->addDays(30),
        'status' => 'borrador',
    ]);

    expect($quote->customer_name)->toBe('Cliente Test');
    expect($quote->customer_full_name)->toBe('Cliente Test (Empresa Test)');
    expect($quote->customer_country)->toBe('CL'); // Del cliente, no de la cotización
});
