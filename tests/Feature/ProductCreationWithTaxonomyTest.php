<?php

use Database\Seeders\ProductTaxonomySeeder;

beforeEach(function () {
    $this->seed(ProductTaxonomySeeder::class);
});

it('creates a local product with type and snapshots; rejects invalid attributes', function () {
    // Quote
    $quote = $this->postJson('/api/customer-quotes', [
        'customer_name' => 'Empresa X',
        'currency' => 'CLP',
    ])->assertCreated()->json();

    $type = \App\Models\ProductType::query()->where('code', 'TERMO-500ML-ACERO')->first();
    expect($type)->not->toBeNull();

    // Success: includes required attributes (material in set, capacidad=500)
    $res = $this->postJson("/api/customer-quotes/{$quote['id']}/products", [
        'type_id' => $type->id,
        'name' => 'Termo Cliente',
        'attributes' => ['material' => 'Acero 304', 'capacidad' => 500],
        'specs' => [],
        'hs_code' => $type->hs_code_sugerido,
        'position' => 1,
    ])->assertCreated();

    $created = $res->json();
    expect($created)
        ->toHaveKeys(['type_id', 'type_code', 'type_name', 'subcategory_code', 'category_code', 'name', 'attributes'])
        ->and($created['type_code'])->toBe('TERMO-500ML-ACERO');

    // Fail: missing required attribute
    $this->postJson("/api/customer-quotes/{$quote['id']}/products", [
        'type_id' => $type->id,
        'name' => 'Termo Incompleto',
        'attributes' => ['material' => 'Acero 304'],
    ])->assertUnprocessable()->assertJsonValidationErrors(['attributes.capacidad']);
});

it('rejects group_id from another quote', function () {
    $quote1 = $this->postJson('/api/customer-quotes', [
        'customer_name' => 'Q1',
        'currency' => 'CLP',
    ])->assertCreated()->json();
    $quote2 = $this->postJson('/api/customer-quotes', [
        'customer_name' => 'Q2',
        'currency' => 'CLP',
    ])->assertCreated()->json();
    $group = $this->postJson("/api/customer-quotes/{$quote2['id']}/groups", [
        'name' => 'Grupo Q2',
    ])->assertCreated()->json();

    $typeId = \App\Models\ProductType::query()->value('id');
    $this->postJson("/api/customer-quotes/{$quote1['id']}/products", [
        'group_id' => $group['id'],
        'type_id' => $typeId,
        'name' => 'Producto',
        'attributes' => ['capacidad' => 500, 'interfaz' => 'USB-A', 'material' => 'Acero 201'],
    ])->assertUnprocessable()->assertJsonValidationErrors(['group_id']);
});
