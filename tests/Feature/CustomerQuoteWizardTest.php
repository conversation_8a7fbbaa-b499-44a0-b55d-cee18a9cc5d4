<?php

declare(strict_types=1);

use App\Models\Customer;
use App\Models\CustomerQuote;
use App\Models\User;

test('wizard page is accessible', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user)
        ->get('/admin/customer-quotes/wizard')
        ->assertSuccessful();
});

test('wizard can create a basic quote', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');
    $customer = Customer::factory()->create([
        'is_active' => true,
    ]);

    $this->actingAs($user)
        ->post('/admin/customer-quotes/wizard', [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'quote_number' => 'COT-000001',
            'currency' => 'USD',
            'valid_until' => now()->addDays(30)->format('Y-m-d'),
            'country' => 'CL',
            'notes' => 'Cotización de prueba',
            'is_draft' => true,
        ])
        ->assertRedirect();

    expect(CustomerQuote::where('quote_number', 'COT-000001')->exists())->toBeTrue();
});

test('wizard validates required fields', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user)
        ->post('/admin/customer-quotes/wizard', [])
        ->assertSessionHasErrors([
            'customer_id',
            'quote_number',
            'currency',
            'valid_until',
            'country',
        ]);
});

test('wizard validates unique quote number', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');
    $customer = Customer::factory()->create(['is_active' => true]);

    // Crear una cotización existente
    CustomerQuote::factory()->create([
        'quote_number' => 'COT-000001',
    ]);

    $this->actingAs($user)
        ->post('/admin/customer-quotes/wizard', [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'quote_number' => 'COT-000001', // Número duplicado
            'currency' => 'USD',
            'valid_until' => now()->addDays(30)->format('Y-m-d'),
            'country' => 'CL',
            'is_draft' => true,
        ])
        ->assertSessionHasErrors(['quote_number']);
});

test('wizard sets correct status based on draft flag', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');
    $customer = Customer::factory()->create(['is_active' => true]);

    // Test draft quote
    $this->actingAs($user)
        ->post('/admin/customer-quotes/wizard', [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'quote_number' => 'COT-000001',
            'currency' => 'USD',
            'valid_until' => now()->addDays(30)->format('Y-m-d'),
            'country' => 'CL',
            'is_draft' => true,
        ]);

    $quote = CustomerQuote::where('quote_number', 'COT-000001')->first();
    expect($quote->status->value)->toBe('borrador');

    // Test non-draft quote
    $this->actingAs($user)
        ->post('/admin/customer-quotes/wizard', [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'quote_number' => 'COT-000002',
            'currency' => 'USD',
            'valid_until' => now()->addDays(30)->format('Y-m-d'),
            'country' => 'CL',
            'is_draft' => false,
        ]);

    $quote = CustomerQuote::where('quote_number', 'COT-000002')->first();
    expect($quote->status->value)->toBe('enviada');
});

test('wizard populates customer data correctly', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');
    $customer = Customer::factory()->create([
        'is_active' => true,
        'name' => 'Cliente Test',
        'country' => 'CL',
    ]);

    $this->actingAs($user)
        ->post('/admin/customer-quotes/wizard', [
            'customer_id' => $customer->id,
            'customer_name' => $customer->name,
            'quote_number' => 'COT-000001',
            'currency' => 'USD',
            'valid_until' => now()->addDays(30)->format('Y-m-d'),
            'country' => 'CL',
            'is_draft' => true,
        ]);

    $quote = CustomerQuote::where('quote_number', 'COT-000001')->first();
    expect($quote->customer_id)->toBe($customer->id);
    expect($quote->customer_name)->toBe($customer->name);
    expect($quote->country)->toBe($customer->country);
});
