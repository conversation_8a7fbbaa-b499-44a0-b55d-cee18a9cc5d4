<?php

declare(strict_types=1);

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteProduct;
use App\Models\CustomerQuoteProductVariant;
use App\Models\User;
use Database\Seeders\ProductTaxonomySeeder;
use Database\Seeders\RolesSeeder;

beforeEach(function () {
    $this->seed([
        RolesSeeder::class,
        ProductTaxonomySeeder::class,
    ]);
});

test('puede ver la lista de variantes de productos', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);
    $variant = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
    ]);

    $this->actingAs($user)
        ->get('/admin/customer-quote-product-variants')
        ->assertSuccessful()
        ->assertSee($variant->label)
        ->assertSee($product->name)
        ->assertSee($quote->quote_number);
});

test('puede ver los detalles de una variante de producto', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);
    $variant = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
    ]);

    $this->actingAs($user)
        ->get("/admin/customer-quote-product-variants/{$variant->id}")
        ->assertSuccessful()
        ->assertSee($variant->label)
        ->assertSee($product->name)
        ->assertSee($quote->quote_number);
});

test('puede crear una nueva variante de producto', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);

    $this->actingAs($user)
        ->get('/admin/customer-quote-product-variants/create')
        ->assertSuccessful();
});

test('puede editar una variante de producto', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);
    $variant = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
    ]);

    $this->actingAs($user)
        ->get("/admin/customer-quote-product-variants/{$variant->id}/edit")
        ->assertSuccessful()
        ->assertSee($variant->label);
});

test('calcula correctamente el total de una variante', function () {
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);
    $variant = CustomerQuoteProductVariant::factory()->withPrice()->create([
        'quote_product_id' => $product->id,
        'quantity' => 10,
        'unit_price_minor' => 1000, // $10
    ]);

    // 10 unidades × $10 = $100
    expect($variant->totalAmount())->toBe(100.0);
});

test('calcula correctamente el total con tipo de cambio', function () {
    $quote = CustomerQuote::factory()->create(['currency' => 'EUR']);
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);
    $variant = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 5,
        'unit_price_minor' => 1000, // $10 USD
        'price_currency' => 'USD',
        'fx_rate_to_quote' => 0.85, // 1 USD = 0.85 EUR
    ]);

    // 5 unidades × $10 × 0.85 = $42.5 EUR
    expect($variant->totalAmount())->toBe(42.5);
});

test('formatea correctamente el dinero', function () {
    $quote = CustomerQuote::factory()->create(['currency' => 'USD']);
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);
    $variant = CustomerQuoteProductVariant::factory()->withPrice()->create([
        'quote_product_id' => $product->id,
        'unit_price_minor' => 1500, // $15
    ]);

    $formatted = $variant->money('unit_price');
    expect($formatted)->toContain('15');
});

test('maneja correctamente variantes sin precio', function () {
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);
    $variant = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 5,
        'unit_price_minor' => null,
    ]);

    // Sin precio, debe retornar 0
    expect($variant->totalAmount())->toBe(0.0);
});

test('maneja correctamente variantes con cantidad cero', function () {
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => 1,
    ]);
    $variant = CustomerQuoteProductVariant::factory()->withPrice()->create([
        'quote_product_id' => $product->id,
        'quantity' => 0,
        'unit_price_minor' => 1000, // $10
    ]);

    // 0 unidades × $10 = $0
    expect($variant->totalAmount())->toBe(0.0);
});
