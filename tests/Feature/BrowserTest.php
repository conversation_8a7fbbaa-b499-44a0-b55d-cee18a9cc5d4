<?php

declare(strict_types=1);

use App\Models\Customer;
use App\Models\User;
use Livewire\Livewire;

test('wizard page loads correctly and icons are properly sized', function () {
    // Crear usuario sin roles (para evitar problemas de permisos)
    $user = User::factory()->create();
    
    // Crear algunos clientes para el test
    Customer::factory()->count(3)->create(['is_active' => true]);

    $this->actingAs($user);

    // Visitar la página del wizard
    $page = visit('/admin/customer-quotes/wizard');
    
    // Verificar que la página carga correctamente
    $page->assertSee('Cliente')
         ->assertSee('Selecciona o crea un cliente para la cotización')
         ->assertNoJavascriptErrors()
         ->assertNoConsoleLogs();

    // Tomar screenshot para debugging
    $page->screenshot('wizard-step1');

    // Avanzar al paso 2
    $page->click('Siguiente')
         ->assertSee('Configuración')
         ->assertSee('Define la información básica de la cotización')
         ->screenshot('wizard-step2');

    // Avanzar al paso 3
    $page->click('Siguiente')
         ->assertSee('Productos')
         ->assertSee('Selecciona los productos para incluir en la cotización')
         ->screenshot('wizard-step3');

    // Verificar que los iconos tienen el tamaño correcto
    $page->assertSee('Buscar productos por nombre o código...')
         ->assertSee('productos seleccionados');

    // Avanzar al paso 4
    $page->click('Siguiente')
         ->assertSee('Variantes')
         ->assertSee('Configuración de Variantes')
         ->screenshot('wizard-step4');
});

test('wizard icons have correct CSS classes applied', function () {
    $user = User::factory()->create();
    
    Customer::factory()->count(2)->create(['is_active' => true]);

    $this->actingAs($user);

    $page = visit('/admin/customer-quotes/wizard');
    
    // Navegar al paso 3 donde están los iconos problemáticos
    $page->click('Siguiente') // Paso 2
         ->click('Siguiente'); // Paso 3

    // Verificar que los elementos tienen las clases CSS correctas
    $page->assertSee('Buscar productos por nombre o código...');
    
    // Verificar que los iconos SVG tienen las clases de tamaño correctas
    // Esto nos ayudará a debuggear si las clases se están aplicando
    $page->assertSee('productos seleccionados');
    
    // Tomar screenshot detallado del paso 3
    $page->screenshot('wizard-step3-detailed');
    
    // Pausar para inspección manual si es necesario
    // $page->pause();
});

test('wizard CSS is loaded correctly', function () {
    $user = User::factory()->create();

    $this->actingAs($user);

    $page = visit('/admin/customer-quotes/wizard');
    
    // Verificar que los estilos CSS se están cargando
    $page->assertSee('Cliente');
    
    // Navegar al paso 3
    $page->click('Siguiente')
         ->click('Siguiente');
    
    // Verificar que las clases CSS están presentes en el DOM
    $page->assertSee('product-selection')
         ->assertSee('w-3')
         ->assertSee('h-3');
    
    // Tomar screenshot para verificar el estado visual
    $page->screenshot('wizard-css-debug');
});