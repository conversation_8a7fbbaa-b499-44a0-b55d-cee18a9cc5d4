<?php

use App\Models\Customer;

describe('Customer validation', function () {
    test('validates required fields', function (array $data, string $expectedError) {
        $customer = new Customer;

        expect(fn () => $customer->forceFill($data)->save())
            ->toThrow(\Illuminate\Database\QueryException::class);
    })->with([
        'missing name' => [['email' => '<EMAIL>'], 'name'],
        'invalid email format' => [['name' => 'Test', 'email' => 'invalid-email'], 'email'],
        'duplicate email' => [function () {
            Customer::factory()->create(['email' => '<EMAIL>']);

            return ['name' => 'Test', 'email' => '<EMAIL>'];
        }, 'email'],
    ]);

    test('accepts valid country codes', function (string $countryCode) {
        $customer = Customer::factory()->create(['country' => $countryCode]);

        expect($customer->country)->toBe($countryCode);
    })->with(['CL', 'AR', 'PE', 'CO', 'MX', 'US', null]);

    test('customer search works with different patterns', function (string $searchTerm, int $expectedCount) {
        Customer::factory()->create(['name' => 'TechCorp Solutions', 'company' => 'TechCorp']);
        Customer::factory()->create(['name' => 'Another Company', 'email' => '<EMAIL>']);
        Customer::factory()->create(['name' => 'Simple Name', 'company' => null]);

        $results = Customer::search($searchTerm)->get();

        expect($results)->toHaveCount($expectedCount);
    })->with([
        'exact name match' => ['TechCorp', 1],
        'partial name' => ['Tech', 2], // Matches name and email
        'company match' => ['Solutions', 1],
        'email match' => ['tech@', 1],
        'no match' => ['NonExistent', 0],
    ]);
});

describe('Customer scopes', function () {
    beforeEach(function () {
        Customer::factory()->count(3)->create(['is_active' => true, 'country' => 'CL']);
        Customer::factory()->count(2)->create(['is_active' => false, 'country' => 'CL']);
        Customer::factory()->count(1)->create(['is_active' => true, 'country' => 'AR']);
    });

    test('active scope filters correctly', function () {
        $activeCustomers = Customer::active()->get();

        expect($activeCustomers)->toHaveCount(4);
        expect($activeCustomers->every(fn ($customer) => $customer->is_active))->toBeTrue();
    });

    test('country scope filters correctly', function () {
        $chileanCustomers = Customer::byCountry('CL')->get();
        $argentineCustomers = Customer::byCountry('AR')->get();

        expect($chileanCustomers)->toHaveCount(5);
        expect($argentineCustomers)->toHaveCount(1);
        expect($chileanCustomers->every(fn ($c) => $c->country === 'CL'))->toBeTrue();
    });

    test('combined scopes work together', function () {
        $activeChileanCustomers = Customer::active()->byCountry('CL')->get();

        expect($activeChileanCustomers)->toHaveCount(3);
        expect($activeChileanCustomers->every(fn ($c) => $c->is_active && $c->country === 'CL'))->toBeTrue();
    });
});

describe('Customer attributes', function () {
    test('full name formats correctly', function (string $name, ?string $company, string $expected) {
        $customer = Customer::factory()->create(['name' => $name, 'company' => $company]);

        expect($customer->full_name)->toBe($expected);
    })->with([
        'individual' => ['John Doe', null, 'John Doe'],
        'with company' => ['Jane Smith', 'ACME Corp', 'Jane Smith (ACME Corp)'],
        'company same as name' => ['ACME Corp', 'ACME Corp', 'ACME Corp (ACME Corp)'],
    ]);

    test('full address formats correctly', function () {
        $customer = Customer::factory()->create([
            'address' => 'Av. Test 123',
            'city' => 'Santiago',
            'state' => 'RM',
            'postal_code' => '1234567',
            'country' => 'CL',
        ]);

        expect($customer->full_address)->toBe('Av. Test 123, Santiago, RM, 1234567, CL');
    });

    test('handles partial address data', function () {
        $customer = Customer::factory()->create([
            'address' => 'Street 123',
            'city' => 'City',
            'state' => null,
            'postal_code' => null,
            'country' => 'US',
        ]);

        expect($customer->full_address)->toBe('Street 123, City, US');
    });
});
