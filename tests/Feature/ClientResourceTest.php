<?php

use App\Models\Customer;
use App\Models\User;

test('puede acceder a la lista de clientes', function () {
    $user = User::factory()->create();
    // Simular que el usuario puede acceder al panel
    $this->actingAs($user);

    $response = $this->get('/admin/customers');

    // Verificar que la respuesta sea exitosa, redirija o prohibida (dependiendo de la configuración de permisos)
    expect($response->status())->toBeIn([200, 302, 403]);
});

test('puede crear un nuevo cliente directamente', function () {
    $clientData = [
        'name' => 'Cliente Test',
        'email' => '<EMAIL>',
        'phone' => '+56 9 1234 5678',
        'company' => 'Empresa Test',
        'country' => 'CL',
        'city' => 'Santiago',
        'is_active' => true,
    ];

    $client = Customer::create($clientData);

    expect($client->name)->toBe('Cliente Test');
    expect($client->email)->toBe('<EMAIL>');
    expect($client->country)->toBe('CL');
});

test('puede ver los detalles de un cliente', function () {
    $client = Customer::factory()->individual()->create([
        'name' => 'Cliente Visible',
        'email' => '<EMAIL>',
    ]);

    expect($client->name)->toBe('Cliente Visible');
    expect($client->email)->toBe('<EMAIL>');
    expect($client->full_name)->toBe('Cliente Visible');
});

test('puede editar un cliente existente', function () {
    $client = Customer::factory()->create([
        'name' => 'Cliente Original',
        'email' => '<EMAIL>',
    ]);

    $client->update([
        'name' => 'Cliente Actualizado',
        'email' => '<EMAIL>',
        'phone' => '+56 9 8765 4321',
        'company' => 'Empresa Actualizada',
        'country' => 'AR',
        'city' => 'Buenos Aires',
    ]);

    $client->refresh();
    expect($client->name)->toBe('Cliente Actualizado');
    expect($client->email)->toBe('<EMAIL>');
    expect($client->country)->toBe('AR');
});

test('puede filtrar clientes por país usando scopes', function () {
    Customer::factory()->count(3)->chilean()->create();
    Customer::factory()->count(2)->argentine()->create();

    $chileanClients = Customer::byCountry('CL')->get();
    $argentineClients = Customer::byCountry('AR')->get();

    expect($chileanClients)->toHaveCount(3);
    expect($argentineClients)->toHaveCount(2);
    expect($chileanClients->every(fn ($client) => $client->country === 'CL'))->toBeTrue();
});

test('puede buscar clientes por nombre usando scopes', function () {
    Customer::factory()->create(['name' => 'TechCorp Solutions']);
    Customer::factory()->create(['name' => 'Otra Empresa']);

    $results = Customer::search('TechCorp')->get();

    expect($results)->toHaveCount(1);
    expect($results->first()->name)->toBe('TechCorp Solutions');
});

test('puede filtrar clientes activos e inactivos usando scopes', function () {
    Customer::factory()->count(3)->create(['is_active' => true]);
    Customer::factory()->count(2)->inactive()->create();

    $activeClients = Customer::active()->get();
    $allClients = Customer::all();

    expect($activeClients)->toHaveCount(3);
    expect($allClients)->toHaveCount(5);
    expect($activeClients->every(fn ($client) => $client->is_active))->toBeTrue();
});
