<?php

declare(strict_types=1);

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductType;
use App\Models\User;
use Database\Seeders\RolesSeeder;

beforeEach(function () {
    $this->seed(RolesSeeder::class);
});

test('ProductTypeResource permite CRUD completo', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear datos necesarios
    $category = ProductCategory::factory()->create();
    $subcategory = ProductSubcategory::factory()->create(['category_id' => $category->id]);

    // Verificar que se puede acceder a la lista
    $this->get('/admin/product-types')
        ->assertSuccessful();

    // Verificar que se puede acceder al formulario de creación
    $this->get('/admin/product-types/create')
        ->assertSuccessful();

    // Crear un tipo de producto
    $type = ProductType::factory()->create([
        'subcategory_id' => $subcategory->id,
        'code' => 'TEST',
        'name' => 'Test Product Type',
        'hs_code_sugerido' => '1234.56.78',
        'defaults' => ['test' => 'value'],
        'reglas' => ['rule' => 'value'],
    ]);

    // Verificar que se puede ver el detalle
    $this->get("/admin/product-types/{$type->id}")
        ->assertSuccessful()
        ->assertSee($type->name)
        ->assertSee($type->code);

    // Verificar que se puede editar
    $this->get("/admin/product-types/{$type->id}/edit")
        ->assertSuccessful()
        ->assertSee($type->name);
});

test('ProductCategoryResource permite CRUD completo', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Verificar que se puede acceder a la lista
    $this->get('/admin/product-categories')
        ->assertSuccessful();

    // Verificar que se puede acceder al formulario de creación
    $this->get('/admin/product-categories/create')
        ->assertSuccessful();

    // Crear una categoría
    $category = ProductCategory::factory()->create([
        'code' => 'TEST',
        'name' => 'Test Category',
        'metadata' => ['test' => 'value'],
    ]);

    // Verificar que se puede ver el detalle
    $this->get("/admin/product-categories/{$category->id}")
        ->assertSuccessful()
        ->assertSee($category->name)
        ->assertSee($category->code);

    // Verificar que se puede editar
    $this->get("/admin/product-categories/{$category->id}/edit")
        ->assertSuccessful()
        ->assertSee($category->name);
});

test('ProductSubcategoryResource permite CRUD completo', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear categoría necesaria
    $category = ProductCategory::factory()->create();

    // Verificar que se puede acceder a la lista
    $this->get('/admin/product-subcategories')
        ->assertSuccessful();

    // Verificar que se puede acceder al formulario de creación
    $this->get('/admin/product-subcategories/create')
        ->assertSuccessful();

    // Crear una subcategoría
    $subcategory = ProductSubcategory::factory()->create([
        'category_id' => $category->id,
        'code' => 'TEST',
        'name' => 'Test Subcategory',
        'metadata' => ['test' => 'value'],
    ]);

    // Verificar que se puede ver el detalle
    $this->get("/admin/product-subcategories/{$subcategory->id}")
        ->assertSuccessful()
        ->assertSee($subcategory->name)
        ->assertSee($subcategory->code);

    // Verificar que se puede editar
    $this->get("/admin/product-subcategories/{$subcategory->id}/edit")
        ->assertSuccessful()
        ->assertSee($subcategory->name);
});

test('los recursos muestran las relaciones correctamente en las tablas', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear estructura jerárquica
    $category = ProductCategory::factory()->create(['name' => 'Test Category']);
    $subcategory = ProductSubcategory::factory()->create([
        'category_id' => $category->id,
        'name' => 'Test Subcategory',
    ]);
    $type = ProductType::factory()->create([
        'subcategory_id' => $subcategory->id,
        'name' => 'Test Type',
    ]);

    // Verificar que las tablas muestran las relaciones
    $this->get('/admin/product-types')
        ->assertSuccessful()
        ->assertSee($type->name)
        ->assertSee($subcategory->name)
        ->assertSee($category->name);

    $this->get('/admin/product-subcategories')
        ->assertSuccessful()
        ->assertSee($subcategory->name)
        ->assertSee($category->name);

    $this->get('/admin/product-categories')
        ->assertSuccessful()
        ->assertSee($category->name);
});

test('los filtros de tabla funcionan correctamente', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear datos de prueba
    $category1 = ProductCategory::factory()->create(['name' => 'Category 1']);
    $category2 = ProductCategory::factory()->create(['name' => 'Category 2']);

    $subcategory1 = ProductSubcategory::factory()->create(['category_id' => $category1->id]);
    $subcategory2 = ProductSubcategory::factory()->create(['category_id' => $category2->id]);

    $type1 = ProductType::factory()->create(['subcategory_id' => $subcategory1->id]);
    $type2 = ProductType::factory()->create(['subcategory_id' => $subcategory2->id]);

    // Verificar que se pueden filtrar por categoría
    $this->get('/admin/product-subcategories')
        ->assertSuccessful()
        ->assertSee($subcategory1->name)
        ->assertSee($subcategory2->name);

    // Verificar que se pueden filtrar por subcategoría
    $this->get('/admin/product-types')
        ->assertSuccessful()
        ->assertSee($type1->name)
        ->assertSee($type2->name);
});
