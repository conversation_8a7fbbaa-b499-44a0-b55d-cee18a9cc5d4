<?php

declare(strict_types=1);

use App\Filament\Pages\QuoteWizard;
use App\Models\User;
use Livewire\Livewire;

beforeEach(function () {
    $this->seed(\Database\Seeders\RolesSeeder::class);
});

test('quote wizard page can be loaded', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    Livewire::actingAs($user)
        ->test(QuoteWizard::class)
        ->assertOk()
        ->assertSee('Crear Cotización')
        ->assertSee('Cliente');
});

test('quote wizard can navigate between steps', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    Livewire::actingAs($user)
        ->test(QuoteWizard::class)
        ->goToNextWizardStep()
        ->assertWizardCurrentStep(2)
        ->assertSee('Configuración')
        ->goToNextWizardStep()
        ->assertWizardCurrentStep(3)
        ->assertSee('Productos')
        ->goToPreviousWizardStep()
        ->assertWizardCurrentStep(2);
});

test('quote wizard can go to specific step', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    Livewire::actingAs($user)
        ->test(QuoteWizard::class)
        ->goToWizardStep(3)
        ->assertWizardCurrentStep(3)
        ->assertSee('Productos')
        ->goToWizardStep(1)
        ->assertWizardCurrentStep(1)
        ->assertSee('Cliente');
});

        test('quote wizard shows product selection correctly', function () {
            $user = User::factory()->create();
            $user->assignRole('admin');

            Livewire::actingAs($user)
                ->test(QuoteWizard::class)
                ->goToWizardStep(3)
                ->assertSee('Productos y Variantes')
                ->assertSee('Agregar Kit')
                ->assertSee('Agregar Producto');
        });
