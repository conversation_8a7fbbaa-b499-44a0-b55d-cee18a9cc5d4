<?php

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteProduct;
use App\Models\CustomerQuoteProductVariant;
use App\Models\ProductionBatch;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Supplier;

test('puede obtener resumen de reconciliación', function () {
    $quote = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();

    $product = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote->id]);
    $variant = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 200,
    ]);

    $purchaseOrder = PurchaseOrder::factory()->create([
        'quote_id' => $quote->id,
        'supplier_id' => $supplier->id,
    ]);

    $poItem = PurchaseOrderItem::factory()->create([
        'purchase_order_id' => $purchaseOrder->id,
        'quote_product_variant_id' => $variant->id,
        'quantity' => 200,
    ]);

    $batch = ProductionBatch::factory()->create([
        'purchase_order_item_id' => $poItem->id,
        'quantity' => 200,
    ]);

    $response = $this->getJson("/api/customer-quotes/{$quote->id}/sourcing/summary");

    $response->assertSuccessful()
        ->assertJsonStructure([
            'quote_id',
            'quote_number',
            'summary' => [
                'total_variants',
                'reconciled_variants',
                'unreconciled_variants',
                'total_po_quantity',
                'total_batch_quantity',
                'is_fully_reconciled',
            ],
            'variants' => [
                '*' => [
                    'variant_id',
                    'variant_name',
                    'variant_quantity',
                    'po_quantity',
                    'batch_quantity',
                    'po_difference',
                    'batch_difference',
                    'is_reconciled',
                    'po_items',
                    'batches',
                ],
            ],
        ]);

    $responseData = $response->json();
    expect($responseData['summary']['total_variants'])->toBe(1);
    expect($responseData['summary']['reconciled_variants'])->toBe(1);
    expect($responseData['summary']['is_fully_reconciled'])->toBeTrue();
    expect($responseData['variants'][0]['is_reconciled'])->toBeTrue();
});

test('detecta variantes no reconciliadas', function () {
    $quote = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();

    $product = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote->id]);
    $variant = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 200,
    ]);

    $purchaseOrder = PurchaseOrder::factory()->create([
        'quote_id' => $quote->id,
        'supplier_id' => $supplier->id,
    ]);

    // Solo asignar 150 de 200 (faltan 50)
    $poItem = PurchaseOrderItem::factory()->create([
        'purchase_order_id' => $purchaseOrder->id,
        'quote_product_variant_id' => $variant->id,
        'quantity' => 150,
    ]);

    // Solo crear lote de 100 de 150 (faltan 50)
    $batch = ProductionBatch::factory()->create([
        'purchase_order_item_id' => $poItem->id,
        'quantity' => 100,
    ]);

    $response = $this->getJson("/api/customer-quotes/{$quote->id}/sourcing/summary");

    $response->assertSuccessful();

    $responseData = $response->json();
    expect($responseData['summary']['reconciled_variants'])->toBe(0);
    expect($responseData['summary']['is_fully_reconciled'])->toBeFalse();

    $variantData = $responseData['variants'][0];
    expect($variantData['is_reconciled'])->toBeFalse();
    expect($variantData['po_difference'])->toBe(-50); // Faltan 50 en OC
    expect($variantData['batch_difference'])->toBe(-50); // Faltan 50 en lotes
});

test('maneja múltiples variantes con diferentes estados de reconciliación', function () {
    $quote = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();

    $product = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote->id]);

    // Variante 1: Completamente reconciliada
    $variant1 = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 100,
    ]);

    // Variante 2: Parcialmente reconciliada
    $variant2 = CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 200,
    ]);

    $purchaseOrder = PurchaseOrder::factory()->create([
        'quote_id' => $quote->id,
        'supplier_id' => $supplier->id,
    ]);

    // OC para variante 1 (completa)
    $poItem1 = PurchaseOrderItem::factory()->create([
        'purchase_order_id' => $purchaseOrder->id,
        'quote_product_variant_id' => $variant1->id,
        'quantity' => 100,
    ]);

    $batch1 = ProductionBatch::factory()->create([
        'purchase_order_item_id' => $poItem1->id,
        'quantity' => 100,
    ]);

    // OC para variante 2 (parcial)
    $poItem2 = PurchaseOrderItem::factory()->create([
        'purchase_order_id' => $purchaseOrder->id,
        'quote_product_variant_id' => $variant2->id,
        'quantity' => 150, // Solo 150 de 200
    ]);

    $batch2 = ProductionBatch::factory()->create([
        'purchase_order_item_id' => $poItem2->id,
        'quantity' => 150,
    ]);

    $response = $this->getJson("/api/customer-quotes/{$quote->id}/sourcing/summary");

    $response->assertSuccessful();

    $responseData = $response->json();
    expect($responseData['summary']['total_variants'])->toBe(2);
    expect($responseData['summary']['reconciled_variants'])->toBe(1);
    expect($responseData['summary']['unreconciled_variants'])->toBe(1);
    expect($responseData['summary']['is_fully_reconciled'])->toBeFalse();
});

test('maneja cotización sin variantes', function () {
    $quote = CustomerQuote::factory()->create();

    $response = $this->getJson("/api/customer-quotes/{$quote->id}/sourcing/summary");

    $response->assertSuccessful();

    $responseData = $response->json();
    expect($responseData['summary']['total_variants'])->toBe(0);
    expect($responseData['summary']['reconciled_variants'])->toBe(0);
    expect($responseData['summary']['is_fully_reconciled'])->toBeTrue();
});
