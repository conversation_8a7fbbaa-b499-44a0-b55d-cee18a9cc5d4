<?php

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteProduct;
use App\Models\CustomerQuoteProductVariant;
use App\Models\PurchaseOrder;
use App\Models\Supplier;

test('puede crear una orden de compra', function () {
    $quote = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();

    $poData = [
        'supplier_id' => $supplier->id,
        'currency' => 'USD',
        'incoterm' => 'FOB',
        'terms' => [
            'payment' => '30/70',
            'remarks' => 'QA AQL 2.5',
        ],
        'notes' => 'Priorizar línea termo',
    ];

    $response = $this->postJson("/api/customer-quotes/{$quote->id}/purchase-orders", $poData);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'id',
            'quote_id',
            'supplier_id',
            'currency',
            'incoterm',
            'terms',
            'status',
            'correlation_id',
            'notes',
            'supplier',
            'quote',
        ]);

    $this->assertDatabaseHas('purchase_orders', [
        'quote_id' => $quote->id,
        'supplier_id' => $supplier->id,
        'currency' => 'USD',
        'incoterm' => 'FOB',
        'status' => 'borrador',
    ]);
});

test('puede agregar un ítem a una orden de compra', function () {
    $quote = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();
    $product = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote->id]);
    $variant = CustomerQuoteProductVariant::factory()->create(['quote_product_id' => $product->id]);

    $purchaseOrder = PurchaseOrder::factory()->create([
        'quote_id' => $quote->id,
        'supplier_id' => $supplier->id,
    ]);

    $itemData = [
        'quote_product_variant_id' => $variant->id,
        'quantity' => 200,
        'unit_price' => 1.85,
        'lead_time_days' => 18,
        'notes' => 'Color negro mate',
    ];

    $response = $this->postJson("/api/purchase-orders/{$purchaseOrder->id}/items", $itemData);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'id',
            'purchase_order_id',
            'quote_product_variant_id',
            'quantity',
            'unit_price',
            'lead_time_days',
            'correlation_id',
            'notes',
            'quote_product_variant',
        ]);

    $this->assertDatabaseHas('purchase_order_items', [
        'purchase_order_id' => $purchaseOrder->id,
        'quote_product_variant_id' => $variant->id,
        'quantity' => 200,
        'unit_price' => 1.85,
    ]);
});

test('puede enviar una orden de compra', function () {
    $purchaseOrder = PurchaseOrder::factory()->create(['status' => 'borrador']);

    $response = $this->postJson("/api/purchase-orders/{$purchaseOrder->id}/send");

    $response->assertSuccessful()
        ->assertJsonFragment(['status' => 'enviada']);

    $this->assertDatabaseHas('purchase_orders', [
        'id' => $purchaseOrder->id,
        'status' => 'enviada',
    ]);
});

test('puede confirmar una orden de compra', function () {
    $purchaseOrder = PurchaseOrder::factory()->create(['status' => 'enviada']);

    $response = $this->postJson("/api/purchase-orders/{$purchaseOrder->id}/confirm");

    $response->assertSuccessful()
        ->assertJsonFragment(['status' => 'confirmada']);

    $this->assertDatabaseHas('purchase_orders', [
        'id' => $purchaseOrder->id,
        'status' => 'confirmada',
    ]);
});

test('puede cerrar una orden de compra', function () {
    $purchaseOrder = PurchaseOrder::factory()->create(['status' => 'confirmada']);

    $response = $this->postJson("/api/purchase-orders/{$purchaseOrder->id}/close");

    $response->assertSuccessful()
        ->assertJsonFragment(['status' => 'cerrada']);

    $this->assertDatabaseHas('purchase_orders', [
        'id' => $purchaseOrder->id,
        'status' => 'cerrada',
    ]);
});

test('no puede enviar una orden que no está en borrador', function () {
    $purchaseOrder = PurchaseOrder::factory()->create(['status' => 'enviada']);

    $response = $this->postJson("/api/purchase-orders/{$purchaseOrder->id}/send");

    $response->assertUnprocessable()
        ->assertJsonFragment(['error' => 'Solo se pueden enviar órdenes en estado borrador.']);
});

test('no puede cerrar una orden que no está confirmada', function () {
    $purchaseOrder = PurchaseOrder::factory()->create(['status' => 'enviada']);

    $response = $this->postJson("/api/purchase-orders/{$purchaseOrder->id}/close");

    $response->assertUnprocessable()
        ->assertJsonFragment(['error' => 'Solo se pueden cerrar órdenes confirmadas.']);
});

test('valida que la variante pertenezca a la cotización de la orden', function () {
    $quote1 = CustomerQuote::factory()->create();
    $quote2 = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();

    $product1 = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote1->id]);
    $product2 = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote2->id]);
    $variant1 = CustomerQuoteProductVariant::factory()->create(['quote_product_id' => $product1->id]);
    $variant2 = CustomerQuoteProductVariant::factory()->create(['quote_product_id' => $product2->id]);

    $purchaseOrder = PurchaseOrder::factory()->create([
        'quote_id' => $quote1->id,
        'supplier_id' => $supplier->id,
    ]);

    $itemData = [
        'quote_product_variant_id' => $variant2->id, // Variante de otra cotización
        'quantity' => 100,
    ];

    $response = $this->postJson("/api/purchase-orders/{$purchaseOrder->id}/items", $itemData);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['quote_product_variant_id']);
});
