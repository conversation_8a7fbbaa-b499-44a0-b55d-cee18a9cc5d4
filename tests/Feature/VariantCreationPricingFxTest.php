<?php

use Database\Seeders\ProductTaxonomySeeder;

beforeEach(function () {
    $this->seed(ProductTaxonomySeeder::class);
});

function createQuoteAndProduct(string $currency = 'CLP'): array
{
    $quote = test()->postJson('/api/customer-quotes', [
        'customer_name' => 'Empresa FX',
        'currency' => $currency,
    ])->assertCreated()->json();

    $type = \App\Models\ProductType::query()->first();
    $product = test()->postJson("/api/customer-quotes/{$quote['id']}/products", [
        'type_id' => $type->id,
        'name' => 'Prod FX',
        'attributes' => ['capacidad' => 500, 'interfaz' => 'USB-A', 'material' => 'Acero 304'],
    ])->assertCreated()->json();

    return [$quote, $product];
}

it('creates variant with same currency autocompleting fx=1', function () {
    [$quote, $product] = createQuoteAndProduct('CLP');

    $var = $this->postJson("/api/customer-quote-products/{$product['id']}/variants", [
        'label' => 'Local CLP',
        'attributes' => ['color' => 'Negro'],
        'quantity' => 5,
        'price_currency' => 'CLP',
        'unit_price_minor' => 2500,
    ])->assertCreated()->json();

    expect((float) $var['fx_rate_to_quote'])->toBe(1.0);
});

it('requires fx when price currency differs; accepts unit_price string', function () {
    [$quote, $product] = createQuoteAndProduct('CLP');

    // Missing fx
    $this->postJson("/api/customer-quote-products/{$product['id']}/variants", [
        'label' => 'USD sin FX',
        'attributes' => ['color' => 'Blanco'],
        'quantity' => 2,
        'price_currency' => 'USD',
        'unit_price' => '12.50',
    ])->assertUnprocessable()->assertJsonValidationErrors(['fx_rate_to_quote']);

    // With fx
    $ok = $this->postJson("/api/customer-quote-products/{$product['id']}/variants", [
        'label' => 'USD con FX',
        'attributes' => ['color' => 'Blanco'],
        'quantity' => 2,
        'price_currency' => 'USD',
        'unit_price' => '12.50',
        'fx_rate_to_quote' => 900.0,
    ])->assertCreated()->json();

    expect($ok['unit_price_minor'])->toBe(1250) // 12.50 USD → 1250 minor
        ->and($ok['price_currency'])->toBe('USD')
        ->and((float) $ok['fx_rate_to_quote'])->toBe(900.0);
});
