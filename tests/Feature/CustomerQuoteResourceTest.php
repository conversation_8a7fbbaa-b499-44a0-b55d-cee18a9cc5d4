<?php

declare(strict_types=1);

use App\Models\{Customer, CustomerQuote, User};

test('customer quote model has correct customer relationship', function () {
    $customer = Customer::factory()->create(['name' => 'Test Customer']);
    $quote = CustomerQuote::factory()->create([
        'customer_id' => $customer->id,
        'customer_name' => 'Test Customer Name',
    ]);

    // Verificar que la relación customer funciona
    expect($quote->customer)->not->toBeNull();
    expect($quote->customer->name)->toBe('Test Customer');
    
    // Verificar que el atributo customer_name funciona
    expect($quote->customer_name)->toBe('Test Customer Name');
});

test('customer quote table can be rendered without errors', function () {
    $user = User::factory()->create();
    
    // Crear datos de prueba
    $customer = Customer::factory()->create();
    $quote = CustomerQuote::factory()->create([
        'customer_id' => $customer->id,
        'quote_number' => 'TEST-001',
        'status' => 'borrador',
    ]);

    // Verificar que podemos acceder a la página sin errores de relación
    $response = $this->actingAs($user)
        ->get('/admin/customer-quotes');
    
    // No debería haber errores 500 por relaciones incorrectas
    expect($response->status())->not->toBe(500);
});

test('customer quote filters work with customer relationship', function () {
    $customer1 = Customer::factory()->create(['name' => 'Customer 1']);
    $customer2 = Customer::factory()->create(['name' => 'Customer 2']);
    
    $quote1 = CustomerQuote::factory()->create(['customer_id' => $customer1->id]);
    $quote2 = CustomerQuote::factory()->create(['customer_id' => $customer2->id]);
    $quote3 = CustomerQuote::factory()->create(['customer_id' => null]);

    // Verificar que podemos filtrar por customer_id
    $quotesWithCustomer1 = CustomerQuote::where('customer_id', $customer1->id)->get();
    expect($quotesWithCustomer1)->toHaveCount(1);
    expect($quotesWithCustomer1->first()->id)->toBe($quote1->id);

    // Verificar que podemos filtrar por customer_id null
    $quotesWithoutCustomer = CustomerQuote::whereNull('customer_id')->get();
    expect($quotesWithoutCustomer)->toHaveCount(1);
    expect($quotesWithoutCustomer->first()->id)->toBe($quote3->id);
});