<?php

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteProduct;
use App\Models\CustomerQuoteProductVariant;
use App\Models\ProductionBatch;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\Supplier;

test('puede crear un lote de producción', function () {
    $quote = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();
    $product = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote->id]);
    $variant = CustomerQuoteProductVariant::factory()->create(['quote_product_id' => $product->id]);

    $purchaseOrder = PurchaseOrder::factory()->create([
        'quote_id' => $quote->id,
        'supplier_id' => $supplier->id,
    ]);

    $poItem = PurchaseOrderItem::factory()->create([
        'purchase_order_id' => $purchaseOrder->id,
        'quote_product_variant_id' => $variant->id,
        'quantity' => 200,
    ]);

    $batchData = [
        'quantity' => 120,
        'planned_start' => '2025-10-01T00:00:00Z',
        'planned_finish' => '2025-10-10T00:00:00Z',
        'notes' => 'Split por capacidad',
    ];

    $response = $this->postJson("/api/purchase-order-items/{$poItem->id}/batches", $batchData);

    $response->assertSuccessful()
        ->assertJsonStructure([
            'id',
            'purchase_order_item_id',
            'quantity',
            'planned_start',
            'planned_finish',
            'status',
            'pool_state',
            'correlation_id',
            'notes',
            'purchase_order_item',
        ]);

    $this->assertDatabaseHas('production_batches', [
        'purchase_order_item_id' => $poItem->id,
        'quantity' => 120,
        'status' => 'borrador',
        'pool_state' => 'available',
    ]);
});

test('puede cambiar el estado de un lote', function () {
    $batch = ProductionBatch::factory()->create(['status' => 'borrador']);

    $response = $this->postJson("/api/production-batches/{$batch->id}/status", [
        'status' => 'planificado',
    ]);

    $response->assertSuccessful()
        ->assertJsonFragment(['status' => 'planificado']);

    $this->assertDatabaseHas('production_batches', [
        'id' => $batch->id,
        'status' => 'planificado',
    ]);
});

test('no puede cambiar a un estado inválido', function () {
    $batch = ProductionBatch::factory()->create(['status' => 'borrador']);

    $response = $this->postJson("/api/production-batches/{$batch->id}/status", [
        'status' => 'completado', // Saltar estados intermedios
    ]);

    $response->assertUnprocessable()
        ->assertJsonFragment(['error' => "No se puede cambiar de estado 'borrador' a 'completado'."]);
});

test('puede hacer transiciones válidas de estado', function () {
    $batch = ProductionBatch::factory()->create(['status' => 'borrador']);

    // borrador -> planificado
    $response = $this->postJson("/api/production-batches/{$batch->id}/status", [
        'status' => 'planificado',
    ]);
    $response->assertSuccessful();

    // planificado -> en_produccion
    $response = $this->postJson("/api/production-batches/{$batch->id}/status", [
        'status' => 'en_produccion',
    ]);
    $response->assertSuccessful();

    // en_produccion -> completado
    $response = $this->postJson("/api/production-batches/{$batch->id}/status", [
        'status' => 'completado',
    ]);
    $response->assertSuccessful();

    $this->assertDatabaseHas('production_batches', [
        'id' => $batch->id,
        'status' => 'completado',
    ]);
});

test('valida fechas de planificación', function () {
    $quote = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();
    $product = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote->id]);
    $variant = CustomerQuoteProductVariant::factory()->create(['quote_product_id' => $product->id]);

    $purchaseOrder = PurchaseOrder::factory()->create([
        'quote_id' => $quote->id,
        'supplier_id' => $supplier->id,
    ]);

    $poItem = PurchaseOrderItem::factory()->create([
        'purchase_order_id' => $purchaseOrder->id,
        'quote_product_variant_id' => $variant->id,
    ]);

    $batchData = [
        'quantity' => 100,
        'planned_start' => '2025-10-10T00:00:00Z',
        'planned_finish' => '2025-10-01T00:00:00Z', // Fecha de fin antes que inicio
    ];

    $response = $this->postJson("/api/purchase-order-items/{$poItem->id}/batches", $batchData);

    $response->assertUnprocessable()
        ->assertJsonValidationErrors(['planned_finish']);
});

test('puede crear múltiples lotes para un mismo ítem', function () {
    $quote = CustomerQuote::factory()->create();
    $supplier = Supplier::factory()->create();
    $product = CustomerQuoteProduct::factory()->create(['customer_quote_id' => $quote->id]);
    $variant = CustomerQuoteProductVariant::factory()->create(['quote_product_id' => $product->id]);

    $purchaseOrder = PurchaseOrder::factory()->create([
        'quote_id' => $quote->id,
        'supplier_id' => $supplier->id,
    ]);

    $poItem = PurchaseOrderItem::factory()->create([
        'purchase_order_id' => $purchaseOrder->id,
        'quote_product_variant_id' => $variant->id,
        'quantity' => 200,
    ]);

    // Crear primer lote
    $response1 = $this->postJson("/api/purchase-order-items/{$poItem->id}/batches", [
        'quantity' => 120,
        'notes' => 'Lote 1',
    ]);
    $response1->assertSuccessful();

    // Crear segundo lote
    $response2 = $this->postJson("/api/purchase-order-items/{$poItem->id}/batches", [
        'quantity' => 80,
        'notes' => 'Lote 2',
    ]);
    $response2->assertSuccessful();

    // Verificar que se crearon ambos lotes
    $this->assertDatabaseCount('production_batches', 2);

    $batches = ProductionBatch::where('purchase_order_item_id', $poItem->id)->get();
    expect($batches->sum('quantity'))->toBe(200);
});
