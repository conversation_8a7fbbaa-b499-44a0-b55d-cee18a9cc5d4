<?php

declare(strict_types=1);

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteGroup;
use App\Models\CustomerQuoteProduct;
use App\Models\CustomerQuoteProductVariant;

test('kit composition validation works correctly for valid kit', function () {
    $quote = CustomerQuote::factory()->create();

    // Kit válido: 100 kits × 1 memoria por kit = 100 memorias
    $kit = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'quantity' => 100,
        'unit_price_minor' => 2700000,
        'price_currency' => 'CLP',
    ]);

    $product = CustomerQuoteProduct::factory()->create([
        'group_id' => $kit->id,
        'units_per_kit' => 1,
    ]);

    // 70 + 30 = 100 ✓
    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 70,
    ]);

    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 30,
    ]);

    expect($kit->isValidKitComposition())->toBeTrue();
    expect($kit->validateKitComposition())->toBeEmpty();
});

test('kit composition validation detects errors when quantities do not match', function () {
    $quote = CustomerQuote::factory()->create();

    $kit = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'quantity' => 100,
        'unit_price_minor' => 2700000,
        'price_currency' => 'CLP',
    ]);

    $product = CustomerQuoteProduct::factory()->create([
        'group_id' => $kit->id,
        'units_per_kit' => 1,
    ]);

    // 80 + 30 = 110, pero esperamos 100 × 1 = 100 ❌
    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 80, // Error: debería ser 70
    ]);

    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 30,
    ]);

    expect($kit->isValidKitComposition())->toBeFalse();
    expect($kit->validateKitComposition())->not->toBeEmpty();
    expect($kit->validateKitComposition()[0])->toContain('Esperado 100 unidades (100 kits × 1), pero tiene 110 unidades');
});

test('kit composition validation works with multiple products', function () {
    $quote = CustomerQuote::factory()->create();

    // Kit de terraza: 50 kits
    $kit = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'quantity' => 50,
        'unit_price_minor' => 3000000,
        'price_currency' => 'CLP',
    ]);

    // Mesa: 1 por kit = 50 mesas
    $mesa = CustomerQuoteProduct::factory()->create([
        'group_id' => $kit->id,
        'units_per_kit' => 1,
    ]);

    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $mesa->id,
        'quantity' => 50, // 50 kits × 1 = 50 ✓
    ]);

    // Sillas: 4 por kit = 200 sillas
    $sillas = CustomerQuoteProduct::factory()->create([
        'group_id' => $kit->id,
        'units_per_kit' => 4,
    ]);

    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $sillas->id,
        'quantity' => 200, // 50 kits × 4 = 200 ✓
    ]);

    expect($kit->isValidKitComposition())->toBeTrue();
    expect($kit->validateKitComposition())->toBeEmpty();
});

test('kit composition validation detects errors in multiple products', function () {
    $quote = CustomerQuote::factory()->create();

    $kit = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'quantity' => 50,
        'unit_price_minor' => 3000000,
        'price_currency' => 'CLP',
    ]);

    // Mesa: 1 por kit = 50 mesas (correcto)
    $mesa = CustomerQuoteProduct::factory()->create([
        'group_id' => $kit->id,
        'units_per_kit' => 1,
    ]);

    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $mesa->id,
        'quantity' => 50, // 50 kits × 1 = 50 ✓
    ]);

    // Sillas: 4 por kit = 200 sillas (incorrecto)
    $sillas = CustomerQuoteProduct::factory()->create([
        'group_id' => $kit->id,
        'units_per_kit' => 4,
    ]);

    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $sillas->id,
        'quantity' => 180, // 50 kits × 4 = 200, pero tenemos 180 ❌
    ]);

    expect($kit->isValidKitComposition())->toBeFalse();
    expect($kit->validateKitComposition())->not->toBeEmpty();
    expect($kit->validateKitComposition()[0])->toContain('Sillas');
    expect($kit->validateKitComposition()[0])->toContain('Esperado 200 unidades (50 kits × 4), pero tiene 180 unidades');
});

test('kit composition validation skips non-kit groups', function () {
    $quote = CustomerQuote::factory()->create();

    // Grupo sin precio fijo (kit con precios de componentes)
    $group = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'quantity' => null, // Sin cantidad de kits
        'unit_price_minor' => null, // Sin precio fijo
    ]);

    $product = CustomerQuoteProduct::factory()->create([
        'group_id' => $group->id,
        'units_per_kit' => null, // Sin unidades por kit
    ]);

    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 100,
    ]);

    // No debería validar porque no es kit con precio fijo
    expect($group->isValidKitComposition())->toBeTrue();
    expect($group->validateKitComposition())->toBeEmpty();
});

test('kit composition validation skips products without units_per_kit', function () {
    $quote = CustomerQuote::factory()->create();

    $kit = CustomerQuoteGroup::factory()->create([
        'customer_quote_id' => $quote->id,
        'quantity' => 100,
        'unit_price_minor' => 2700000,
        'price_currency' => 'CLP',
    ]);

    // Producto sin units_per_kit
    $product = CustomerQuoteProduct::factory()->create([
        'group_id' => $kit->id,
        'units_per_kit' => null,
    ]);

    CustomerQuoteProductVariant::factory()->create([
        'quote_product_id' => $product->id,
        'quantity' => 999, // Cualquier cantidad, no se valida
    ]);

    // No debería validar este producto porque no tiene units_per_kit
    expect($kit->isValidKitComposition())->toBeTrue();
    expect($kit->validateKitComposition())->toBeEmpty();
});
