<?php

declare(strict_types=1);

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteProduct;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductType;
use App\Models\User;
use Database\Seeders\RolesSeeder;

beforeEach(function () {
    $this->seed(RolesSeeder::class);
});

test('CustomerQuoteProductResource sigue funcionando con la nueva taxonomía', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear taxonomía completa
    $category = ProductCategory::factory()->create();
    $subcategory = ProductSubcategory::factory()->create(['category_id' => $category->id]);
    $type = ProductType::factory()->create(['subcategory_id' => $subcategory->id]);

    // Crear cotización y producto
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => $type->id,
    ]);

    // Verificar que el recurso sigue funcionando
    $this->get('/admin/customer-quote-products')
        ->assertSuccessful()
        ->assertSee($product->name);

    $this->get("/admin/customer-quote-products/{$product->id}")
        ->assertSuccessful()
        ->assertSee($product->name);
});

test('los cálculos de totales siguen funcionando correctamente', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear taxonomía
    $category = ProductCategory::factory()->create();
    $subcategory = ProductSubcategory::factory()->create(['category_id' => $category->id]);
    $type = ProductType::factory()->create(['subcategory_id' => $subcategory->id]);

    // Crear producto
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => $type->id,
    ]);

    // Verificar cálculos
    expect($product->totalUnits())->toBe(0); // Sin variantes
    expect($product->totalAmount())->toBe(0.0); // Sin variantes
    expect($product->avgUnitPrice())->toBeNull(); // Sin variantes
});

test('las relaciones con tipos de productos siguen funcionando', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear taxonomía
    $category = ProductCategory::factory()->create();
    $subcategory = ProductSubcategory::factory()->create(['category_id' => $category->id]);
    $type = ProductType::factory()->create(['subcategory_id' => $subcategory->id]);

    // Crear producto
    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => $type->id,
    ]);

    // Verificar que la relación funciona
    expect($product->type_id)->toBe($type->id);

    // Verificar que podemos acceder a la taxonomía completa
    expect($type->subcategory->category->id)->toBe($category->id);
});

test('los filtros de tabla siguen funcionando', function () {
    $user = User::factory()->create();
    $user->assignRole('admin');

    $this->actingAs($user);

    // Crear datos de prueba
    $category = ProductCategory::factory()->create();
    $subcategory = ProductSubcategory::factory()->create(['category_id' => $category->id]);
    $type = ProductType::factory()->create(['subcategory_id' => $subcategory->id]);

    $quote = CustomerQuote::factory()->create();
    $product = CustomerQuoteProduct::factory()->create([
        'customer_quote_id' => $quote->id,
        'type_id' => $type->id,
    ]);

    // Verificar que los filtros funcionan
    $this->get('/admin/customer-quote-products')
        ->assertSuccessful()
        ->assertSee($product->name)
        ->assertSee($quote->quote_number);
});
