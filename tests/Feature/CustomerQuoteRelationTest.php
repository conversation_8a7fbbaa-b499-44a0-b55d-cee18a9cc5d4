<?php

use App\Models\Customer;
use App\Models\CustomerQuote;

test('puede crear una cotización con cliente relacionado', function () {
    $client = Customer::factory()->create([
        'name' => 'Cliente Test',
        'email' => '<EMAIL>',
        'country' => 'CL',
    ]);

    $quote = CustomerQuote::create([
        'quote_number' => 'COT-REL-001',
        'customer_id' => $client->id,
        'customer_name' => $client->name,
        'currency' => 'USD',
        'country' => $client->country,
        'valid_until' => now()->addDays(30),
        'status' => 'borrador',
    ]);

    expect($quote->customer_id)->toBe($client->id);
    expect($quote->customer_name)->toBe($client->name);
    expect($quote->customer->name)->toBe($client->name);
    expect($client->quotes->count())->toBe(1);
    expect($client->quotes->first()->quote_number)->toBe('COT-REL-001');
});

test('puede obtener información del cliente desde la cotización', function () {
    $client = Customer::factory()->create([
        'name' => 'Cliente Relación',
        'email' => '<EMAIL>',
        'company' => 'Empresa Relación',
        'country' => 'AR',
    ]);

    $quote = CustomerQuote::create([
        'quote_number' => 'COT-REL-002',
        'customer_id' => $client->id,
        'customer_name' => $client->name,
        'currency' => 'EUR',
        'country' => 'CL', // Diferente al cliente
        'valid_until' => now()->addDays(30),
        'status' => 'enviada',
    ]);

    expect($quote->customer_name)->toBe('Cliente Relación');
    expect($quote->customer_full_name)->toBe('Cliente Relación (Empresa Relación)');
    expect($quote->customer_country)->toBe('AR'); // Del cliente, no de la cotización
});

test('puede filtrar cotizaciones por cliente', function () {
    $client1 = Customer::factory()->create(['name' => 'Cliente Uno']);
    $client2 = Customer::factory()->create(['name' => 'Cliente Dos']);

    CustomerQuote::factory()->count(2)->create(['customer_id' => $client1->id]);
    CustomerQuote::factory()->count(3)->create(['customer_id' => $client2->id]);

    $quotesClient1 = CustomerQuote::forCustomer($client1->id)->get();
    $quotesClient2 = CustomerQuote::forCustomer($client2->id)->get();

    expect($quotesClient1)->toHaveCount(2);
    expect($quotesClient2)->toHaveCount(3);
    expect($quotesClient1->every(fn ($quote) => $quote->customer_id === $client1->id))->toBeTrue();
});

test('puede filtrar cotizaciones con y sin cliente', function () {
    $client = Customer::factory()->create();

    CustomerQuote::factory()->count(2)->create(['customer_id' => $client->id]);
    CustomerQuote::factory()->count(3)->create(['customer_id' => null]);

    $quotesWithClient = CustomerQuote::withCustomer()->get();
    $quotesWithoutClient = CustomerQuote::withoutCustomer()->get();

    expect($quotesWithClient)->toHaveCount(2);
    expect($quotesWithoutClient)->toHaveCount(3);
    expect($quotesWithClient->every(fn ($quote) => $quote->customer_id !== null))->toBeTrue();
    expect($quotesWithoutClient->every(fn ($quote) => $quote->customer_id === null))->toBeTrue();
});

test('puede obtener estadísticas de cotizaciones por cliente', function () {
    $client = Customer::factory()->create();

    CustomerQuote::factory()->count(5)->create(['customer_id' => $client->id]);

    $client->refresh();

    expect($client->quotes_count)->toBe(5);
    expect($client->hasQuotes())->toBeTrue();
    expect($client->last_quote)->not->toBeNull();
    expect($client->last_quote->customer_id)->toBe($client->id);
});

test('puede crear cotización sin cliente (compatibilidad hacia atrás)', function () {
    $quote = CustomerQuote::create([
        'quote_number' => 'COT-SIN-CLIENTE',
        'customer_id' => null,
        'customer_name' => 'Cliente Manual',
        'currency' => 'CLP',
        'country' => 'CL',
        'valid_until' => now()->addDays(30),
        'status' => 'borrador',
    ]);

    expect($quote->customer_id)->toBeNull();
    expect($quote->customer_name)->toBe('Cliente Manual');
    expect($quote->customer)->toBeNull();
    expect($quote->customer_full_name)->toBe('Cliente Manual');
});
