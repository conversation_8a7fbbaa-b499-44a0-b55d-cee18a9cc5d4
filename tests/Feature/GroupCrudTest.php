<?php

use Database\Seeders\ProductTaxonomySeeder;

it('creates, updates and deletes a group; delete cascades products/variants', function () {
    $this->seed(ProductTaxonomySeeder::class);

    // Create quote
    $quote = $this->postJson('/api/customer-quotes', [
        'customer_name' => 'Empresa X',
        'currency' => 'CLP',
    ])->assertCreated()->json();

    // Create group
    $group = $this->postJson("/api/customer-quotes/{$quote['id']}/groups", [
        'name' => 'Grupo A',
        'notes' => 'Notas A',
        'position' => 1,
    ])->assertCreated()->json();

    // Update group
    $this->patchJson("/api/customer-quote-groups/{$group['id']}", [
        'name' => 'Grupo A1',
        'notes' => 'Notas A1',
    ])->assertOk()->assertJsonPath('name', 'Grupo A1');

    // Create a product under group
    $typeId = \App\Models\ProductType::query()->value('id');
    expect($typeId)->not->toBeNull();
    $product = $this->postJson("/api/customer-quotes/{$quote['id']}/products", [
        'group_id' => $group['id'],
        'type_id' => $typeId,
        'name' => 'Producto Local',
        'attributes' => ['capacidad' => 500, 'interfaz' => 'USB-A', 'material' => 'Acero 304'],
        'position' => 1,
    ])->assertCreated()->json();

    // Add a variant
    $this->postJson("/api/customer-quote-products/{$product['id']}/variants", [
        'label' => 'Var 1',
        'attributes' => ['color' => 'Negro', 'acabado' => 'Láser'],
        'quantity' => 10,
        'price_currency' => 'CLP',
        'unit_price_minor' => 1000,
    ])->assertCreated();

    // Delete group -> products are not deleted (group FK is set null)
    $this->deleteJson("/api/customer-quote-groups/{$group['id']}")
        ->assertNoContent();

    $this->assertDatabaseHas('customer_quote_products', [
        'id' => $product['id'],
        'group_id' => null,
    ]);
});
