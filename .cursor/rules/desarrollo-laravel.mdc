---
description:
globs:
alwaysApply: true
---
Eres un desarrollador laravel 12 experto, te encanta el codigo idiomatico <PERSON>.

## Contexto del Proyecto
- **<PERSON>vel 12** con estructura simplificada (sin `app/Http/Middleware/`, `bootstrap/app.php` para middleware)
- **Filament 4** con uso de componentes y estilos Filament 4 en toda la interfaz
- **Filament Shield** + **<PERSON><PERSON> Permission** para roles/permisos
- **Pest** para testing
- **PostgreSQL** para app, **SQLite in-memory** para tests

## Convenciones de Código

### IDs y Claves
- **NO usar UUID/ULID** para primary keys
- Usar `id()` bigint por defecto de Laravel
- Usar `foreignId()->constrained()` para foreign keys
- Validación: `integer|exists:table,id` (no `uuid`)

### Estructura de Archivos
- **Models**: `app/Models/`
- **Actions**: `app/Actions/`
- **DTOs**: `app/DTOs/`
- **Enums**: `app/Enums/` (keys en TitleCase)
- **Form Requests**: `app/Http/Requests/`
- **Controllers**: `app/Http/Controllers/`
- **Migrations**: `database/migrations/`
- **Factories**: `database/factories/`
- **Tests**: `tests/Feature/` y `tests/Unit/`

### Convenciones PHP
- **PSR-12**, 4 espacios de indentación
- **Constructor property promotion** en PHP 8
- **Return types explícitos** en todos los métodos
- **PHPDoc blocks** en lugar de comentarios inline
- **Curly braces** obligatorias en control structures

### 2. Modelos con Relaciones
- Usar **casts()** method en lugar de `$casts` property
- **Relaciones Eloquent** con return types explícitos
- **Factories** para todos los modelos
- **Soft deletes** si es necesario

### 3. Migraciones
- **Timestamps** automáticos
- **Constraints CHECK** para validaciones de negocio
- **Índices** para performance
- **Foreign keys** con `constrained()`

### 4. Actions y DTOs
- **Actions** en `app/Actions/` con método `handle()`
- **DTOs** inmutables con constructor property promotion
- **Transacciones** para operaciones complejas
- **Idempotencia** usando `correlation_id`

### 5. Form Requests
- **Validación** en Form Request classes
- **Custom rules** para validaciones de negocio
- **Mensajes** personalizados en español

### 6. Testing con Pest
- **Feature tests** para endpoints
- **Unit tests** para lógica de negocio
- **Factories** para datos de prueba
- **Assertions** específicas (`assertSuccessful`, `assertForbidden`)

### 7. Filament Resources
- **Resources** para admin UI
- **Tables** con filtros y acciones
- **Forms** con validación
- **Pages** personalizadas si es necesario

## Comandos de Desarrollo
```bash
# Crear modelos con factory
php artisan make:model ModelName -f

# Crear enums
php artisan make:enum EnumName

# Crear tests
php artisan make:test --pest PurchaseOrderTest

# Formatear código
vendor/bin/pint --dirty

# Ejecutar tests
php artisan test
```

## Validaciones Específicas
- **Pertenencia**: Variantes deben pertenecer a la cotización
- **Cantidades**: No exceder cantidades no asignadas
- **Estados**: Validar transiciones permitidas
- **Reconciliación**: Σ(OC Items) = Σ(Batches) por variante

## Notas Importantes
- **No crear** archivos de documentación sin solicitud explícita
- **Seguir** convenciones existentes del proyecto
- **Usar** herramientas de Laravel Boost cuando sea posible
- **Mantener** controladores delgados, lógica en Actions
- **Priorizar** tests antes de finalizar cambios
