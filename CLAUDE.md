# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Laravel 12 application for managing manufacturing quotes and purchase orders (PSS - Purchase Sourcing System). It's built using modern Laravel stack with Livewire/Volt for frontend interactivity, Filament for admin panels, and PostgreSQL as the primary database.

### Core Architecture

- **Backend**: Laravel 12 with PHP 8.4
- **Frontend**: Livewire 3 + Volt for interactive components, Flux UI component library
- **Database**: PostgreSQL (dev: localhost:55432, db: app, user: app, pass: secret)  
- **Admin Panel**: Filament 4 with <PERSON><PERSON> Permission for RBAC
- **Testing**: Pest 4 (with browser testing capabilities)
- **Styling**: Tailwind CSS 4

### Domain Model

The application manages a quote-to-order workflow:
- **Customers** request quotes for manufactured products
- **Customer Quotes** contain groups and products with variants
- **Suppliers** provide materials through **Purchase Orders**
- **Production Batches** track manufacturing
- **Product Taxonomy**: Types → Categories → Subcategories

Key entities: `Customer`, `CustomerQuote`, `CustomerQuoteGroup`, `CustomerQuoteProduct`, `CustomerQuoteProductVariant`, `Supplier`, `PurchaseOrder`, `ProductType`, `ProductCategory`, etc.

## Development Commands

### Essential Commands
```bash
# Full development environment (server + queue + logs + vite)
composer run dev

# Individual services
php artisan serve                    # Development server
php artisan queue:listen --tries=1   # Queue worker  
php artisan pail --timeout=0        # Log monitoring
npm run dev                         # Vite asset building

# Build assets for production
npm run build

# Database
php artisan migrate:fresh --seed    # Fresh database with seed data

# Testing
php artisan test                    # Run all tests
php artisan test --filter=TestName # Run specific test
php artisan test tests/Feature/ExampleTest.php # Run specific file

# Code quality
vendor/bin/pint                    # Fix code formatting
vendor/bin/pint --dirty            # Fix only modified files
```

### Authentication & Authorization

**Default Users (Development):**
- Admin: `<EMAIL>` / `password`
- Super Admin: `<EMAIL>` / `password` 
- Panel User: `<EMAIL>` / `password`

**RBAC System:**
- Uses Spatie Laravel Permission + Filament Shield
- Roles: `super_admin`, `admin`, `panel_user`
- Filament admin panel: `/admin` (accessible by `admin` and `super_admin` roles)
- Super admin gets full access via `Gate::before`

## Code Conventions

### Database Design
- **CRITICAL**: Use autoincremental `id()` PKs and `foreignId()->constrained()` FKs
- **NO UUIDs or ULIDs** - this is explicitly forbidden per project conventions
- Validation rules: `integer|exists:table,id` for foreign keys
- Before merging, run: `rg -n "\buuid\b|HasUuids|ulid\(" --hidden --glob '!vendor/**' --glob '!public/js/**' --glob '!composer.lock'`

### Filament Resources Structure
Resources follow a highly organized structure:
```
app/Filament/Resources/EntityName/
├── EntityNameResource.php          # Main resource
├── Pages/                          # CRUD pages
│   ├── ListEntityNames.php
│   ├── CreateEntityName.php
│   ├── EditEntityName.php
│   └── ViewEntityName.php
├── Schemas/                        # Form and info list schemas
│   ├── EntityNameForm.php
│   └── EntityNameInfolist.php
├── Tables/                         # Table definitions
│   └── EntityNamesTable.php
└── RelationManagers/               # Related data managers
```

### Livewire/Volt Usage
- Components live in `resources/views/livewire/`
- Use class-based Volt components (not functional API)
- Create with: `php artisan make:volt ComponentName --test --pest`
- Test in `tests/Feature/Volt/` directory

### Models & Relationships
- Use typed relationships with return type hints
- Follow Laravel 12 conventions (casts in `casts()` method, not `$casts` property)
- Leverage Eloquent relationships over raw queries
- Use eager loading to prevent N+1 queries

### Validation & Forms
- Always use Form Request classes for validation
- Provide Spanish error messages in `lang/es/validation.php`
- Follow existing patterns for array vs string-based validation rules

### Testing with Pest 4
- Use Pest for all tests (not PHPUnit syntax)
- Leverage browser testing capabilities for UI testing
- Use model factories extensively
- Test files: `tests/Feature/` and `tests/Unit/`
- Run specific tests: `php artisan test --filter=testName`

## Project-Specific Notes

### Current Development Focus
The application is currently implementing a customer quote restructure (see `wip/feat1/ghissue1-backend.md`). The migration from `client_*` to `customer_*` naming is in progress.

### Migration Status
There's an ongoing rename from "Client" to "Customer" terminology:
- New models use `Customer*` naming
- Legacy `Client*` models are being phased out
- Migration files handle the table/column renames

### Key Business Rules
- Customer quotes have a hierarchical structure: Quote → Groups → Products → Variants
- Only products (not groups) can have variants
- Quantities must be ≥ 1
- Variant IDs must belong to their parent product
- Quote statuses: `borrador` | `enviada` | `aprobada` | `rechazada`

### Livewire Components Location
Interactive components are in `resources/views/livewire/`:
- `quotes/` - Quote management
- `sourcing/` - Sourcing and planning
- `auth/` - Authentication flows
- `settings/` - User settings

## Environment Setup Notes

- PostgreSQL runs on port 55432 (not default 5432)
- Use provided `.env.example` as starting point
- Queue connection: `database` (no Redis required for development)
- Vite serves assets with CORS enabled
- Mail driver set to `log` for development