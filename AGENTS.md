# Repository Guidelines

## Project Structure & Module Organization
- `app/` domain logic, models, policies; `routes/` HTTP & console routes.
- `resources/` views/assets (Vite + Tailwind); `public/` web root.
- `config/`, `database/` (migrations, seeders), `storage/` runtime files.
- `tests/` Pest tests under `Feature/` and `Unit/`.
- Admin/UI uses Filament; auth/roles via Spatie Permission; Livewire volt/flux present.

## Build, Test, and Development Commands
- Install: `composer install` and `npm ci` (or `npm i`).
- Env: `cp .env.example .env && php artisan key:generate`.
- DB (local): `docker compose up -d postgres` (see `docker-compose.yml`).
- Migrate/seed: `php artisan migrate --seed`.
- Dev server: `composer run dev` (serves app, queue worker, logs, and Vite).
- Frontend only: `npm run dev`; Production assets: `npm run build`.
- Test suite: `composer test` or `php artisan test` (Pest enabled).

## Coding Style & Naming Conventions
- PHP: PSR-12, 4-space indent; run formatter: `vendor/bin/pint`.
- Classes `StudlyCase`, methods/vars `camelCase`, constants `UPPER_SNAKE_CASE`.
- Migrations: timestamped; tests end with `*Test.php` under `tests/Feature|Unit`.
- Keep controllers thin; prefer actions/services in `app/` with clear namespaces.

## Testing Guidelines
- Framework: Pest + Laravel plugin. Place integration tests in `tests/Feature`, units in `tests/Unit`.
- Name tests descriptively (e.g., `EditUserFormTest.php`).
- Fast DB defaults to in-memory SQLite (see `phpunit.xml`). Use factories/seeders.
- Run a focused file: `./vendor/bin/pest tests/Feature/RolePolicyTest.php`.

## Commit & Pull Request Guidelines
- Use Conventional Commits: `feat(scope): …`, `fix: …`, `chore: …`, `docs: …`, `test: …`.
- PRs: include summary, linked issues, migration/seed notes, and UI screenshots when relevant.
- Ensure green tests, formatted code, and updated docs.

## Security & Configuration Tips
- Never commit secrets. Configure local creds in `.env`; production via env vars.
- Default DB for app can be Postgres (see compose); tests use SQLite in-memory.
- If adding permissions/roles, update seeders and Filament resources accordingly.

## IDs & Keys
- Primary keys: use Laravel defaults (`id()` bigint) and `foreignId()->constrained()`.
- Do NOT introduce UUID/ULID PKs or `HasUuids` traits.
- Validation: prefer `integer|exists:table,id` over `uuid` rules.
- Quick check before merge: `rg -n "\\buuid\\b|HasUuids|ulid\\(" --hidden --glob '!vendor/**' --glob '!public/js/**' --glob '!composer.lock'`

## Agent-Specific Notes
- Follow this file for structure/style. Prefer minimal, focused diffs and keep changes within existing conventions.
