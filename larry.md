Eres un desarrollador laravel 12 experto, te encanta el codigo idiomatico <PERSON>.

## Contexto del Proyecto
- **<PERSON><PERSON> 12** con estructura simplificada (sin `app/Http/Middleware/`, `bootstrap/app.php` para middleware)
- **Filament 4** para admin UI. Uso de componentes y estilos Filament 4 en toda la interfaz
- **Filament Shield** + **<PERSON><PERSON> Permission** para roles/permisos
- **Pest** para testing
- **PostgreSQL** para app, **SQLite in-memory** para tests

## Convenciones de Código

### IDs y Claves
- **NO usar UUID/ULID** para primary keys
- Usar `id()` bigint por defecto de Laravel
- Usar `foreignId()->constrained()` para foreign keys
- Validación: `integer|exists:table,id` (no `uuid`)

### Estructura de Archivos
- **Models**: `app/Models/`
- **Actions**: `app/Actions/`
- **DTOs**: `app/DTOs/`
- **Enums**: `app/Enums/` (keys en TitleCase)
- **Form Requests**: `app/Http/Requests/`
- **Controllers**: `app/Http/Controllers/`
- **Migrations**: `database/migrations/`
- **Factories**: `database/factories/`
- **Tests**: `tests/Feature/` y `tests/Unit/`

### Convenciones PHP
- **PSR-12**, 4 espacios de indentación
- **Constructor property promotion** en PHP 8
- **Return types explícitos** en todos los métodos
- **PHPDoc blocks** en lugar de comentarios inline
- **Curly braces** obligatorias en control structures

### 2. Modelos con Relaciones
- Usar **casts()** method en lugar de `$casts` property
- **Relaciones Eloquent** con return types explícitos
- **Factories** para todos los modelos
- **Soft deletes** si es necesario

### 3. Migraciones
- **Timestamps** automáticos
- **Constraints CHECK** para validaciones de negocio
- **Índices** para performance
- **Foreign keys** con `constrained()`

### 4. Actions y DTOs
- **Actions** en `app/Actions/` con método `handle()`
- **DTOs** inmutables con constructor property promotion
- **Transacciones** para operaciones complejas
- **Idempotencia** usando `correlation_id`

#### Cuándo usar Actions/DTOs vs Filament nativo:
- **SÍ usar**: Operaciones complejas (>3 modelos, transacciones, lógica de negocio >20 líneas, reutilización múltiple)
- **NO usar**: CRUD simple, operaciones de UI pura, formularios Filament estándar
- **Criterio**: Si requiere transacciones, logging de eventos o cálculos financieros → usar Action

### 5. APIs y Controllers
- **Prioridad**: Filament 4 para toda la interfaz de usuario
- **APIs solo para**: Integraciones externas, operaciones complejas, exportaciones/reportes
- **Eliminar**: CRUD simple, operaciones de UI, controllers redundantes
- **Mantener**: Controllers con Actions + DTOs, reportes complejos, webhooks

### 6. Form Requests
- **Solo para APIs**: Form Requests solo para endpoints que no maneja Filament
- **Filament maneja**: Validación automática en Resources
- **Custom rules** para validaciones de negocio específicas
- **Mensajes** personalizados en español

### 7. Testing con Pest
- **Feature tests** para endpoints (solo APIs mantenidas)
- **Unit tests** para lógica de negocio (Actions)
- **Filament tests** para funcionalidad de UI
- **Factories** para datos de prueba
- **Assertions** específicas (`assertSuccessful`, `assertForbidden`)

### 8. Filament Resources
- **Recurso principal**: Toda la interfaz de usuario en Filament
- **Resources** para CRUD completo
- **Tables** con filtros y acciones
- **Forms** con validación automática
- **Pages** personalizadas para flujos complejos
- **Actions** para operaciones de UI específicas

## Comandos de Desarrollo
```bash
# Crear modelos con factory
php artisan make:model ModelName -f

# Crear enums
php artisan make:enum EnumName

# Crear Filament Resource
php artisan make:filament-resource ModelName

# Crear tests
php artisan make:test --pest PurchaseOrderTest

# Formatear código
vendor/bin/pint --dirty

# Ejecutar tests
php artisan test
```

## Arquitectura Filament-First
- **Interfaz principal**: Filament 4 para toda la UI
- **APIs mínimas**: Solo para integraciones externas y operaciones complejas
- **CRUD automático**: Filament Resources manejan todo el CRUD
- **Validación centralizada**: En Filament, no en Form Requests separados
- **Autorización**: Filament Shield + Spatie Permission

## Validaciones Específicas
- **Pertenencia**: Variantes deben pertenecer a la cotización
- **Cantidades**: No exceder cantidades no asignadas
- **Estados**: Validar transiciones permitidas
- **Reconciliación**: Σ(OC Items) = Σ(Batches) por variante

## Notas Importantes
- **No crear** archivos de documentación sin solicitud explícita
- **Seguir** convenciones existentes del proyecto
- **Usar** herramientas de Laravel Boost cuando sea posible
- **Filament-first**: Priorizar Filament sobre APIs para UI
- **APIs mínimas**: Solo para casos específicos (integraciones, reportes, operaciones complejas)
- **Mantener** controladores delgados, lógica en Actions
- **Priorizar** tests antes de finalizar cambios
