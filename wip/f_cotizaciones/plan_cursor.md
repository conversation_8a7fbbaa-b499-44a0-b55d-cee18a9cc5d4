# Implementación de Feature: Gestión de Variantes Pendientes de Cotización

## Descripción
Feature que permite al analista de sourcing ver el listado de variantes de producto que no tienen cotización de proveedor (productos pendientes de cotización) y crear cotizaciones para esas variantes.

## Implementación Paso a Paso

### 1. Análisis del Modelo de Datos
- **Identificar la relación**: Las variantes de producto (`CustomerQuoteProductVariant`) que no tienen cotizaciones de proveedor asociadas
- **Definir el criterio**: Una variante está "pendiente" cuando no existe una `PurchaseOrder` o `PurchaseOrderItem` que la incluya
- **Considerar estados**: Filtrar por variantes que estén en estado "activo" o "pendiente" en la cotización del cliente
- **Revisar modelo existente**: Verificar si existe un modelo `SupplierQuote` o si necesitamos crearlo

### 2. Crear Query Scope en el Modelo
- **Agregar método `pendingForSourcing()`** en `CustomerQuoteProductVariant`
- **Implementar subquery** para excluir variantes que ya tienen órdenes de compra
- **Incluir relaciones necesarias** como `customerQuoteProduct`, `customerQuote`, `customer`
- **Aplicar filtros de negocio** (estados válidos, fechas, etc.)

### 3. Crear Action para Obtener Variantes Pendientes
- **Crear `GetPendingVariantsForSourcingAction`** en `app/Actions/`
- **Implementar método `handle()`** que ejecute el query scope
- **Aplicar paginación** para manejar grandes volúmenes de datos
- **Incluir filtros opcionales** (cliente, categoría, fecha, etc.)
- **Retornar colección optimizada** con datos necesarios para la UI

### 4. Crear Form Request para Filtros
- **Crear `FilterPendingVariantsRequest`** en `app/Http/Requests/`
- **Definir validaciones** para parámetros de filtrado
- **Incluir mensajes en español** para errores de validación
- **Aplicar sanitización** de datos de entrada

### 5. Crear Controller para la API
- **Crear `PendingVariantsController`** en `app/Http/Controllers/`
- **Implementar método `index()`** que use la Action
- **Aplicar autorización** usando policies existentes
- **Retornar JSON response** con datos paginados
- **Manejar excepciones** de negocio apropiadamente

### 6. Crear Modelo SupplierQuote (si no existe)
- **Crear modelo `SupplierQuote`** en `app/Models/`
- **Definir relaciones** con `CustomerQuoteProductVariant`, `Supplier`, `User`
- **Implementar casts** para fechas, precios y estados
- **Crear migración** con campos necesarios (supplier_id, variant_id, price, currency, status, etc.)
- **Agregar factory** para datos de prueba

### 7. Crear Action para Crear Cotización
- **Crear `CreateSupplierQuoteAction`** en `app/Actions/`
- **Implementar lógica de negocio** para crear cotización de proveedor
- **Validar que la variante esté pendiente** antes de crear
- **Generar correlation_id** para trazabilidad
- **Usar transacciones** para consistencia de datos

### 8. Crear DTO para Datos de Cotización
- **Crear `CreateSupplierQuoteDto`** en `app/DTOs/`
- **Definir propiedades inmutables** con constructor property promotion
- **Incluir validaciones** de tipos y rangos
- **Documentar estructura** con PHPDoc

### 9. Crear Form Request para Crear Cotización
- **Crear `CreateSupplierQuoteRequest`** en `app/Http/Requests/`
- **Validar datos requeridos** (proveedor, precio, moneda, etc.)
- **Aplicar reglas de negocio** (precios positivos, fechas válidas)
- **Incluir mensajes personalizados** en español

### 10. Implementar Endpoint para Crear Cotización
- **Agregar método `store()`** en el controller
- **Usar el Form Request** para validación
- **Ejecutar la Action** para crear la cotización
- **Retornar respuesta** con datos de la cotización creada
- **Manejar errores** de negocio apropiadamente

### 11. Crear Filament Resource para Admin
- **Crear `PendingVariantsResource`** en `app/Filament/Resources/`
- **Implementar tabla** con columnas relevantes (cliente, producto, variante, fecha)
- **Agregar filtros** por cliente, categoría, fecha
- **Implementar acciones** para crear cotizaciones
- **Configurar permisos** usando Filament Shield

### 12. Crear Policy para Autorización
- **Crear `PendingVariantPolicy`** en `app/Policies/`
- **Implementar métodos** `viewAny()`, `view()`, `create()`
- **Aplicar reglas de negocio** (solo analistas de sourcing)
- **Integrar con roles** de Spatie Permission

### 13. Crear Tests Feature
- **Crear `PendingVariantsTest`** en `tests/Feature/`
- **Probar endpoint de listado** con diferentes filtros
- **Probar creación de cotización** con datos válidos e inválidos
- **Probar autorización** para diferentes roles
- **Probar paginación** y ordenamiento

### 14. Crear Tests Unit
- **Crear `GetPendingVariantsForSourcingActionTest`** en `tests/Unit/`
- **Probar lógica de filtrado** con diferentes escenarios
- **Probar performance** con grandes volúmenes de datos
- **Probar edge cases** (variantes sin datos, estados inválidos)

### 15. Crear Factory para Datos de Prueba
- **Crear `SupplierQuoteFactory`** en `database/factories/`
- **Definir estados** para diferentes escenarios de prueba
- **Incluir relaciones** con variantes y proveedores
- **Generar datos realistas** para testing

### 16. Configurar Rutas
- **Agregar rutas API** en `routes/api.php`
- **Aplicar middleware** de autenticación y autorización
- **Usar route model binding** donde sea apropiado
- **Agregar prefijos** para organización

### 17. Implementar Logging y Auditoría
- **Agregar logs** en las Actions para trazabilidad
- **Registrar eventos** de creación de cotizaciones
- **Implementar auditoría** usando el modelo `EventLog` existente
- **Incluir correlation_id** en todos los logs

### 18. Optimizar Performance
- **Agregar índices** en la base de datos para queries frecuentes
- **Implementar eager loading** para evitar N+1 queries
- **Usar paginación** para grandes volúmenes
- **Considerar caché** para datos que no cambian frecuentemente

### 19. Documentar API
- **Agregar PHPDoc** en todos los métodos públicos
- **Documentar endpoints** con ejemplos de request/response
- **Incluir códigos de error** y sus significados
- **Documentar filtros** y parámetros disponibles

## Consideraciones Técnicas

### Convenciones del Proyecto
- Seguir estructura Laravel 12 con Actions, DTOs y Form Requests
- Usar Pest para testing con Feature y Unit tests
- Implementar autorización con Policies y Spatie Permission
- Mantener controladores delgados con lógica en Actions
- Usar Filament para interfaz de administración

### Performance y Escalabilidad
- Implementar paginación para manejar grandes volúmenes
- Usar eager loading para evitar N+1 queries
- Considerar índices de base de datos para queries frecuentes
- Implementar caché para datos que no cambian frecuentemente

### Seguridad y Auditoría
- Aplicar autorización apropiada para cada endpoint
- Registrar eventos importantes en EventLog
- Usar correlation_id para trazabilidad
- Validar datos de entrada con Form Requests

## Consideraciones de Negocio

### Estados de Cotización
- Definir estados válidos para `SupplierQuote` (pending, sent, received, accepted, rejected)
- Implementar transiciones de estado válidas
- Considerar fechas de vencimiento para cotizaciones

### Validaciones de Negocio
- Una variante no puede tener múltiples cotizaciones activas del mismo proveedor
- Validar que el proveedor esté activo antes de crear cotización
- Verificar que la variante pertenezca a una cotización de cliente válida
- Aplicar reglas de precio mínimo/máximo según políticas de la empresa

### Integración con Workflow Existente
- Considerar cómo se integra con el proceso de creación de Purchase Orders
- Definir qué sucede cuando se acepta una cotización de proveedor
- Establecer notificaciones para analistas cuando se reciben cotizaciones
