# Plan de Implementación: Cotizaciones de Proveedor para Variantes de Producto

A continuación se describe la estrategia de implementación para la funcionalidad que permite a los Analistas de Sourcing gestionar cotizaciones de proveedores para variantes de producto pendientes.

### 1. Definición de Rutas

- **Ruta para el listado:** Se creará una nueva ruta `GET`, por ejemplo `/productos-pendientes-cotizacion`, que apuntará a un nuevo método en un controlador. Esta ruta mostrará la lista de variantes sin cotización.
- **Ruta para el formulario de creación:** Se definirá una ruta `GET`, como `/cotizacion-proveedor/crear/{variante}`, que mostrará el formulario para crear una nueva cotización para una variante de producto específica.
- **Ruta para el guardado:** Se creará una ruta `POST`, como `/cotizacion-proveedor`, para procesar el formulario y almacenar la nueva cotización en la base de datos.

### 2. Controlador para el Listado

- Se creará un nuevo controlador, por ejemplo `ProductoPendienteCotizacionController`, o se añadirá un método a un controlador de Sourcing existente.
- El método principal (`index`) se encargará de obtener los datos. Utilizará los modelos de Eloquent para construir una consulta que seleccione todas las variantes de producto que **no tienen** una relación con una cotización de proveedor (`whereDoesntHave`).
- Este método pasará la colección de variantes resultante a una nueva vista.

### 3. Creación de la Vista del Listado

- Se diseñará una nueva vista de Blade para mostrar la lista.
- La vista contendrá una tabla con columnas relevantes como: SKU de la variante, nombre del producto, atributos (ej. color, talla), etc.
- Cada fila de la tabla tendrá un botón o enlace "Crear Cotización" que dirigirá al usuario a la ruta de creación, pasando el ID de la variante de producto.

### 4. Controlador para la Creación de Cotizaciones

- Se puede usar un nuevo `CotizacionProveedorController` o añadir los métodos al controlador existente.
- **Método `create`:** Recibirá el ID de la variante de producto. Cargará el modelo de la variante y lo pasará a la vista del formulario. También podría pasar una lista de proveedores disponibles para seleccionarlos en el formulario.
- **Método `store`:** Este método será el responsable de la lógica de guardado.

### 5. Formulario de Creación y `FormRequest`

- Se creará una vista de Blade con el formulario para la nueva cotización. El formulario incluirá campos como:
  - Proveedor (un campo de selección).
  - Precio de costo.
  - Moneda.
  - Cantidad mínima de pedido (MOQ).
  - Fecha de validez de la cotización.
- Se generará un `FormRequest` específico (ej. `StoreCotizacionProveedorRequest`) para validar los datos de entrada. Las reglas de validación asegurarán que el precio sea numérico, que el proveedor exista, que la fecha sea válida, etc.

### 6. Lógica de Almacenamiento (`store`)

- El método `store` del controlador recibirá el `FormRequest` validado.
- Creará una nueva instancia del modelo `CotizacionProveedor` (o el nombre que se le asigne).
- Poblará el modelo con los datos validados del formulario, incluyendo la asociación con el `id` de la variante de producto y el `id` del proveedor.
- Guardará el nuevo modelo en la base de datos.
- Finalmente, redirigirá al usuario de vuelta a la lista de productos pendientes con un mensaje de éxito (ej. "Cotización creada correctamente").

### 7. Modelo y Migración (si es necesario)

- Si aún no existe, se creará el modelo `CotizacionProveedor` con su correspondiente migración.
- La tabla en la base de datos deberá tener campos para el precio, moneda, MOQ, fechas, y las claves foráneas para `product_variant_id` y `supplier_id`.
- Se definirán las relaciones de Eloquent en los modelos:
  - Una `ProductVariant` puede tener muchas `CotizacionProveedor` (`hasMany`).
  - Una `CotizacionProveedor` pertenece a una `ProductVariant` (`belongsTo`).
  - Una `CotizacionProveedor` pertenece a un `Supplier` (`belongsTo`).

### 8. Permisos y Acceso

- Se protegerán todas las rutas nuevas con un `middleware`.
- El `middleware` verificará si el usuario autenticado tiene el rol de "Analista de Sourcing" (utilizando `spatie/laravel-permission` o la lógica de autorización de Laravel).
- Se añadirá un enlace en el menú de navegación principal (ej. en el sidebar) que apunte a la nueva página de listado. Este enlace solo será visible para los usuarios con el rol adecuado, usando la directiva `@can` de Blade.
