# Implementación: Feature para Gestión de Variantes Pendientes de Cotización

## Análisis del Problema
El analista de sourcing necesita identificar qué variantes de productos en cotizaciones de clientes aún no tienen cotizaciones de proveedores asociadas, para poder gestionarlas y solicitar precios.

## Pasos de Implementación

### **Paso 1: Análisis de Relaciones y Modelo de Datos**
- Identificar la relación entre CustomerQuoteProductVariant y PurchaseOrderItem (actualmente no existe directa)
- Determinar que una variante está "pendiente" cuando no tiene items de órdenes de compra asociados
- **CRÍTICO**: Crear relación faltante o tabla pivot `variant_purchase_order_items` para conectar variantes con items
- Analizar si PurchaseOrderItem necesita campo `variant_id` para referenciar variantes específicas

### **Paso 2: Crear Query Scope en el Modelo**
- Agregar scope `withoutPurchaseOrders()` en CustomerQuoteProductVariant
- Implementar scope `pendingQuotation()` que combine múltiples condiciones:
  - Solo variantes de cotizaciones en estado 'enviada' o 'aprobada'
  - Excluir variantes con PurchaseOrderItems asociados
  - Incluir solo cotizaciones no expiradas
- Incluir eager loading de relaciones necesarias (quoteProduct.quote.customer, quoteProduct.group)

### **Paso 3: Crear Recurso Filament Específico**
- Generar `PendingVariantsResource` en `app/Filament/Resources/Sourcing/`
- Configurar como recurso de solo lectura (sin create/edit/delete actions)
- Establecer navegación en grupo "Sourcing" con icono `Heroicon::OutlineClipboardDocumentList`
- Configurar modelo base como `CustomerQuoteProductVariant` con query personalizado

### **Paso 4: Implementar Tabla con Información Contextual**
- Mostrar columnas: cliente, número cotización, grupo, producto, variante, cantidad, precio unitario, total
- Agregar columna de "días desde cotización" para urgencia
- Agregar filtros por cliente, estado de cotización, rango de fechas, tipo de producto
- Incluir búsqueda global por nombre de cliente, número de cotización, o nombre de producto
- Mostrar badges para estados críticos (cotizaciones próximas a vencer, alta prioridad)
- Implementar ordenamiento por defecto: cotizaciones más antiguas primero

### **Paso 5: Crear Acción Bulk para Solicitar Cotizaciones**
- Implementar `RequestQuotationBulkAction` que permita seleccionar múltiples variantes
- Crear modal con formulario para seleccionar proveedores destino
- Permitir agrupar variantes por criterios (mismo tipo de producto, mismo cliente)

### **Paso 6: Generar Purchase Orders Automáticamente**
- Crear servicio `PurchaseOrderGeneratorService` 
- Implementar lógica para agrupar variantes por proveedor seleccionado
- Auto-completar campos comunes (moneda basada en país del proveedor, términos por defecto)
- Generar números de orden correlativos y únicos con formato `PO-{YYYY}-{nnnn}`
- Crear PurchaseOrderItems correspondientes con referencia a cada variante
- Establecer estado inicial como "draft" o "pending_quote"

### **Paso 7: Crear Sistema de Notificaciones**
- Implementar notificación al crear órdenes de compra masivamente
- Enviar emails a proveedores con las nuevas solicitudes de cotización
- Crear dashboard widget mostrando estadísticas de variantes pendientes

### **Paso 8: Agregar Estado de Seguimiento**
- Extender modelo PurchaseOrder con estados específicos de cotización
- Implementar timestamps para tracking de tiempos de respuesta
- Crear vista de seguimiento de cotizaciones enviadas vs recibidas

### **Paso 9: Integrar con Dashboard de Sourcing**
- Crear widgets mostrando KPIs: variantes pendientes, tiempo promedio de cotización
- Implementar gráficos de tendencias y alertas por vencimiento
- Agregar accesos directos a acciones frecuentes

### **Paso 10: Implementar Permisos y Roles**
- Crear permisos específicos: `view_pending_variants`, `create_purchase_orders_bulk`
- Asignar a roles existentes según CLAUDE.md: `admin`, `super_admin` y crear rol `sourcing_analyst`
- Restringir acciones de crear órdenes según jerarquía y usar Spatie Laravel Permission
- Integrar con Filament Shield para gestión de permisos en panel admin

### **Paso 11: Crear Logs de Auditoría**
- Implementar EventLog para trackear creación masiva de órdenes
- Registrar qué usuario, cuándo y qué variantes fueron procesadas
- Crear reportes de actividad para supervisores

### **Paso 12: Optimizar Performance**
- Implementar caching para consulta de variantes pendientes
- Usar chunk processing para operaciones bulk sobre grandes volúmenes
- Optimizar queries con índices en campos frecuentemente filtrados

## Consideraciones Técnicas

**Filament Components Utilizados:**
- Resource con tabla personalizada
- BulkActions con modal forms
- Filters y search functionality
- Custom pages si se requiere funcionalidad específica

**Laravel Features Aplicados:**
- Eloquent relationships, scopes y HasManyThrough para consultas complejas
- Service classes para lógica de negocio (PurchaseOrderGeneratorService)
- Queued jobs para procesamiento masivo y envío de emails
- Policy classes para autorización granular (PendingVariantPolicy)
- Database transactions para operaciones atómicas
- Cache para optimizar consultas frecuentes

**UX/UI Considerations:**
- Navegación intuitiva desde dashboard principal
- Feedback visual claro durante operaciones bulk
- Validación en tiempo real de selecciones
- Confirmaciones apropiadas para acciones críticas

## Dependencias y Prerequisitos

**Modelos que requieren modificación:**
- `PurchaseOrderItem` → Agregar campo `variant_id` y relación `belongsTo(CustomerQuoteProductVariant)`
- `CustomerQuoteProductVariant` → Agregar relación `hasMany(PurchaseOrderItem)` 

**Migraciones requeridas:**
- `add_variant_id_to_purchase_order_items_table.php`
- `create_sourcing_roles_and_permissions.php`

**Estados de PurchaseOrder que deben existir:**
- `draft`, `pending_quote`, `quoted`, `accepted`, `rejected`

Esta implementación proporcionaría una herramienta completa para que los analistas de sourcing identifiquen eficientemente las brechas en cotizaciones y las resuelvan de manera masiva y organizada, respetando la arquitectura existente del sistema PSS.