# Taxonomía de Productos (Catálogo Ligero) para Enfoque B

## Introducción y Migración (Enfoque Refundacional)

Como el proyecto aún no se publica, adoptamos una migración refundacional del modelo: removemos la dependencia del catálogo global de ítems/variantes y hacemos obligatoria una taxonomía ligera (categoría → subcategoría → tipo) para clasificar productos locales de cotización.

Nuevo rumbo (high-level):
- Mantener Enfoque B (grupos/productos/variantes locales por cotización).
- Eliminar FKs a `products` y `product_variants` globales.
- Hacer obligatorio `type_id` (FK a `product_types`) en `client_quote_products` y persistir snapshot de taxonomía.
- Promover campos locales completos en productos/variantes (nombre, atributos/specs, HS, peso, volumen).

Plan de migración (refundacional, breaking‑changes aceptados):
1) Crear taxonomía: tablas `product_categories`, `product_subcategories`, `product_types` con `code` únicos y `reglas/defaults` JSON.
2) Alterar cotización (local):
   - `client_quote_products`: DROP `product_id` si existiera; ADD `type_id` NOT NULL; ADD `name`, `attributes` JSON, `specs` JSON, `hs_code`, `weight`, `volume`, snapshots `type_code`, `type_name`, `subcategory_code`, `category_code` (NOT NULL si aplica política estricta).
   - `client_quote_product_variants`: DROP `product_id`/`variant_id` si existieran; ADD `label`/`name`, `attributes` JSON, `specs` JSON, `hs_code`, `weight`, `volume`, `quantity` (≥1).
3) Backend: actualizar Form Requests y Actions de “create” para recibir campos locales + `type_id` obligatorio y aplicar reglas dinámicas del tipo.
4) Resources: exponer campos locales y snapshot de taxonomía; filtros por tipo/subcategoría/categoría.

Checklist de verificación (nuevo baseline)
- Crear producto de cotización requiere `type_id` y `name`; valida atributos obligatorios según `product_types.reglas`.
- Variantes no dependen de un catálogo maestro; se validan y persisten localmente con `quantity ≥ 1`.
- JSON incluye `type_code`, `type_name`, `subcategory_code`, `category_code` y datos locales (attributes/specs/HS/peso/volumen).


Este documento introduce una taxonomía global y estable (categoría → subcategoría → tipo) para clasificar productos y variantes que, a nivel de cotización, se modelan como entidades locales (Enfoque B: grupos, productos y variantes dentro de la cotización). La taxonomía habilita reglas comunes, analítica transversal y mejores defaults, sin exigir un catálogo maestro completo de ítems reutilizables.

---

## Objetivo

- Agregar un catálogo ligero (taxonomía) para clasificar productos producidos.
- Mantener productos/variantes “únicos por cotización” (sin maestro global de ítems), preservando identidad local y snapshots.
- Habilitar validaciones dinámicas, parámetros por familia y analítica por categoría.

---

## Beneficios

- Reglas por familia: atributos obligatorios, MOQs/múltiplos, lead time base, HS code sugerido, parámetros de empaque/volumétrico por tipo.
- Consistencia: nombres/jerarquía estables para reportes, permisos y dashboards.
- Velocidad: defaults por tipo aceleran la captura inicial del producto en cotización.
- Analítica transversal: márgenes, tasa de cierre, calidad y tiempos por categoría/subcategoría/tipo.
- Evolución: posibilidad de promover ciertos “tipos” a plantillas sin re‑modelar las cotizaciones existentes.

---

## Alcance

- Nuevo catálogo global de taxonomía: categorías, subcategorías y tipos.
- En Enfoque B, los productos locales de la cotización referencian un `type_id` y guardan un snapshot denormalizado de códigos/nombres de la taxonomía para estabilidad histórica.
- No se introduce un catálogo global de ítems/variantes; los productos y variantes permanecen locales a la cotización.

---

## Modelo de Datos (refundacional)

Tablas de taxonomía (globales, obligatorias):

- `product_categories`
  - Campos: `id` (uuid/ulid), `code`(string, único), `name`(string), `metadata`(JSON, opcional), `timestamps`.
- `product_subcategories`
  - Campos: `id`, `category_id`(FK), `code`, `name`, `metadata`(JSON), `timestamps`.
- `product_types`
  - Campos: `id`, `subcategory_id`(FK), `code`(único), `name`, `hs_code_sugerido`(string, opcional), `defaults`(JSON), `reglas`(JSON), `timestamps`.

Tablas locales de cotización (Enfoque B):

- `client_quote_products` (productos locales)
  - `id` (uuid/ulid PK)
  - `client_quote_id` (FK)
  - `group_id` (FK, nullable)
  - `type_id` (FK → `product_types.id`, NOT NULL)
  - `name` (string, NOT NULL)
  - `attributes` (JSON, NOT NULL; p. ej., material, capacidad)
  - `specs` (JSON, opcional; fichas/medidas)
  - `hs_code` (string, opcional)
  - `weight` (numeric, opcional), `volume` (numeric, opcional)
  - `notes` (text, opcional), `position` (int)
  - Snapshots taxonomía: `type_code`, `type_name`, `subcategory_code`, `category_code` (strings, NOT NULL)
  - Timestamps

- `client_quote_product_variants` (variantes locales)
  - `id` (uuid/ulid PK)
  - `quote_product_id` (FK → `client_quote_products.id`)
  - `label` (string, NOT NULL)
  - `attributes` (JSON, NOT NULL; p. ej., color, talla, acabado)
  - `specs` (JSON, opcional)
  - `hs_code` (string, opcional)
  - `weight` (numeric, opcional), `volume` (numeric, opcional)
  - `quantity` (int, NOT NULL, ≥1)
  - `notes` (text, opcional), `position` (int)
  - Timestamps

Claves e índices sugeridos:

- Unicidad: `product_categories.code`, `product_subcategories(code, category_id)`, `product_types.code`.
- Índices por FK: `product_subcategories.category_id`, `product_types.subcategory_id`, `client_quote_products.type_id`, `client_quote_products.client_quote_id`, `client_quote_products.group_id`.

---

## Validaciones y Reglas Dinámicas

- Reglas por tipo (`product_types.reglas` JSON):
  - Atributos requeridos por producto/variante (p. ej., para “USB 3.0”: `capacidad`, `interfaz`; para “Polera”: `talla`, `color`).
  - Restricciones: MOQ, múltiplos, lead time base, políticas de empaque (unidades por caja), factores volumétricos.
- Defaults por tipo (`product_types.defaults` JSON):
  - Sugerencias para `hs_code`, `peso/volumen` estimado, densidad, tolerancias de calidad.
- En Form Requests (refundacional): `type_id` es obligatorio; el validador lee `reglas` y aplica atributos requeridos/múltiplos/rangos.

---

## Ejemplos

- Categoría: “Bebidas & Contenedores” → Subcategoría: “Termos” → Tipo: “Termo acero 500ml”
  - Reglas: `material` en {Acero 304, Acero 201}, `capacidad`=500 ml, área de impresión obligatoria.
  - Defaults: `hs_code_sugerido`=“9617.00”, `peso`=280 g, empaque 24 u/caja.
  - Un `client_quote_product` (Termo Cliente X) referencia este `type_id`; sus variantes locales (Negro mate/Grabado láser) cumplen atributos del tipo.

- Categoría: “Electrónica” → Subcategoría: “USB” → Tipo: “USB 3.0 carcasa metal”
  - Reglas: `capacidad` obligatoria, `interfaz` ∈ {USB‑A, USB‑C}, MOQ base 100.
  - Defaults: `lead_time_base`=15 días, `peso`=30 g.

---

## Flujo y Snapshots

- En creación/edición (borrador):
  - El usuario elige `type_id` (opcional). La UI muestra atributos obligatorios y defaults.
  - Se guardan datos locales del producto y, si existe `type_id`, el snapshot de códigos/nombres.
- En cierre de cotización:
  - Se congela el snapshot (taxonomía y parámetros efectivos) junto con la cotización (consistente con el modelo de versión/gates).

---

## Impacto en API/Backend (Laravel 12 idiomático)

- Form Requests (refundacional):
  - `StoreClientQuoteProductRequest`: requiere `type_id`, `name`, `attributes`(JSON), opcionales `specs/hs_code/weight/volume/notes/position`.
  - `StoreClientQuoteProductVariantRequest`: requiere `quote_product_id`, `label`, `attributes`(JSON), `quantity ≥ 1`; opcionales `specs/hs_code/weight/volume/notes/position`.
  - Validación dinámica: servicio de taxonomía aplica `reglas` del `type_id` (atributos obligatorios/múltiplos/rangos) a producto/variantes.
- Actions/DTOs (set mínimo):
  - `CreateClientQuoteProductAction` y su DTO reciben campos locales + `type_id`; persisten snapshots de taxonomía.
  - `AddVariantToQuoteProductAction` valida y crea variantes locales (sin FKs a maestro).
  - Update/Delete simples siguen en controladores (`fill()->save()`, `delete()`), con FKs y cascadas.
- Resources:
  - `ClientQuoteProductResource`: expone `type_code`, `type_name`, `subcategory_code`, `category_code` + campos locales.

---

## Plan de Implementación (refundacional)

1) Crear taxonomía: `product_categories`, `product_subcategories`, `product_types` (semilla inicial de tipos frecuentes).
2) Alterar esquema local: eliminar FKs a `products/product_variants`; añadir campos locales obligatorios y `type_id` NOT NULL; añadir snapshots taxonómicos.
3) Adaptar backend: actualizar Form Requests “store” y Actions de creación para leer/escribir los nuevos campos; aplicar reglas dinámicas.
4) Actualizar Resources: incluir snapshots y campos locales en responses y filtros por taxonomía.
5) Documentación: reflejar nuevos contratos en ghissue1-backend.md y ejemplos de payloads.

---

## Consideraciones y Trade‑offs

- Sin catálogo maestro de ítems: menos reutilización “1 a 1”, pero suficiente estructura para reglas y analítica.
- Si en el futuro se identifica reutilización real, se puede promover un `product_type` a plantilla, sin romper cotizaciones pasadas.
- Mantener snapshots evita drift cuando cambie el nombre/código de un tipo.

---

## Diagrama (conceptual)

```mermaid
graph TD
  C[product_categories] --> S[product_subcategories]
  S --> T[product_types]
  T --> QP[client_quote_products]
  QP --> QPV[client_quote_product_variants]
```

---

## Resumen

Esta taxonomía ligera aporta orden, reglas y visibilidad transversal manteniendo la simplicidad del Enfoque B (productos/variantes locales por cotización). Mejora la calidad de datos y acelera la captura sin forzar un catálogo global de ítems. Permite crecer gradualmente hacia plantillas o maestro cuando el negocio lo demande.
