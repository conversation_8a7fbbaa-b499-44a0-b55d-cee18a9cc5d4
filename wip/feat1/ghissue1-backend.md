# Paso 1 — Backend para crear la estructura de la cotización (sin precios ni fechas) — Laravel 12

## Resumen
Implementar el backend (migraciones, modelos Eloquent, validación y controladores) para capturar la estructura de un proyecto make‑to‑order (MTO), alineado con el flujo inicial Plan‑to‑Cost. Adoptamos el Enfoque B (tablas separadas):
- `customer_quote_groups` para grupos comerciales (kits),
- `customer_quote_products` para productos (en raíz o dentro de un grupo),
- `customer_quote_product_variants` para variantes como hojas.

Esta etapa solo estructura y configura (qué y cuánto), sin precios ni hitos/fechas.

## Objetivo
- Persistir la cabecera de la cotización (`customer_quotes`) en estado `borrador`.
- Persistir la estructura separada de grupos (`client_quote_groups`) y productos (`client_quote_products`), permitiendo productos en raíz o bajo un grupo.
- Persistir configuraciones de variantes por producto (`client_quote_product_variants`) con `variant_id` (identidad estable), `quantity` (> 0) y `variant_label` (opcional).
- Exponer endpoints HTTP para crear y consultar la estructura (sin precios ni hitos/fechas) con validación en español.

## Alcance
- Migraciones: creación/alteración de tablas con FKs e índices.
- Modelos Eloquent + relaciones + enums (tipos y estados) + casts.
- Form Requests con reglas y mensajes en español.
- Controladores REST (mínimos) y rutas (`api.php`).
- Resources (JSON shape) para lectura anidada de la estructura.

Fuera de alcance: costos, márgenes, impuestos, FX, OCs, lotes, embarques y documento final.

---

## Modelo de datos (nombres exactos)

### Tabla: `customer_quotes`
- `id` bigint PK autoincrement
- `quote_number` string nullable (único cuando se asigne en paso posterior)
- `customer_name` string
- `project_id` bigint nullable
- `currency` char(3)
- `valid_until` date nullable
- `status` enum: `borrador` | `enviada` | `aprobada` | `rechazada` (default `borrador`)
- `notes` text nullable
- `created_at`/`updated_at`

Índices sugeridos: unique parcial de `quote_number` (cuando no nulo).

### Tabla: `customer_quote_groups`
- `id` bigint PK autoincrement
- `client_quote_id` bigint FK → `customer_quotes.id` (on delete cascade)
- `name` string
- `notes` text nullable
- `position` unsignedInteger (orden visual)
- `created_at`/`updated_at`

Índices: (`client_quote_id`), (`position`).

### Tabla: `customer_quote_products`
- `id` bigint PK autoincrement
- `client_quote_id` bigint FK → `customer_quotes.id` (on delete cascade)
- `group_id` bigint nullable FK → `customer_quote_groups.id` (on delete set null)
- `product_id` bigint FK → `products.id`
- `notes` text nullable
- `position` unsignedInteger (orden visual)
- `created_at`/`updated_at`

Índices: (`client_quote_id`), (`group_id`), (`product_id`), (`position`).

Reglas de integridad:
- Un producto puede estar en raíz (`group_id` NULL) o dentro de un grupo.

### Tabla: `customer_quote_product_variants`
- `id` bigint PK autoincrement
- `quote_product_id` bigint FK → `customer_quote_products.id` (on delete cascade)
- `product_id` bigint FK → `products.id`
- `variant_id` bigint FK → `product_variants.id`
- `variant_label` string nullable (p. ej., "Azul Marino, Logo Frontal")
- `quantity` unsignedInteger (> 0)
- `notes` text nullable
- `position` unsignedInteger (orden dentro del producto)
- `created_at`/`updated_at`

Índices: (`quote_product_id`), (`product_id`), (`variant_id`), (`position`).

Invariantes de negocio:
- Solo se permiten variantes asociadas a `client_quote_products`.
- `quantity >= 1`.
- `variant_id` debe pertenecer al `product_id` asociado.

---

## Modelos Eloquent (nombres exactos)

- `App\Models\ClientQuote`
  - $table = `client_quotes`
  - Relaciones: `groups(): HasMany<ClientQuoteGroup>`, `products(): HasMany<ClientQuoteProduct>`
  - Enum `status`: PHP enum `ClientQuoteStatus` (`borrador`, `enviada`, `aprobada`, `rechazada`), casted.

- `App\Models\ClientQuoteGroup`
  - $table = `client_quote_groups`
  - Relaciones:
    - `quote(): BelongsTo<ClientQuote>`
    - `products(): HasMany<ClientQuoteProduct>`

- `App\Models\ClientQuoteProduct`
  - $table = `client_quote_products`
  - Relaciones:
    - `quote(): BelongsTo<ClientQuote>`
    - `group(): BelongsTo<ClientQuoteGroup>` (nullable)
    - `product(): BelongsTo<Product>`
    - `variants(): HasMany<ClientQuoteProductVariant>`

- `App\Models\ClientQuoteProductVariant`
  - $table = `client_quote_product_variants`
  - Relaciones:
    - `quoteProduct(): BelongsTo<ClientQuoteProduct>`
    - `product(): BelongsTo<Product>`
    - `variant(): BelongsTo<ProductVariant>`

- (Referencia) `App\Models\Product` y `App\Models\ProductVariant` deben existir con PK `id` bigint.

Enums (PHP 8.1+):
- `App\Enums\ClientQuoteStatus: borrador|enviada|aprobada|rechazada`

---

## Modelos y relaciones (narrativa)

La Cotización (`client_quotes`) es el agregado raíz y nace en estado `borrador`. En esta etapa no guarda precios ni fechas; solo contexto y metadatos mínimos. La cotización contiene Grupos (`client_quote_groups`) y Productos (`client_quote_products`). Los grupos son contenedores comerciales (kits) y no admiten variantes; sirven para agrupar productos. Los productos pueden vivir en raíz o dentro de un grupo y referencian al catálogo (`products.id`).

La última capa del árbol es siempre la variante, modelada como `client_quote_product_variants`, que cuelga exclusivamente de un producto de cotización. Cada registro fija la decisión de variante (`variant_id` en `product_variants.id`) y la `quantity`. Opcionalmente se persiste `variant_label` como ayuda de lectura; la identidad real proviene de `variant_id` (que debe pertenecer al `product_id`).

Cardinalidades: una Cotización tiene muchos Grupos y muchos Productos; un Grupo tiene muchos Productos; un Producto tiene muchas Variantes. Las eliminaciones se propagan en cascada (cotización → grupos → productos → variantes). La validación garantiza invariantes: solo los productos aceptan variantes; los grupos nunca; `quantity ≥ 1`; y `variant_id ∈ product`.

Esta estructura refleja el paso inicial del flujo Plan‑to‑Cost (MTO): primero se estructura y configura la demanda (qué productos/variantes y en qué cantidades) sin introducir todavía costos, márgenes ni hitos; esas capas se agregan en etapas posteriores (sourcing, lotes, embarques y costeo).

---

## Rutas y Controladores (API)

Archivo `routes/api.php`:
- `POST   /customer-quotes` → `ClientQuoteController@store`
- `GET    /customer-quotes/{quote}` → `ClientQuoteController@show`
- `POST   /customer-quotes/{quote}/groups` → `ClientQuoteGroupController@store`
- `PATCH  /customer-quote-groups/{group}` → `ClientQuoteGroupController@update`
- `DELETE /customer-quote-groups/{group}` → `ClientQuoteGroupController@destroy`
- `POST   /customer-quotes/{quote}/products` → `ClientQuoteProductController@store`
- `PATCH  /customer-quote-products/{product}` → `ClientQuoteProductController@update`
- `DELETE /customer-quote-products/{product}` → `ClientQuoteProductController@destroy`
- `POST   /customer-quote-products/{product}/variants` → `ClientQuoteProductVariantController@store`
- `PATCH  /customer-quote-product-variants/{variant}` → `ClientQuoteProductVariantController@update`
- `DELETE /customer-quote-product-variants/{variant}` → `ClientQuoteProductVariantController@destroy`

Controladores (nombres exactos):
- `App\Http\Controllers\ClientQuoteController`
- `App\Http\Controllers\ClientQuoteGroupController`
- `App\Http\Controllers\ClientQuoteProductController`
- `App\Http\Controllers\ClientQuoteProductVariantController`

Resources (JSON):
- `App\Http\Resources\ClientQuoteResource` → cabecera + estructura anidada:
  - `groups[]` con sus `products[]` y para cada product sus `variants[]` (`variant_id`, `variant_label`, `quantity`).
  - `products[]` (productos en raíz) con sus `variants[]`.

---

## Form Requests (validación en español)

- `StoreClientQuoteRequest`
  - Reglas: `customer_name` required|string|min:1 (se acepta `client_name` como legado), `currency` required|size:3, `valid_until` date|after:today (opcional), `notes` nullable|string.
- `StoreClientQuoteGroupRequest`
  - Reglas: `name` required|string|min:1; `notes` nullable|string.
- `UpdateClientQuoteGroupRequest`
  - Editar `name`, `notes`.
- `StoreClientQuoteProductRequest`
  - Reglas: `product_id` required|integer|exists:products,id; `group_id` nullable|integer|exists:client_quote_groups,id; `notes` nullable|string.
  - Regla custom: si `group_id` se envía, debe pertenecer a la misma cotización.
- `UpdateClientQuoteProductRequest`
  - Editar `group_id` (nullable), `notes`.
- `StoreClientQuoteProductVariantRequest`
  - Reglas: `variant_id` required|integer|exists:product_variants,id; `quantity` required|integer|min:1; `product_id` required|integer|exists:products,id; `variant_label` nullable|string|min:1.
  - Reglas custom: (1) el `quote_product` asociado debe existir; (2) `variant_id` debe pertenecer al `product_id` del `quote_product`.
- `UpdateClientQuoteProductVariantRequest`
  - Editar `variant_id` (opcional), `variant_label`, `quantity`, `notes` con mismas reglas.

Mensajes: definir en `lang/es/validation.php` mensajes claros (p. ej., “La variante es obligatoria”, “La cantidad debe ser mayor que 0”, “Debe elegir un producto para este ítem”).

---

## Arquitectura idiomática (Laravel 12)

Aplicamos controladores delgados + Actions invocables, DTOs inmutables y Enums. Los controladores validan (Form Requests), mapean a DTOs y delegan a Actions. Las Actions encapsulan reglas, transacciones e idempotencia.

- Directorios y naming
  - `app/Enums/ClientQuoteStatus.php` (string backed enum)
  - `app/DTOs/*.php` (clases `readonly` con `fromRequest()`/`fromArray()`)
  - `app/Actions/ClientQuotes/*.php`, `.../ClientQuoteGroups/*.php`, `.../ClientQuoteProducts/*.php`, `.../ClientQuoteProductVariants/*.php`
  - `app/Http/Controllers/*Controller.php` (invocan Actions)

- Enums
  - `ClientQuoteStatus: string { case Borrador='borrador'; case Enviada='enviada'; case Aprobada='aprobada'; case Rechazada='rechazada'; }`

- DTOs (campos esenciales)
  - `CreateClientQuoteDto { string clientName; string currency; ?DateTimeInterface validUntil; ?string notes; }`
  - `CreateClientQuoteGroupDto { string quoteId; string name; ?string notes; ?int position; }`
  - `CreateClientQuoteProductDto { string quoteId; string productId; ?string groupId; ?string notes; ?int position; }`
  - `CreateClientQuoteProductVariantDto { string quoteProductId; string productId; string variantId; int quantity; ?string variantLabel; ?int position; }`

- Actions (firma y responsabilidad)
  - `CreateClientQuoteAction::handle(CreateClientQuoteDto): ClientQuote`
  - `CreateClientQuoteGroupAction::handle(CreateClientQuoteGroupDto): ClientQuoteGroup`
  - `CreateClientQuoteProductAction::handle(CreateClientQuoteProductDto): ClientQuoteProduct`
  - `AddVariantToQuoteProductAction::handle(CreateClientQuoteProductVariantDto): ClientQuoteProductVariant`
  - Actualizaciones y borrados simples se manejan en controladores con Eloquent (`fill()->save()`, `delete()`), apoyados en FKs y cascadas.

- Reglas en Actions
  - Transacciones (`DB::transaction`) y checks de pertenencia (group→quote, variant→product).
  - Invariantes: grupos sin variantes; productos requieren `product_id`; `variant_id ∈ product` y `quantity ≥ 1`.
  - Idempotencia opcional: aceptar `correlation_id` en DTO y usar `firstOrCreate` por hash.

- Controladores (ejemplo de uso)
  - `ClientQuoteProductVariantController@store(StoreClientQuoteProductVariantRequest $r)`:
    - `$dto = CreateClientQuoteProductVariantDto::fromRequest($r);`
    - `$variant = app(AddVariantToQuoteProductAction::class)->handle($dto);`
    - `return new ClientQuoteProductVariantResource($variant);`
  - `ClientQuoteProductController@update(UpdateClientQuoteProductRequest $r, ClientQuoteProduct $product)`:
    - `$product->fill($r->validated())->save();`
    - `return new ClientQuoteProductResource($product->refresh());`
  - `ClientQuoteGroupController@destroy(ClientQuoteGroup $group)`:
    - `$group->delete(); // cascada borra productos/variantes`

---

## Lógica de negocio

- La última capa del árbol es siempre la variante (hoja).
- Un `group` no admite variantes.
- Un `product` de cotización requiere `product_id` y puede estar en raíz o pertenecer a un `group`.
- `variant_id` debe pertenecer al `product_id` del `quote_product`.
- Antes de cerrar la etapa, todo `group` debe tener ≥ 1 `product` y todo `product` debe tener ≥ 1 `variant` (validación de cierre, no bloqueante en creación).

---

## Tareas

Migraciones
- [ ] Crear/alterar tablas `client_quotes`, `client_quote_groups`, `client_quote_products`, `client_quote_product_variants` (UUID, FKs, índices, enums).

Modelos y Enums
- [ ] `ClientQuote`, `ClientQuoteGroup`, `ClientQuoteProduct`, `ClientQuoteProductVariant` con relaciones y casts.
- [ ] Enum `ClientQuoteStatus` (borrador|enviada|aprobada|rechazada`).

Resources (JSON):
- `App\\Http\\Resources\\ClientQuoteResource` → cabecera + estructura anidada:
  - `groups[]` con sus `products[]` y para cada product sus `variants[]` (`variant_id`, `variant_label`, `quantity`).
  - `products[]` (productos en raíz) con sus `variants[]`.
  - Campos estándar: `customer_id`, `customer_name`, `currency`, `status`, `valid_until`, `notes`, etc.
  - Campos legacy (deprecados): `client_id`, `client_name` (se eliminarán en una versión futura).

Controladores y Rutas
- [ ] Endpoints descritos arriba en `api.php` con controladores correspondientes.
- [ ] `ClientQuoteResource` para lectura anidada (groups + products raíz + variants).

Validación y Localización
- [ ] Form Requests con reglas y mensajes en español.
- [ ] Mensajes en `lang/es/validation.php` y textos de error coherentes con el SOP.

Pruebas (Pest)
- [ ] Feature: crear cotización → agregar group → agregar product (en raíz y bajo group) → agregar variantes (`variant_id`) → `GET /client-quotes/{id}` refleja estructura anidada.
- [ ] Feature: validar errores (422) en pertenencia variant→product y group→quote.
- [ ] DB: aserciones de FKs y cascadas al eliminar grupos/productos/variantes.

Documentación
- [ ] Breve README/zspec indicando los endpoints y el JSON esperado.

---

## Criterios de aceptación
- Se pueden crear cotizaciones `borrador` y consultarlas vía API con su estructura anidada (sin precios ni fechas).
- Se pueden crear grupos y productos (en raíz o anidados bajo grupo), y variantes con `quantity >= 1`.
- Validaciones en español, con códigos 422 y `errors` por campo coherentes.
- Invariantes respetados: grupos sin variantes; `variant_id` pertenece al `product_id`; productos pueden estar en raíz o bajo grupo; antes de cierre, grupo con ≥1 producto y producto con ≥1 variante.
- Eliminaciones hacen cascade correctamente (borrar un grupo borra sus productos y variantes asociadas).

## Riesgos y mitigaciones
- Complejidad de anidación: limitar a un nivel (group → product) y hojas en variantes (como especificación).
- Integridad: reforzar reglas con validación y FKs; considerar CHECKs en DB donde el motor lo permita.
- Naming: mantener EXACTAMENTE los nombres definidos para evitar fricción con etapas siguientes.

---

## Estimación
1.5–2 días de desarrollo + 0.5 día de pruebas (Pest) y documentación.
