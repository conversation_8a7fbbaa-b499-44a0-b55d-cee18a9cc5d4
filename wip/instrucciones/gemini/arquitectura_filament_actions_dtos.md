# Guía de Arquitectura: Uso de Actions y DTOs con Filament

Esta guía define cuándo y cómo utilizar el patrón de Acciones de negocio y DTOs (Data Transfer Objects) en un proyecto que utiliza Filament 4 como interfaz de usuario principal.

## Principio Fundamental

Combinar Filament con Acciones y DTOs crea una arquitectura robusta, mantenible y escalable. La separación de responsabilidades es clave:

1.  **Filament (Capa de Presentación/UI)**: Se encarga de renderizar la interfaz, capturar la entrada del usuario y actuar como **punto de entrada** para las interacciones.
2.  **DTOs (Contratos de Datos)**: Actúan como un "puente" limpio y fuertemente tipado entre la capa de UI (Filament) y la lógica de negocio. Desacoplan la lógica de la fuente de datos.
3.  **Acciones (Lógica de Negocio)**: Contien<PERSON> la lógica de negocio pura, son reutilizables y fácilmente testeables de forma aislada.

---

## Reglas de Decisión

A continuación se presenta una guía para decidir qué enfoque tomar en cada caso.

### Caso 1: Usar el Patrón Completo (Filament Action -> DTO -> Custom Action)

Este debería ser el enfoque por defecto para cualquier operación que represente un **proceso de negocio fundamental**.

#### Cuándo usarlo:

-   **Lógica con efectos secundarios**: Si la acción hace más que un simple `Model::create($data)` o `Model::update($data)`. Por ejemplo, si envía notificaciones, interactúa con otras partes del sistema, despacha eventos o jobs.
-   **Lógica de negocio compleja**: Si hay condicionales, cálculos, o se deben manipular varios modelos a la vez.
-   **Necesidad de reutilización**: Si la misma lógica podría ser invocada desde otro lugar (API, CLI, jobs en cola, etc.).
-   **Operaciones críticas**: Procesos como "Finalizar una factura", "Aprobar una cotización", "Iniciar un lote de producción".
-   **Se requiere testear la lógica de forma aislada** (tests unitarios).

#### Ejemplo de Flujo:

```php
// En un Resource de Filament
use App\Actions\CreatePurchaseOrderAction;
use App\DTOs\CreatePurchaseOrderDto;
use Filament\Actions\Action;

Action::make('createPurchaseOrder')
    ->action(function (array $data) {
        // 1. La UI (Filament) entrega un array.
        
        // 2. Lo convertimos en un contrato de datos (DTO).
        $dto = new CreatePurchaseOrderDto(
            supplier_id: $data['supplier_id'],
            items: $data['items'],
            // ... otros campos
        );

        // 3. Pasamos el DTO a nuestra acción de negocio.
        // La acción de Filament actúa como un simple coordinador.
        app(CreatePurchaseOrderAction::class)->execute($dto);
    });
```

### Caso 2: Usar solo las Características de Filament

Para operaciones muy simples, el patrón completo puede ser excesivo y está bien usar solo las funcionalidades nativas de Filament.

#### Cuándo usarlo:

-   **CRUD simple y puro**: Creación o actualización de un único modelo sin lógica asociada.
-   **Acciones triviales**: Como un "toggle" para activar/desactivar un registro.
-   **Prototipado rápido**: Para construir algo rápidamente, con la intención de refactorizarlo más tarde si la lógica se complica.
-   La acción es puramente de UI (ej. redirigir, abrir un modal).

#### Ejemplo de Flujo Simple:

```php
// En un Resource de Filament
use App\Models\ProductCategory;
use Filament\Actions\Action;

Action::make('createCategory')
    ->form([
        // ... campos del formulario
    ])
    ->action(function (array $data) {
        // La lógica es tan simple que no merece una Acción o DTO.
        ProductCategory::create($data);
    });
```

---

### Tabla Resumen de Decisión

| ¿Cuándo?              | Usar Patrón Completo (Action + DTO)     | Usar solo Filament                      |
| :-------------------- | :-------------------------------------- | :-------------------------------------- |
| **Complejidad**       | Lógica de negocio compleja, múltiples pasos. | CRUD simple, 1-2 pasos triviales.       |
| **Efectos Secundarios** | Sí (emails, jobs, eventos, logs, etc.). | No, solo operaciones en la BD.          |
| **Reutilización**     | La lógica se necesita en otros lugares. | La lógica es específica de esta UI.     |
| **Testabilidad**      | Se requiere testeo unitario de la lógica. | Un test de feature es suficiente.       |
| **Ejemplos**          | `ProcesarPago`, `GenerarReporteAnual`   | `CrearTag`, `EditarNombreDeUsuario`     |
