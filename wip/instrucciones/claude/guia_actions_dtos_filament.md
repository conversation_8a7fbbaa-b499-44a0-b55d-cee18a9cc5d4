# Guía: Actions + DTOs vs Filament Puro - C<PERSON><PERSON>do Usar Cada Patrón

## **Contexto**
Con Filament 4 como interfaz principal, necesitamos criterios claros para decidir cuándo usar Action Pattern + DTOs versus aprovechar las capacidades nativas de Filament.

## **<PERSON><PERSON><PERSON>do SÍ usar Action Pattern + DTOs con Filament**

### **1. Operaciones de Negocio Complejas**
```php
// ✅ USAR: Lógica compleja con múltiples validaciones
CreatePurchaseOrderAction::handle(CreatePurchaseOrderDto $dto)
// - Validaciones de negocio
// - Transacciones DB
// - EventLog
// - Correlation IDs
// - Notificaciones
```

### **2. Operaciones Transaccionales Críticas**
```php
// ✅ USAR: Múltiples modelos + auditoría
ProcessBulkQuotationAction::handle(BulkQuotationDto $dto)
// - Crear múltiples PurchaseOrders
// - Actualizar variantes
// - Enviar emails
// - Logging completo
```

### **3. Reutilización Cross-Context**
```php
// ✅ USAR: Misma lógica en Filament + API + Jobs
GenerateQuoteReportAction::handle(QuoteReportDto $dto)
// - Usado en Filament bulk action
// - Usado en API endpoint
// - Usado en scheduled job
```

## **Cuándo NO usar Action Pattern + DTOs**

### **1. CRUD Simple de Filament**
```php
// ❌ NO USAR: Filament ya maneja esto elegantemente
// Crear/editar Customer, Supplier básico
// Form directo al modelo es suficiente
CustomerResource::form() // Directo al modelo
```

### **2. Operaciones Simples de Una Sola Entidad**
```php
// ❌ OVERKILL: Para cambios simples
$customer->update(['status' => 'active']);
// No necesita Action + DTO
```

### **3. Formularios Filament Estándar**
```php
// ❌ INNECESARIO: Filament Form ya valida
TextInput::make('name')->required()
// Form Request puede ser suficiente
```

## **Guía de Decisión Práctica**

### **Usar Actions + DTOs cuando:**
1. **Múltiples modelos involucrados** (>2 entidades)
2. **Lógica de negocio específica** (no solo CRUD)
3. **Necesitas auditoría/logging** detallado
4. **Reutilización** en múltiples contextos
5. **Operaciones asíncronas** o en background
6. **Validaciones complejas** más allá de form validation

### **No usar Actions + DTOs cuando:**
1. **CRUD directo** a una sola entidad
2. **Filament ya provee** la funcionalidad
3. **Operaciones triviales** sin lógica de negocio
4. **Formularios estándar** sin processing especial

## **Enfoque Híbrido Recomendado**

### **Nivel 1: Filament Puro (60% casos)**
```php
// Gestión básica de Customer, Supplier, ProductCategory
CustomerResource::form() → Model::create()
```

### **Nivel 2: Filament + Form Requests (25% casos)**
```php
// Validaciones más complejas pero sin lógica de negocio
CustomerResource::form() + StoreCustomerRequest
```

### **Nivel 3: Filament + Actions + DTOs (15% casos)**
```php
// Operaciones complejas, bulk actions, workflows
BulkAction::make('create_purchase_orders')
    ->action(fn($records) => CreatePurchaseOrderAction::handle(
        CreateBulkPurchaseOrderDto::from($records)
    ))
```

## **Casos Específicos del Proyecto PSS**

### **✅ USAR Actions + DTOs:**
- Bulk creation de Purchase Orders desde variantes
- Generación de reportes de cotización
- Workflow de aprobación de quotes
- Sincronización de datos entre sistemas
- Operaciones de sourcing masivas

### **❌ NO USAR Actions + DTOs:**
- CRUD de Customer/Supplier básico
- Edición de Product Categories
- Gestión de usuarios/roles básica
- Configuración de settings simples

## **Template de Decisión**

```php
// Pregúntate:
// 1. ¿Involucra >2 modelos? → Action + DTO
// 2. ¿Tiene lógica de negocio específica? → Action + DTO  
// 3. ¿Necesita ser reutilizado? → Action + DTO
// 4. ¿Es CRUD simple? → Filament puro
// 5. ¿Solo necesita validación extra? → Form Request
```

## **Ejemplos Prácticos**

### **Caso 1: Gestión de Customer (Filament Puro)**
```php
// ✅ Filament nativo es suficiente
class CustomerResource extends Resource
{
    public static function form(Schema $schema): Schema
    {
        return $schema->components([
            TextInput::make('name')->required(),
            TextInput::make('email')->email(),
            // ... más campos simples
        ]);
    }
}
```

### **Caso 2: Validaciones Complejas (Form Request)**
```php
// ✅ Filament + Form Request
class CustomerResource extends Resource
{
    protected static ?string $createFormRequest = StoreCustomerRequest::class;
    
    // StoreCustomerRequest maneja validaciones específicas
}
```

### **Caso 3: Operación de Negocio (Action + DTO)**
```php
// ✅ Action + DTO para lógica compleja
BulkAction::make('generate_purchase_orders')
    ->action(function (Collection $records) {
        $dto = CreateBulkPurchaseOrderDto::fromVariants($records);
        return CreateBulkPurchaseOrderAction::handle($dto);
    })
    ->requiresConfirmation()
    ->modalDescription('Esto creará órdenes de compra para las variantes seleccionadas...');
```

## **Checklist de Implementación**

Antes de crear un Action + DTO, verifica:

- [ ] ¿La operación involucra múltiples modelos?
- [ ] ¿Hay lógica de negocio más allá de CRUD?
- [ ] ¿Necesita auditoría/logging específico?
- [ ] ¿Se reutilizará en otros contextos?
- [ ] ¿Requiere transacciones complejas?

Si respondes "No" a todas, probablemente Filament puro es suficiente.

## **Beneficios de Este Enfoque**

1. **Simplicidad**: 60% de casos usan Filament directo
2. **Consistencia**: Criterios claros para cada patrón  
3. **Mantenibilidad**: Menos complejidad innecesaria
4. **Flexibilidad**: Actions disponibles para casos complejos
5. **Performance**: Filament optimizado para CRUD estándar

Esta guía mantiene la simplicidad de Filament para casos comunes mientras aprovecha Actions + DTOs para la complejidad real del negocio del sistema PSS.