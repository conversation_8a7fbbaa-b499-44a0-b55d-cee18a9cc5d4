# Listado de APIs, Actions y DTOs a Eliminar - **VERSIÓN REVISADA**

## **⚠️ PREÁMBULO CRÍTICO**
Este documento ha sido revisado para corregir gaps críticos identificados en la versión anterior. **NO proceder con eliminación** sin completar la **Fase 0: Auditoría Previa**.

---

## **FASE 0: AUDITORÍA PREVIA (OBLIGATORIA)**

### **🔍 Validaciones Críticas Pre-eliminación:**

#### **1. Mapeo de Dependencias**
```bash
# Buscar referencias a controllers que se van a eliminar
rg "CustomerQuoteController|SupplierController|CustomerQuoteGroupController" --type php
rg "StoreCustomerQuoteRequest|UpdateSupplierRequest" --type php
rg "/api/customer-quotes|/api/suppliers" --type php --type js --type vue
```

#### **2. Auditoría de Integraciones Externas**
- [ ] Verificar logs de nginx/apache para uso de APIs a eliminar
- [ ] Consultar con equipo de integraciones sobre sistemas externos
- [ ] Revisar documentación de APIs compartida con terceros
- [ ] Validar que no hay webhooks entrantes usando estas APIs

#### **3. Completeness de Filament Resources**
- [ ] `CustomerResource` existe y tiene CRUD completo
- [ ] `SupplierResource` existe y maneja validaciones complejas (ContactField/ContactValue)
- [ ] `CustomerQuoteResource` existe con RelationManagers (Groups, Products, Variants)
- [ ] Todas las validaciones de Form Requests están replicadas en Filament

#### **4. Feature Flags Setup**
```php
// config/features.php
return [
    'legacy_api_enabled' => env('FEATURE_LEGACY_API', true),
    'supplier_api_enabled' => env('FEATURE_SUPPLIER_API', true), 
    'quote_api_enabled' => env('FEATURE_QUOTE_API', true),
    'filament_only_mode' => env('FEATURE_FILAMENT_ONLY', false),
];
```

#### **5. Tests de Regresión**
- [ ] Crear tests que validen paridad funcional Filament vs API
- [ ] Tests de validación: Filament forms vs Form Requests  
- [ ] Tests de autorización: Filament policies vs API policies
- [ ] Tests de cálculos: Filament vs custom logic

---

## **APIs a Eliminar - RECLASIFICACIÓN**

### **🔴 ELIMINAR INMEDIATAMENTE - SAFE (12 rutas)**
```php
// Legacy routes client-* - CONFIRMADO NO HAY DEPENDENCIAS
Route::post('/client-quotes', [CustomerQuoteController::class, 'store']);
Route::get('/client-quotes/{quote}', [CustomerQuoteController::class, 'show']);
Route::post('/client-quotes/{quote}/groups', [CustomerQuoteGroupController::class, 'store']);
Route::patch('/client-quote-groups/{group}', [CustomerQuoteGroupController::class, 'update']);
Route::delete('/client-quote-groups/{group}', [CustomerQuoteGroupController::class, 'destroy']);
Route::post('/client-quotes/{quote}/products', [CustomerQuoteProductController::class, 'store']);
Route::patch('/client-quote-products/{product}', [CustomerQuoteProductController::class, 'update']);
Route::delete('/client-quote-products/{product}', [CustomerQuoteProductController::class, 'destroy']);
Route::post('/client-quote-products/{product}/variants', [CustomerQuoteProductVariantController::class, 'store']);
Route::patch('/client-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'update']);
Route::delete('/client-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'destroy']);
Route::post('/client-quotes/{quote}/purchase-orders', [PurchaseOrderController::class, 'store']);
```

### **🟡 ELIMINAR CON FEATURE FLAGS - GRADUAL (8 rutas)**
```php
// Customer Quotes CRUD - MIGRAR GRADUALMENTE
Route::post('/customer-quotes', [CustomerQuoteController::class, 'store']);
Route::put('/customer-quotes/{quote}', [CustomerQuoteController::class, 'update']);
Route::get('/customer-quotes/{quote}', [CustomerQuoteController::class, 'show']);

// Suppliers CRUD - VALIDAR INTEGRACIONES PRIMERO  
Route::apiResource('suppliers', SupplierController::class); // 5 rutas

// Quote hierarchy CRUD - POST feature flag validation
Route::post('/customer-quotes/{quote}/groups', [CustomerQuoteGroupController::class, 'store']);
Route::patch('/customer-quote-groups/{group}', [CustomerQuoteGroupController::class, 'update']);
Route::delete('/customer-quote-groups/{group}', [CustomerQuoteGroupController::class, 'destroy']);
Route::post('/customer-quotes/{quote}/products', [CustomerQuoteProductController::class, 'store']);
Route::patch('/customer-quote-products/{product}', [CustomerQuoteProductController::class, 'update']);
Route::delete('/customer-quote-products/{product}', [CustomerQuoteProductController::class, 'destroy']);
Route::post('/customer-quote-products/{product}/variants', [CustomerQuoteProductVariantController::class, 'store']);
Route::patch('/customer-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'update']);
Route::delete('/customer-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'destroy']);
```

### **✅ MANTENER DEFINITIVAMENTE - CRÍTICAS PARA INTEGRACIÓN (9+ rutas)**
```php
// Purchase Orders - CORE BUSINESS LOGIC
Route::post('/customer-quotes/{quote}/purchase-orders', [PurchaseOrderController::class, 'store']);
Route::get('/purchase-orders/{purchaseOrder}', [PurchaseOrderController::class, 'show']);
Route::post('/purchase-orders/{purchaseOrder}/items', [PurchaseOrderController::class, 'addItem']);
Route::post('/purchase-orders/{purchaseOrder}/send', [PurchaseOrderController::class, 'send']);
Route::post('/purchase-orders/{purchaseOrder}/confirm', [PurchaseOrderController::class, 'confirm']);
Route::post('/purchase-orders/{purchaseOrder}/close', [PurchaseOrderController::class, 'close']);

// Production Batches - INTEGRACIÓN PLANTA
Route::post('/purchase-order-items/{purchaseOrderItem}/batches', [ProductionBatchController::class, 'store']);
Route::get('/production-batches/{productionBatch}', [ProductionBatchController::class, 'show']);
Route::post('/production-batches/{productionBatch}/status', [ProductionBatchController::class, 'changeStatus']);

// Sourcing Summary - REPORTES COMPLEJOS
Route::get('/customer-quotes/{quote}/sourcing/summary', [SourcingSummaryController::class, 'summary']);
```

---

## **Controladores - RECLASIFICACIÓN**

### **🔴 ELIMINAR DESPUÉS DE VALIDACIÓN:**
- `app/Http/Controllers/CustomerQuoteController.php` ⚠️ **POST-AUDITORÍA**
- `app/Http/Controllers/CustomerQuoteGroupController.php` ⚠️ **POST-AUDITORÍA**
- `app/Http/Controllers/CustomerQuoteProductController.php` ⚠️ **POST-AUDITORÍA**
- `app/Http/Controllers/CustomerQuoteProductVariantController.php` ⚠️ **POST-AUDITORÍA**
- `app/Http/Controllers/SupplierController.php` ⚠️ **VALIDAR INTEGRACIONES PRIMERO**

### **✅ MANTENER DEFINITIVAMENTE:**
- `app/Http/Controllers/PurchaseOrderController.php` ✅ **CRÍTICO PARA ERP/INTEGRACIÓN**
- `app/Http/Controllers/ProductionBatchController.php` ✅ **CRÍTICO PARA PLANTA**
- `app/Http/Controllers/SourcingSummaryController.php` ✅ **REPORTES COMPLEJOS**

---

## **Form Requests - ANÁLISIS CORREGIDO**

### **🔴 ELIMINAR POST-VALIDACIÓN (6 archivos):**
- `app/Http/Requests/StoreCustomerQuoteRequest.php` ⚠️ **POST-FILAMENT-VALIDATION**
- `app/Http/Requests/UpdateCustomerQuoteRequest.php` ⚠️ **POST-FILAMENT-VALIDATION**
- `app/Http/Requests/StoreCustomerQuoteGroupRequest.php` ⚠️ **POST-FILAMENT-VALIDATION**
- `app/Http/Requests/UpdateCustomerQuoteGroupRequest.php` ⚠️ **POST-FILAMENT-VALIDATION**
- `app/Http/Requests/StoreSupplierRequest.php` ⚠️ **VALIDACIONES COMPLEJAS - VERIFICAR FILAMENT**
- `app/Http/Requests/UpdateSupplierRequest.php` ⚠️ **VALIDACIONES COMPLEJAS - VERIFICAR FILAMENT**

### **🟡 EVALUAR (4 archivos):**
- `app/Http/Requests/StoreCustomerQuoteProductRequest.php` ❓ **NO AUDITADO AÚN**
- `app/Http/Requests/UpdateCustomerQuoteProductRequest.php` ❓ **NO AUDITADO AÚN**
- `app/Http/Requests/StoreCustomerQuoteProductVariantRequest.php` ❓ **NO AUDITADO AÚN**
- `app/Http/Requests/UpdateCustomerQuoteProductVariantRequest.php` ❓ **NO AUDITADO AÚN**

### **✅ MANTENER DEFINITIVAMENTE (4 archivos):**
- `app/Http/Requests/StorePurchaseOrderRequest.php` ✅ **LÓGICA CRÍTICA ERP**
- `app/Http/Requests/StorePurchaseOrderItemRequest.php` ✅ **LÓGICA CRÍTICA ERP**
- `app/Http/Requests/StoreProductionBatchRequest.php` ✅ **LÓGICA CRÍTICA PLANTA**
- `app/Http/Requests/ChangeStatusRequest.php` ✅ **TRANSVERSAL COMPLEJO**

---

## **Actions y DTOs - ANÁLISIS REFINADO**

### **✅ MANTENER TODOS LOS ACTIONS:**
- `app/Actions/CreatePurchaseOrderAction.php` ✅ **LÓGICA DE NEGOCIO CRÍTICA**
- `app/Actions/AddItemToPurchaseOrderAction.php` ✅ **LÓGICA DE NEGOCIO CRÍTICA**
- `app/Actions/CreateProductionBatchAction.php` ✅ **LÓGICA DE NEGOCIO CRÍTICA**

### **🟡 REFACTOR GenerateCorrelationIdAction:**
```php
// OPCIÓN 1: Helper estático
class CorrelationId 
{
    public static function generate(): string 
    {
        return Str::uuid()->toString();
    }
}

// OPCIÓN 2: Trait reutilizable
trait GeneratesCorrelationIds 
{
    protected function generateCorrelationId(): string 
    {
        return Str::uuid()->toString();
    }
}
```

### **✅ MANTENER TODOS LOS DTOs:**
- `app/DTOs/Currency.php` ✅ **VALUE OBJECT FUNDAMENTAL**
- `app/DTOs/Tax.php` ✅ **CÁLCULOS DE NEGOCIO**
- `app/DTOs/CreatePurchaseOrderDto.php` ✅ **USADO POR ACTION CRÍTICA**
- `app/DTOs/CreatePurchaseOrderItemDto.php` ✅ **USADO POR ACTION CRÍTICA**
- `app/DTOs/CreateProductionBatchDto.php` ✅ **USADO POR ACTION CRÍTICA**

---

## **Livewire Components - MIGRACIÓN PRIORIZADA**

### **🔴 MIGRACIÓN PRIORITARIA:**

#### **1. Quote Show (ALTA PRIORIDAD)**
- **URL**: `http://localhost:8000/quotes/{id}`
- **Archivo**: `resources/views/livewire/quotes/show.blade.php` (294 líneas)
- **Migración**: → Enhanced `ViewCustomerQuote` page
- **Filament Components**: InfoList + Widgets + RelationManagers
- **Consideraciones**: Preservar cálculos financieros complejos

#### **2. Sourcing Plan (ALTA PRIORIDAD)**  
- **URL**: `http://localhost:8000/sourcing/quotes/{id}/sourcing-plan`
- **Archivo**: `resources/views/livewire/sourcing/sourcing-plan.blade.php` (549 líneas)
- **Migración**: → Custom `SourcingPlanPage` 
- **Filament Components**: Tables + Filters + KPI Widgets
- **Consideraciones**: Preservar filtros complejos y KPIs

#### **3. Quote Plan (MEDIA PRIORIDAD)**
- **URL**: `http://127.0.0.1:8000/sourcing/quotes/{id}/plan`  
- **Archivo**: `resources/views/livewire/sourcing/quote-plan.blade.php`
- **Migración**: → Custom Page o integración en ViewCustomerQuote
- **Consideraciones**: Evaluar si se puede integrar con #1

---

## **PLAN DE EJECUCIÓN CORREGIDO**

### **FASE 0: AUDITORÍA PREVIA (1-2 semanas)**
1. ✅ **Ejecutar validaciones críticas** listadas arriba
2. ✅ **Mapear todas las dependencias** con grep/rg
3. ✅ **Validar completeness de Filament Resources**
4. ✅ **Setup de feature flags** en todas las envs
5. ✅ **Crear tests de regresión** Filament vs API
6. ✅ **Comunicación con equipos** de integraciones

### **FASE 1: LEGACY CLEANUP (SAFE - 1 semana)**
1. Eliminar rutas `client-*` (12 rutas confirmed unused)
2. Cleanup imports relacionados
3. Validar que no se rompió nada

### **FASE 2: LIVEWIRE MIGRATION FIRST (2-3 semanas)**
1. Migrar Quote Show → ViewCustomerQuote enhanced
2. Migrar Sourcing Plan → Custom SourcingPlanPage  
3. Migrar Quote Plan → Integración o Custom Page
4. Tests de paridad funcional Livewire vs Filament
5. Eliminar rutas web y componentes Livewire solo DESPUÉS de validación

### **FASE 3: GRADUAL API ELIMINATION (3-4 semanas)**
1. **Feature flag rollout**: Deshabilitar APIs gradualmente en dev/staging
2. **Monitoreo**: Logs de APIs para detectar uso no esperado
3. **Customer Quotes CRUD**: Eliminar solo si Filament 100% funcional
4. **Suppliers CRUD**: Eliminar solo si NO hay integraciones externas
5. **Quote hierarchy**: Eliminar solo si RelationManagers completos

### **FASE 4: CLEANUP & OPTIMIZATION (1 semana)**
1. Eliminar controllers/requests/resources confirmados no usados
2. Cleanup tests obsoletos + migrar tests importantes
3. Actualizar documentación de APIs mantenidas
4. Performance review: Filament vs APIs eliminadas

### **FASE 5: MONITORING & ROLLBACK PREP (ONGOING)**
1. Monitoring de performance post-eliminación
2. Plan de rollback documentado para cada componente
3. Backup de código eliminado por X meses
4. Review post-implementación con stakeholders

---

## **ROLLBACK PLAN**

### **🔄 Estrategia de Rollback por Fase:**

#### **Git Strategy:**
```bash
# Tag before each phase for easy rollback
git tag -a "pre-phase-1-legacy-cleanup" -m "Before legacy API cleanup"
git tag -a "pre-phase-2-livewire-migration" -m "Before Livewire migration"
git tag -a "pre-phase-3-api-elimination" -m "Before API elimination"
```

#### **Feature Flag Rollback:**
```php
// Emergency rollback via env vars
FEATURE_LEGACY_API=true
FEATURE_SUPPLIER_API=true  
FEATURE_QUOTE_API=true
FEATURE_FILAMENT_ONLY=false
```

#### **Database Rollback:**
- **NO HAY CAMBIOS DE BD** en esta migración - rollback seguro
- Mantener migrations intactas durante todo el proceso

---

## **CONSIDERACIONES ADICIONALES**

### **📊 Monitoring & Analytics:**
```php
// Logging de uso de API pre-eliminación
Log::info('API_USAGE', [
    'endpoint' => request()->path(),
    'method' => request()->method(),
    'user_id' => auth()->id(),
    'timestamp' => now(),
]);
```

### **📚 Documentación de APIs Mantenidas:**
- Purchase Orders API documentation
- Production Batches API documentation  
- Sourcing Summary API documentation
- Authentication y authorization requirements
- Rate limiting y error handling

### **🔒 Security Review:**
- Verificar que eliminación de APIs no crea security holes
- Review de permissions en Filament vs APIs eliminadas
- Audit trail de cambios realizados

### **⚡ Performance Considerations:**
- Benchmark: Filament pages vs API + frontend
- Database query optimization en Filament Resources
- Caching strategy para Filament heavy queries
- Asset loading optimization post-elimination

---

## **RESUMEN EJECUTIVO CORREGIDO**

### **Impacto Cuantitativo Ajustado:**
- **12 rutas legacy** eliminadas inmediatamente (SAFE)
- **8 rutas CRUD** eliminadas gradualmente (POST-VALIDACIÓN)
- **9+ rutas críticas** MANTENIDAS (Purchase Orders, Production, Sourcing)
- **3-6 controllers** eliminados (POST-AUDITORÍA)
- **6-10 Form Requests** eliminados (POST-VALIDACIÓN)
- **3 Livewire components** migrados a Filament (~843 líneas)
- **Tests relacionados** migrados o eliminados según necesidad

### **Beneficios Esperados:**
- **Reducción controlada de complejidad** (no más 70%, más conservador)
- **Consistencia UX** en Filament interface
- **Mantenibilidad mejorada** sin sacrificar integraciones críticas
- **Risk mitigation** con feature flags y rollback plan

### **Risk Mitigation:**
- **Feature flags** para rollback inmediato
- **Auditoría previa** comprehensive
- **Tests de regresión** para validar paridad
- **Comunicación proactiva** con stakeholders
- **Migración gradual** en lugar de big bang

**CONCLUSIÓN**: Esta versión revisada prioriza **seguridad y validación** sobre velocidad de eliminación, asegurando que no se rompan integraciones críticas y manteniendo un path de rollback claro.