# Listado de APIs, Actions y DTOs a Eliminar

## **APIs a Eliminar (29 rutas)**

### **🔴 Legacy Routes - ELIMINAR INMEDIATAMENTE**
```php
// Todas las rutas client-* deprecated (12 rutas)
Route::post('/client-quotes', [CustomerQuoteController::class, 'store']);
Route::get('/client-quotes/{quote}', [CustomerQuoteController::class, 'show']);
Route::post('/client-quotes/{quote}/groups', [CustomerQuoteGroupController::class, 'store']);
Route::patch('/client-quote-groups/{group}', [CustomerQuoteGroupController::class, 'update']);
Route::delete('/client-quote-groups/{group}', [CustomerQuoteGroupController::class, 'destroy']);
Route::post('/client-quotes/{quote}/products', [CustomerQuoteProductController::class, 'store']);
Route::patch('/client-quote-products/{product}', [CustomerQuoteProductController::class, 'update']);
Route::delete('/client-quote-products/{product}', [CustomerQuoteProductController::class, 'destroy']);
Route::post('/client-quote-products/{product}/variants', [CustomerQuoteProductVariantController::class, 'store']);
Route::patch('/client-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'update']);
Route::delete('/client-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'destroy']);
Route::post('/client-quotes/{quote}/purchase-orders', [PurchaseOrderController::class, 'store']);
```

### **🔴 CRUD Completo de Suppliers (5 rutas)**
```php
Route::get('/suppliers', [SupplierController::class, 'index']);
Route::post('/suppliers', [SupplierController::class, 'store']);
Route::get('/suppliers/{supplier}', [SupplierController::class, 'show']);
Route::put('/suppliers/{supplier}', [SupplierController::class, 'update']);
Route::delete('/suppliers/{supplier}', [SupplierController::class, 'destroy']);
```

### **🔴 Customer Quotes CRUD (3 rutas)**
```php
Route::post('/customer-quotes', [CustomerQuoteController::class, 'store']);
Route::put('/customer-quotes/{quote}', [CustomerQuoteController::class, 'update']);
Route::get('/customer-quotes/{quote}', [CustomerQuoteController::class, 'show']);
```

### **🔴 Customer Quote Groups CRUD (3 rutas)**
```php
Route::post('/customer-quotes/{quote}/groups', [CustomerQuoteGroupController::class, 'store']);
Route::patch('/customer-quote-groups/{group}', [CustomerQuoteGroupController::class, 'update']);
Route::delete('/customer-quote-groups/{group}', [CustomerQuoteGroupController::class, 'destroy']);
```

### **🔴 Customer Quote Products CRUD (3 rutas)**
```php
Route::post('/customer-quotes/{quote}/products', [CustomerQuoteProductController::class, 'store']);
Route::patch('/customer-quote-products/{product}', [CustomerQuoteProductController::class, 'update']);
Route::delete('/customer-quote-products/{product}', [CustomerQuoteProductController::class, 'destroy']);
```

### **🔴 Customer Quote Product Variants CRUD (3 rutas)**
```php
Route::post('/customer-quote-products/{product}/variants', [CustomerQuoteProductVariantController::class, 'store']);
Route::patch('/customer-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'update']);
Route::delete('/customer-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'destroy']);
```

## **Controladores a Eliminar (6 archivos)**

### **🔴 Eliminar completamente:**
- `app/Http/Controllers/SupplierController.php`
- `app/Http/Controllers/CustomerQuoteController.php`
- `app/Http/Controllers/CustomerQuoteGroupController.php`
- `app/Http/Controllers/CustomerQuoteProductController.php`
- `app/Http/Controllers/CustomerQuoteProductVariantController.php`
- `app/Http/Controllers/SourcingSummaryController.php` *(evaluar si se usa en dashboard)*

## **Form Requests - Análisis Detallado (14 archivos)**

### **🔴 ELIMINAR - CRUD APIs que Filament reemplaza (10 archivos):**

#### **Customer Quote Requests:**
- `app/Http/Requests/StoreCustomerQuoteRequest.php` ❌ **ELIMINAR**
  - *Validaciones básicas (customer_name, currency, valid_until)*
  - *Reemplazado por Filament CustomerQuoteResource form validation*
- `app/Http/Requests/UpdateCustomerQuoteRequest.php` ❌ **ELIMINAR**
  - *Mismas validaciones que Store, innecesario con Filament*

#### **Customer Quote Group Requests:**
- `app/Http/Requests/StoreCustomerQuoteGroupRequest.php` ❌ **ELIMINAR**
  - *Reemplazado por Filament GroupsRelationManager*
- `app/Http/Requests/UpdateCustomerQuoteGroupRequest.php` ❌ **ELIMINAR**
  - *Reemplazado por Filament GroupsRelationManager*

#### **Customer Quote Product Requests:**
- `app/Http/Requests/StoreCustomerQuoteProductRequest.php` ❌ **ELIMINAR**
  - *Reemplazado por Filament ProductsRelationManager*
- `app/Http/Requests/UpdateCustomerQuoteProductRequest.php` ❌ **ELIMINAR**
  - *Reemplazado por Filament ProductsRelationManager*

#### **Customer Quote Product Variant Requests:**
- `app/Http/Requests/StoreCustomerQuoteProductVariantRequest.php` ❌ **ELIMINAR**
  - *Reemplazado por Filament VariantsRelationManager*
- `app/Http/Requests/UpdateCustomerQuoteProductVariantRequest.php` ❌ **ELIMINAR**
  - *Reemplazado por Filament VariantsRelationManager*

#### **Supplier Requests:**
- `app/Http/Requests/StoreSupplierRequest.php` ❌ **ELIMINAR**
  - *Validaciones complejas con custom rules (ValidContactField, ValidContactValue)*
  - *SupplierResource en Filament ya maneja estas validaciones*
- `app/Http/Requests/UpdateSupplierRequest.php` ❌ **ELIMINAR**
  - *Filament SupplierResource reemplaza completamente*

### **✅ MANTENER - Requests para lógica de negocio (4 archivos):**

#### **Purchase Orders (mantener temporalmente):**
- `app/Http/Requests/StorePurchaseOrderRequest.php` ✅ **MANTENER**
  - *Validaciones: supplier_id, currency, incoterm, terms*
  - *Usada por CreatePurchaseOrderAction que se mantiene*
  - *Evaluación futura: si PO API se mantiene para integración ERP*

- `app/Http/Requests/StorePurchaseOrderItemRequest.php` ✅ **MANTENER**
  - *Validaciones para items de purchase orders*
  - *Usada por AddItemToPurchaseOrderAction*

#### **Production Batches (mantener temporalmente):**
- `app/Http/Requests/StoreProductionBatchRequest.php` ✅ **MANTENER**
  - *Validaciones para production batches*
  - *Usada por CreateProductionBatchAction*
  - *Evaluación futura: si Production API se mantiene para integración planta*

#### **Status Changes (transversal):**
- `app/Http/Requests/ChangeStatusRequest.php` ✅ **MANTENER**
  - *Validación dinámica de estados por entidad (po, batch)*
  - *Lógica de negocio compleja con match expressions*
  - *Reutilizada por múltiples controllers para cambios de estado*

## **API Resources a Eliminar**

### **🔴 Eliminar si existen:**
- `app/Http/Resources/CustomerQuoteResource.php`
- `app/Http/Resources/CustomerQuoteGroupResource.php`
- `app/Http/Resources/CustomerQuoteProductResource.php`
- `app/Http/Resources/CustomerQuoteProductVariantResource.php`
- `app/Http/Resources/SupplierResource.php`

## **Actions Potencialmente Redundantes**

### **🟡 Evaluar si están duplicadas con Filament:**

#### **Mantener (utilizadas por múltiples contextos):**
- `app/Actions/CreatePurchaseOrderAction.php` ✅ **MANTENER** - Usada por Filament + posibles integraciones
- `app/Actions/AddItemToPurchaseOrderAction.php` ✅ **MANTENER** - Lógica de negocio compleja
- `app/Actions/CreateProductionBatchAction.php` ✅ **MANTENER** - Integración con planta
- `app/Actions/GenerateCorrelationIdAction.php` ✅ **MANTENER** - Utilidad cross-sistema

#### **Evaluar eliminación:**
*Nota: No se identificaron Actions específicas para CRUD que duplicaran funcionalidad de Filament*

## **DTOs a Evaluar**

### **✅ Mantener (usados por Actions que se conservan):**
- `app/DTOs/Currency.php` ✅ **MANTENER** - Value Object fundamental
- `app/DTOs/Tax.php` ✅ **MANTENER** - Cálculos de negocio
- `app/DTOs/CreatePurchaseOrderDto.php` ✅ **MANTENER** - Usado por Action conservada
- `app/DTOs/CreatePurchaseOrderItemDto.php` ✅ **MANTENER** - Usado por Action conservada
- `app/DTOs/CreateProductionBatchDto.php` ✅ **MANTENER** - Usado por Action conservada

### **🟡 DTOs que podrían existir y evaluar:**
*Buscar en el proyecto si existen DTOs como:*
- `CreateCustomerQuoteDto.php` ❓ **BUSCAR Y ELIMINAR si existe**
- `UpdateCustomerQuoteDto.php` ❓ **BUSCAR Y ELIMINAR si existe**
- `CreateSupplierDto.php` ❓ **BUSCAR Y ELIMINAR si existe**

## **Concerns y Traits API a Eliminar**

### **🔴 Eliminar si solo son usados por controllers eliminados:**
- `app/Http/Concerns/HandlesApiErrors.php` ❓ **EVALUAR** - Si solo lo usan controllers eliminados

## **Tests a Eliminar**

### **🔴 Buscar y eliminar tests relacionados:**
- `tests/Feature/*ControllerTest.php` para controllers eliminados
- `tests/Feature/Api/*` tests de endpoints eliminados
- `tests/Unit/*RequestTest.php` para Form Requests eliminados

## **Migrations Potenciales a Revisar**

### **🟡 No eliminar, pero revisar dependencias:**
- Verificar que no haya migrations que dependan de funcionalidades API eliminadas
- Mantener todas las migrations de estructura de BD

## **Livewire Components a Migrar a Filament**

### **🔴 Componentes con URLs específicas a eliminar:**

#### **Vista de Cotización (Quote Show):**
- **URL**: `http://localhost:8000/quotes/1`
- **Archivo**: `resources/views/livewire/quotes/show.blade.php` (294 líneas)
- **Migración**: → Filament `ViewCustomerQuote` page con InfoList + RelationManagers
- **Funcionalidad**: Vista completa de cotización con cálculos financieros

#### **Plan de Sourcing:**
- **URL**: `http://localhost:8000/sourcing/quotes/1/sourcing-plan`
- **Archivo**: `resources/views/livewire/sourcing/sourcing-plan.blade.php` (549 líneas)
- **Migración**: → Filament Custom Page con tablas optimizadas
- **Funcionalidad**: Dashboard de POs, items, lotes de producción con filtros

#### **Quote Plan:**
- **URL**: `http://127.0.0.1:8000/sourcing/quotes/1/plan`  
- **Archivo**: `resources/views/livewire/sourcing/quote-plan.blade.php`
- **Migración**: → Filament Custom Page o integración en ViewCustomerQuote
- **Funcionalidad**: Planificación de cotizaciones

### **Estrategia de Migración:**

#### **Paso 1: ViewCustomerQuote Enhancement**
```php
// Reemplazar quotes/show.blade.php
class ViewCustomerQuote extends ViewRecord
{
    // InfoList para datos de cotización
    // RelationManagers para groups, products, variants
    // Custom widgets para cálculos financieros
}
```

#### **Paso 2: Custom Sourcing Page**
```php
// Reemplazar sourcing-plan.blade.php
class SourcingPlanPage extends Page
{
    // Tablas de Purchase Orders, Items, Batches
    // Filtros avanzados
    // KPI widgets
}
```

#### **Paso 3: Routes Cleanup**
```php
// Eliminar de routes/web.php:
Route::get('/quotes/{quote}', QuoteShowComponent::class);
Route::get('/sourcing/quotes/{quote}/sourcing-plan', SourcingPlanComponent::class);
Route::get('/sourcing/quotes/{quote}/plan', QuotePlanComponent::class);
```

### **🟡 Beneficios de la migración:**
- **Consistencia UX**: Todo en Filament design system
- **Performance**: Tablas optimizadas de Filament vs custom Livewire
- **Mantenibilidad**: Menos código custom que mantener
- **Features**: Acceso a filtering, sorting, bulk actions nativas

## **Archivos de Configuración**

### **🟡 Limpiar referencias:**
- Revisar si hay referencias a controllers eliminados en:
  - `routes/api.php` (limpiar imports)
  - `routes/web.php` (eliminar rutas Livewire migradas)
  - Service providers
  - Bindings de DI

## **Resumen de Eliminación**

### **Impacto cuantitativo:**
- **29 rutas API** eliminadas (~90% de APIs actuales)
- **6 controllers** eliminados completamente  
- **10 Form Requests** eliminados (de 14 total)
- **5+ API Resources** eliminados
- **3 Livewire components** migrados a Filament (~843 líneas)
- **3 rutas web** eliminadas (quotes, sourcing-plan, plan)
- **Tests relacionados** eliminados

### **Form Requests - Balance final:**
- **Eliminados**: 10 Form Requests (CRUD básico que Filament reemplaza)
- **Mantenidos**: 4 Form Requests (lógica de negocio específica)

### **Archivos que permanecen:**
- Actions de negocio (4 archivos)
- DTOs de negocio (5 archivos) 
- Value Objects (Money, etc.)
- Purchase Orders APIs (temporalmente)
- Production Batches APIs (temporalmente)

### **Beneficios esperados:**
- **Reducción de complejidad**: 70% menos código API
- **Mantenimiento simplificado**: Una sola fuente de verdad (Filament)
- **Consistencia UX**: Todo en Filament interface
- **Performance**: Menos overhead de serialización API

## **Plan de Ejecución**

### **Fase 1: Legacy Cleanup**
1. Eliminar todas las rutas `client-*` 
2. Eliminar controllers asociados no utilizados

### **Fase 2: CRUD APIs**
1. Eliminar CRUD de Suppliers (ya tiene Filament resource)
2. Eliminar CRUD de Customer Quotes (Filament resources + custom pages)
3. Eliminar Form Requests y Resources asociados

### **Fase 3: Livewire to Filament Migration**
1. Migrar `quotes/show.blade.php` → Filament ViewCustomerQuote page
2. Migrar `sourcing-plan.blade.php` → Filament Custom SourcingPlanPage
3. Migrar `quote-plan.blade.php` → Filament Custom Page o integración
4. Eliminar rutas web correspondientes
5. Actualizar navegación y links internos

### **Fase 4: Cleanup**
1. Limpiar imports y referencias
2. Eliminar tests obsoletos
3. Actualizar documentación

### **Fase 5: Evaluación Final**
1. Evaluar Purchase Orders APIs según necesidad de integración ERP
2. Evaluar Production Batches según integración planta
3. Mantener solo APIs realmente necesarias para integraciones externas

## **Roadmap de Migración Livewire → Filament**

### **Prioridad Alta (Impacto inmediato):**
1. **Quote Show** (`/quotes/{id}`) → ViewCustomerQuote con InfoList personalizado
2. **Sourcing Plan** (`/sourcing/quotes/{id}/sourcing-plan`) → Custom Page con widgets

### **Prioridad Media:**
3. **Quote Plan** (`/sourcing/quotes/{id}/plan`) → Integración o Custom Page

### **Consideraciones técnicas:**
- Mantener URLs amigables en Filament con `slug()` methods
- Migrar cálculos financieros a InfoList entries computadas
- Convertir filtros custom a Filament filter forms
- Preservar funcionalidad de KPIs como Filament widgets