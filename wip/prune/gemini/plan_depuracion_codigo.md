# Plan de Depuración de Código: Actions, DTOs, APIs y Form Requests

Este documento es una plantilla para analizar y decidir qué componentes de código (Actions, DTOs, APIs, Form Requests) son candidatos para ser eliminados. La eliminación de código debe ser un proceso cuidadoso basado en un análisis y no en una suposición.

**Advertencia:** No elimine código a menos que tenga una alta certeza de que es obsoleto y no se utiliza en ningún ciclo de negocio, por infrecuente que sea.

## Metodología Propuesta

1.  **Análisis Estático**: Usa tu IDE ("Find Usages") para encontrar referencias directas a la clase. La ausencia de referencias es un primer indicador, pero no es concluyente.
2.  **Análisis Dinámico (Logging)**: Añade un `Log::info('Clase X utilizada');` en el constructor o método principal de la clase sospechosa. Despliégalo en `staging` o `producción` y monitoriza los logs durante un ciclo de negocio completo (ej. un mes).
3.  **Cobertura de Tests**: Ejecuta tu suite de tests con cobertura de código. Un 0% de cobertura es una señal de alerta.

---

## Checklist de Componentes a Investigar

A continuación se listan los componentes encontrados en el proyecto. Úsalos como punto de partida para tu investigación.

### Actions (`app/Actions`)

- [ ] `AddItemToPurchaseOrderAction.php`
- [ ] `CreateProductionBatchAction.php`
- [ ] `CreatePurchaseOrderAction.php`
- [ ] `GenerateCorrelationIdAction.php`

### DTOs (`app/DTOs`)

- [ ] `CreateProductionBatchDto.php`
- [ ] `CreatePurchaseOrderDto.php`
- [ ] `CreatePurchaseOrderItemDto.php`
- [ ] `Currency.php`
- [ ] `Tax.php`

### Form Requests (`app/Http/Requests`)

*Para analizar los Form Requests, busca sus usos en los métodos de los controladores. Si un Form Request no es inyectado en ningún método de un controlador, es un fuerte candidato a ser obsoleto.*

- [ ] `ChangeStatusRequest.php`
- [ ] `StoreCustomerQuoteGroupRequest.php`
- [ ] `StoreCustomerQuoteProductRequest.php`
- [ ] `StoreCustomerQuoteProductVariantRequest.php`
- [ ] `StoreCustomerQuoteRequest.php`
- [ ] `StoreProductionBatchRequest.php`
- [ ] `StorePurchaseOrderItemRequest.php`
- [ ] `StorePurchaseOrderRequest.php`
- [ ] `StoreSupplierRequest.php`
- [ ] `UpdateCustomerQuoteGroupRequest.php`
- [ ] `UpdateCustomerQuoteProductRequest.php`
- [ ] `UpdateCustomerQuoteProductVariantRequest.php`
- [ ] `UpdateCustomerQuoteRequest.php`
- [ ] `UpdateSupplierRequest.php`

### APIs (`routes/api.php`)

*El análisis de las rutas de API requiere revisar el archivo `routes/api.php` y aplicar la misma lógica para cada endpoint. ¿Qué cliente (móvil, 3rd party) lo está usando? ¿Muestran los logs del servidor que este endpoint ha recibido tráfico?*

---

## Tabla de Análisis y Decisión

Usa esta tabla para documentar tus hallazgos.

| Archivo Candidato | Análisis Estático (Usos) | Análisis Dinámico (Logs) | Cobertura de Tests | Notas de Contexto | Decisión Final (Mantener/Eliminar) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `app/Actions/EjemploAction.php` | 0 usos encontrados | No hay logs en 1 mes | 0% | Creado para feature X que se descartó | **Eliminar** |
| `app/DTOs/EjemploDto.php` | 1 uso en un test | Logs vistos en cierre de mes | 80% | Usado solo para el reporte mensual | **Mantener** |
| | | | | | |