# Plan de Eliminación: APIs, Actions y DTOs

## Contexto
Este documento identifica qué elementos del proyecto deben eliminarse para optimizar la arquitectura hacia un uso prioritario de Filament 4, eliminando redundancias y simplificando el código.

## Criterios de Eliminación

### ✅ MANTENER cuando:
- **Operaciones complejas** (>3 modelos, transacciones, lógica de negocio >20 líneas)
- **Reutilización múltiple** (API + Filament + Console)
- **Integraciones externas** (webhooks, sistemas ERP)
- **Exportaciones/reportes** (PDFs, Excel)
- **Value Objects** (Currency, Tax, Money)

### ❌ ELIMINAR cuando:
- **CRUD simple** (crear, leer, actualizar, eliminar básico)
- **Operaciones de UI** que Filament maneja nativamente
- **Solo uso interno** (admin panel)
- **Operación de 1-2 líneas** de código

---

## 🗑️ ELEMENTOS A ELIMINAR

### 1. CONTROLLERS (APIs) - ELIMINAR COMPLETAMENTE

#### ❌ CRUD Simple (Filament maneja mejor):
```
app/Http/Controllers/SupplierController.php
app/Http/Controllers/CustomerQuoteController.php
app/Http/Controllers/CustomerQuoteGroupController.php
app/Http/Controllers/CustomerQuoteProductController.php
app/Http/Controllers/CustomerQuoteProductVariantController.php
```

**Razón**: Filament Resources manejan automáticamente todo el CRUD con validación, autorización y UI.

#### ❌ Operaciones de Estado Simple:
```
app/Http/Controllers/PurchaseOrderController.php
├── Método send() - Solo cambia status a 'enviada'
├── Método confirm() - Solo cambia status a 'confirmada'  
└── Método close() - Solo cambia status a 'cerrada'
```

**Razón**: Cambios de estado simples que Filament Actions manejan mejor.

### 2. RUTAS API - ELIMINAR

#### ❌ CRUD Routes:
```php
// Eliminar de routes/api.php:
Route::apiResource('suppliers', SupplierController::class);
Route::post('/customer-quotes', [CustomerQuoteController::class, 'store']);
Route::put('/customer-quotes/{quote}', [CustomerQuoteController::class, 'update']);
Route::get('/customer-quotes/{quote}', [CustomerQuoteController::class, 'show']);
Route::post('/customer-quotes/{quote}/groups', [CustomerQuoteGroupController::class, 'store']);
Route::patch('/customer-quote-groups/{group}', [CustomerQuoteGroupController::class, 'update']);
Route::delete('/customer-quote-groups/{group}', [CustomerQuoteGroupController::class, 'destroy']);
Route::post('/customer-quotes/{quote}/products', [CustomerQuoteProductController::class, 'store']);
Route::patch('/customer-quote-products/{product}', [CustomerQuoteProductController::class, 'update']);
Route::delete('/customer-quote-products/{product}', [CustomerQuoteProductController::class, 'destroy']);
Route::post('/customer-quote-products/{product}/variants', [CustomerQuoteProductVariantController::class, 'store']);
Route::patch('/customer-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'update']);
Route::delete('/customer-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'destroy']);
```

#### ❌ Operaciones de Estado Simple:
```php
// Eliminar de routes/api.php:
Route::post('/purchase-orders/{purchaseOrder}/send', [PurchaseOrderController::class, 'send']);
Route::post('/purchase-orders/{purchaseOrder}/confirm', [PurchaseOrderController::class, 'confirm']);
Route::post('/purchase-orders/{purchaseOrder}/close', [PurchaseOrderController::class, 'close']);
```

### 3. FORM REQUESTS - ELIMINAR

#### ❌ Requests para CRUD Simple:
```
app/Http/Requests/StoreCustomerQuoteRequest.php
app/Http/Requests/UpdateCustomerQuoteRequest.php
app/Http/Requests/StoreCustomerQuoteGroupRequest.php
app/Http/Requests/UpdateCustomerQuoteGroupRequest.php
app/Http/Requests/StoreCustomerQuoteProductRequest.php
app/Http/Requests/UpdateCustomerQuoteProductRequest.php
app/Http/Requests/StoreCustomerQuoteProductVariantRequest.php
app/Http/Requests/UpdateCustomerQuoteProductVariantRequest.php
```

**Razón**: Filament maneja la validación automáticamente en los Resources.

### 4. ACTIONS - ELIMINAR

#### ❌ Operación Simple:
```
app/Actions/GenerateCorrelationIdAction.php
```

**Razón**: Solo genera hash SHA256, no es lógica de negocio compleja. Se puede mover a un helper o método estático.

---

## ✅ ELEMENTOS A MANTENER

### 1. CONTROLLERS - MANTENER

#### ✅ Operaciones Complejas:
```
app/Http/Controllers/PurchaseOrderController.php
├── Método store() - Usa CreatePurchaseOrderAction + DTO
└── Método addItem() - Usa AddItemToPurchaseOrderAction + DTO

app/Http/Controllers/ProductionBatchController.php
├── Método store() - Usa CreateProductionBatchAction + DTO
└── Método changeStatus() - Lógica de negocio compleja

app/Http/Controllers/SourcingSummaryController.php
└── Método summary() - Reporte complejo con cálculos
```

### 2. ACTIONS - MANTENER

#### ✅ Operaciones Transaccionales:
```
app/Actions/CreatePurchaseOrderAction.php
├── Transacción de DB
├── Validaciones de negocio
├── Logging de eventos
└── Múltiples modelos involucrados

app/Actions/AddItemToPurchaseOrderAction.php
├── Validaciones de pertenencia
├── Lógica de negocio compleja
└── Transacciones

app/Actions/CreateProductionBatchAction.php
├── Validaciones de cantidades
├── Transacciones
└── Logging de eventos
```

### 3. DTOs - MANTENER

#### ✅ Para Operaciones Complejas:
```
app/DTOs/CreatePurchaseOrderDto.php
app/DTOs/CreatePurchaseOrderItemDto.php
app/DTOs/CreateProductionBatchDto.php
```

#### ✅ Value Objects:
```
app/DTOs/Currency.php
app/DTOs/Tax.php
```

### 4. RUTAS API - MANTENER

#### ✅ Operaciones Complejas:
```php
// Mantener en routes/api.php:
Route::post('/customer-quotes/{quote}/purchase-orders', [PurchaseOrderController::class, 'store']);
Route::post('/purchase-orders/{purchaseOrder}/items', [PurchaseOrderController::class, 'addItem']);
Route::post('/purchase-order-items/{purchaseOrderItem}/batches', [ProductionBatchController::class, 'store']);
Route::get('/production-batches/{productionBatch}', [ProductionBatchController::class, 'show']);
Route::post('/production-batches/{productionBatch}/status', [ProductionBatchController::class, 'changeStatus']);
Route::get('/customer-quotes/{quote}/sourcing/summary', [SourcingSummaryController::class, 'summary']);
```

---

## 📊 RESUMEN DE ELIMINACIÓN

### Archivos a Eliminar: **17 archivos**
- **5 Controllers** (CRUD simple)
- **8 Form Requests** (validación que Filament maneja)
- **1 Action** (operación simple)
- **3 métodos** en PurchaseOrderController (cambios de estado)

### Rutas a Eliminar: **15+ rutas**
- Todas las rutas CRUD de suppliers, quotes, groups, products, variants
- Rutas de cambio de estado simple (send, confirm, close)

### Archivos a Mantener: **8 archivos**
- **3 Controllers** (operaciones complejas)
- **3 Actions** (lógica de negocio transaccional)
- **5 DTOs** (operaciones complejas + value objects)

---

## 🎯 BENEFICIOS ESPERADOS

### Reducción de Código:
- **-17 archivos** menos que mantener
- **-15+ rutas** menos en API
- **-500+ líneas** de código menos

### Mejoras de Arquitectura:
- **Filament maneja** todo el CRUD automáticamente
- **Código más limpio** y mantenible
- **Mejor performance** (menos endpoints)
- **Menos duplicación** de validaciones
- **UI consistente** en toda la aplicación

### Facilidad de Mantenimiento:
- **Un solo lugar** para CRUD (Filament Resources)
- **Validaciones centralizadas** en Filament
- **Autorización automática** con Filament Shield
- **UI responsive** automática

---

## 🚀 PLAN DE IMPLEMENTACIÓN

### Fase 1: Preparación
1. Verificar que todos los Filament Resources estén completos
2. Asegurar que las validaciones estén en los Resources
3. Confirmar que la autorización funcione con Filament Shield

### Fase 2: Eliminación
1. Eliminar controllers de CRUD simple
2. Eliminar form requests correspondientes
3. Eliminar rutas API redundantes
4. Eliminar GenerateCorrelationIdAction

### Fase 3: Limpieza
1. Actualizar tests para usar Filament en lugar de APIs
2. Limpiar imports no utilizados
3. Actualizar documentación

### Fase 4: Validación
1. Ejecutar suite completa de tests
2. Verificar funcionalidad en Filament
3. Confirmar que no hay regresiones

---

## 📝 NOTAS IMPORTANTES

- **Backup**: Hacer backup completo antes de eliminar
- **Tests**: Actualizar tests para usar Filament en lugar de APIs
- **Documentación**: Actualizar documentación de APIs
- **Integraciones**: Verificar que no haya integraciones externas usando las APIs eliminadas

---

*Documento generado el: $(date)*
*Proyecto: Laravel 12 + Filament 4*
*Objetivo: Optimización arquitectural hacia Filament-first*
