# Cantidad de unidades en kits/grupos (pendiente)

Decisión: pendiente de confirmación de negocio. Este documento resume pros/cons y opciones de modelado para decidir si guardar explícitamente la cantidad del kit/grupo (`client_quote_groups.quantity`) o derivarla de las variantes.

## Pros de guardar `quantity`
- Semántica clara: el kit se vende N veces (mejor para ventas/costos/logística).
- UX más simple: edición/lectura en un solo campo.
- Pricing/costeo directos: multiplicadores sin sumar variantes.
- Reportes/rendimiento: evita agregaciones por variante.
- Validación: puede forzar uniformidad entre componentes.

## Contras
- Desnormalización: riesgo de inconsistencia con sumas de variantes.
- Casos mixtos: componentes con cantidades distintas complican reglas.
- Sincronización: cambiar `quantity` ¿recalcula variantes o solo valida?
- API/UI más complejas para manejar desalineaciones.

## Opciones de modelado
- Campo opcional `client_quote_groups.quantity` (nullable):
  - null ⇒ grupo “mixto” (se deriva de variantes).
  - >0 ⇒ grupo “uniforme” (N unidades). Validar o normalizar cantidades de variantes.
- Alternativa: `structure_mode` enum `uniform|mixed` + `quantity` (solo en `uniform`).
- Vista: si `quantity` no es null, mostrar “· N unidades”; si es null e inferimos uniformidad, mostrar derivado; si no, marcar “mixto”.

## Próximos pasos sugeridos
- Alinear con pricing/ops si kits son repetibles por norma.
- Si se aprueba, agregar columna `quantity` (nullable) y validar en Store/Update de grupos.
