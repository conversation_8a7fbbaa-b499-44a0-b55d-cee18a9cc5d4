# Paso 5 — Sourcing y Producción (alineado a taxonomía y modelo refundacional)

## Resumen
Convertir la demanda estructurada en la cotización (variantes locales: `customer_quote_product_variants`) en Órdenes de Compra (OCs) y Lotes de Producción, con idempotencia, trazabilidad y reconciliación por variante. No aborda costeo ni logística aún.

Dependencias: `zspec/TAXONOMIA_PRODUCTOS_CATALOGO_LIGERO.md` (productos y variantes locales con `type_id` obligatorio en productos, atributos/specs locales y snapshots de taxonomía).

---

## Objetivo
- Crear OCs e ítems a partir de hojas del árbol (variantes locales de producto de cotización).
- Crear lotes de producción derivados de ítems de OC (permite splits).
- Mantener reconciliación: variante ↔ ítems de OC ↔ lotes (sin diferencias).
- Registrar eventos (Registro de Eventos) y respetar estados/gates.

Fuera de alcance: costos, márgenes, impuestos, FX, embarques y distribución.

---

## Modelo de datos (propuesta mínima)

- `suppliers`
  - Campos: `id`(uuid/ulid), `code`(único), `name`, `contact`(JSON opcional), `active`(bool), `notes`, `timestamps`.

- `purchase_orders` (OCs)
  - Campos: `id`, `quote_id`(FK → `customer_quotes.id`), `supplier_id`(FK), `currency`(ISO 4217), `incoterm`(string), `terms`(JSON), `status`(borrador/enviada/confirmada/cerrada), `correlation_id`(string, único por quote), `notes`, `timestamps`.

- `purchase_order_items`
  - Campos: `id`, `purchase_order_id`(FK), `quote_product_variant_id`(FK → `customer_quote_product_variants.id`), `quantity`(int >0), `unit_price`(numeric, nullable), `lead_time_days`(int, nullable), `correlation_id`(string, único por OC), `notes`, `timestamps`.

- `production_batches`
  - Campos: `id`, `purchase_order_item_id`(FK), `quantity`(int >0), `planned_start`(datetime, nullable), `planned_finish`(datetime, nullable), `status`(borrador/planificado/en_produccion/completado), `pool_state`(available/consumed), `correlation_id`(string, único por OC Item), `notes`, `timestamps`.

- `event_log` (Registro de Eventos)
  - Campos: `id`, `entity`(enum: supplier|po|po_item|batch), `entity_id`, `action`, `actor_id`(nullable), `payload`(JSON), `created_at`.

Constraints sugeridos
- CHECKs: `quantity > 0`; `status` válidos; `pool_state` en {available, consumed}.
- Índices: `purchase_orders(quote_id, supplier_id, status)`, `purchase_order_items(purchase_order_id)`, `production_batches(purchase_order_item_id, status, pool_state)`.

---

## Estados y Gates

- OC: `borrador → enviada → confirmada → cerrada`.
- Lote: `borrador → planificado → en_produccion → completado`.
- Gate de cierre de Paso 5 (para la cotización):
  - Todas las variantes locales asignadas 100% a ítems de OC (sin faltantes ni sobrantes).
  - Todos los ítems de OC cubiertos 100% por lotes (reconciliación en cero).
  - OCs al menos `confirmadas` (o criterio que acordemos como mínimo operativo).

---

## Endpoints (API)

- Suppliers (mínimo)
  - `POST   /suppliers` → crear proveedor.
  - `GET    /suppliers` → listar.

- OCs
  - `POST   /customer-quotes/{quote}/purchase-orders` → crear OC.
  - `POST   /purchase-orders/{po}/items` → agregar ítem de OC (variante + cantidad).
  - `POST   /purchase-orders/{po}/send` → marcar `enviada`.
  - `POST   /purchase-orders/{po}/confirm` → marcar `confirmada`.
  - `POST   /purchase-orders/{po}/close` → marcar `cerrada`.

- Lotes
  - `POST   /purchase-order-items/{poi}/batches` → crear lote(s) para un ítem.
  - `POST   /production-batches/{batch}/status` → cambiar estado (transiciones válidas).

- Resumen
  - `GET    /customer-quotes/{quote}/sourcing/summary` → matriz de reconciliación y estados.

---

## Form Requests (validación)

- `StorePurchaseOrderRequest`
  - Reglas: `supplier_id` required|exists, `currency` size:3, `incoterm` string|min:2, `terms` json, `notes` string nullable.

- `StorePurchaseOrderItemRequest`
  - Reglas: `quote_product_variant_id` required|uuid|exists:customer_quote_product_variants,id (pertenece a `quote`), `quantity` integer|min:1, `unit_price` numeric nullable, `lead_time_days` integer nullable.
  - Regla custom: no exceder la cantidad no asignada de esa variante en la cotización.

- `StoreProductionBatchRequest`
  - Reglas: `quantity` integer|min:1, `planned_start`/`planned_finish` date nullable, `notes` string nullable.
  - Regla custom: Σ(lotes).quantity = `purchase_order_item.quantity` (permitir múltiples llamadas si se usa idempotencia por `correlation_id`).

- `ChangeStatusRequest`
  - Reglas: `status` in([...]) según entidad; validar transición permitida.

---

## Idempotencia y Reconciliación

- `correlation_id` (hash determinista):
  - OC: hash(normalized(quote_id, supplier_id, currency, incoterm, terms)).
  - OC Item: hash(OC.id, quote_product_variant_id, quantity).
  - Lote: hash(PO Item.id, quantity, split_key opcional).
- Regla: si existe entidad con el mismo `correlation_id` y estado compatible → reusar; no duplicar.
- Reconciliación:
  - Por variante: Σ(OC Items.quantity) = Σ(Batches.quantity) = cantidad_asignada_para_variante.
  - El endpoint de resumen reporta diferencias por variante y por OC.

---

## Actions/DTOs (mínimo set)

- `CreatePurchaseOrderAction::handle(CreatePurchaseOrderDto): PurchaseOrder`
- `AddItemToPurchaseOrderAction::handle(CreatePurchaseOrderItemDto): PurchaseOrderItem`
- `CreateProductionBatchAction::handle(CreateProductionBatchDto): ProductionBatch`

Actualizaciones simples (estados, notas) se manejan en controladores con Eloquent (`fill()->save()`), auditando en `event_log`.

---

## Payloads de ejemplo

- Crear OC
```json
{
  "supplierId": "SUP-ULID",
  "currency": "USD",
  "incoterm": "FOB",
  "terms": {"payment": "30/70", "remarks": "QA AQL 2.5"},
  "notes": "Priorizar línea termo"
}
```

- Agregar ítem a OC
```json
{
  "quoteProductVariantId": "QPV-ULID",
  "quantity": 200,
  "unitPrice": 1.85,
  "leadTimeDays": 18,
  "notes": "Color negro mate"
}
```

- Crear lote para un ítem
```json
{
  "quantity": 120,
  "plannedStart": "2025-10-01T00:00:00Z",
  "plannedFinish": "2025-10-10T00:00:00Z",
  "notes": "Split por capacidad"
}
```

---

## Criterios de aceptación
- Se pueden crear OCs por cotización, agregar ítems de OC desde variantes locales y dividir en lotes.
- Idempotencia: repetir la misma operación (mismo input lógico) no duplica OCs/ítems/lotes.
- Reconciliación en cero: Σ(OC Items) = Σ(Batches) por variante; el resumen lo refleja.
- Flujo de estados válido (OC y Lote); `event_log` registra transiciones.
- Validaciones evitan sobre‑asignaciones y transiciones inválidas.

---

## Tareas
- Migraciones: `suppliers`, `purchase_orders`, `purchase_order_items`, `production_batches`, `event_log`.
- Actions + DTOs de creación (3) con transacciones e idempotencia.
- Endpoints y Form Requests con validación (pertenencia, cantidades, estados, idempotencia).
- Resumen de reconciliación (consulta optimizada por variante).
- Pruebas (Pest): crear OC + ítems + lotes; idempotencia; reconciliación; estados; event_log.

---

## Notas de alineamiento con el modelo refundacional
- Las hojas del árbol son `client_quote_product_variants.id` (no `product_variant` global).
- Las reglas de tipo/atributos provienen de la taxonomía aplicada en Paso 2 (no se recalculan aquí).
- Este paso solo estructura sourcing/producción; costeo/logística se abordan en pasos siguientes.
