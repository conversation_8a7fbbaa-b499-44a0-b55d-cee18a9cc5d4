services:
  postgres:
    image: postgres:17.6-alpine
    container_name: smart_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: laravel_smart
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: secret
    ports:
      - "55432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U app -d app"]
      interval: 5s
      timeout: 5s
      retries: 10
volumes:
  pgdata:
