<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('customer_quote_groups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_quote_id')->constrained('customer_quotes')->cascadeOnDelete();
            $table->string('name');
            $table->text('notes')->nullable();
            $table->unsignedInteger('position')->default(0);
            $table->timestamps();

            $table->index('position');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customer_quote_groups');
    }
};
