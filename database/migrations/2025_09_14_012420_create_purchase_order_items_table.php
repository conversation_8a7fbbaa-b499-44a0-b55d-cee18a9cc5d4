<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_order_id')->constrained('purchase_orders');
            $table->foreignId('quote_product_variant_id')->constrained('customer_quote_product_variants');
            $table->integer('quantity');
            $table->decimal('unit_price', 10, 2)->nullable();
            $table->integer('lead_time_days')->nullable();
            $table->string('correlation_id')->unique();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['purchase_order_id']);
            $table->index(['quote_product_variant_id']);
            $table->index(['correlation_id']);
        });

        // Add CHECK constraint (PostgreSQL syntax)
        if (DB::getDriverName() === 'pgsql') {
            DB::statement('ALTER TABLE purchase_order_items ADD CONSTRAINT purchase_order_items_quantity_check CHECK (quantity > 0)');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_order_items');
    }
};
