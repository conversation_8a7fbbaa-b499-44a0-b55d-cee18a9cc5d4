<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_quote_products', function (Blueprint $table) {
            // Campo para indicar cuántas unidades de este producto van por kit
            $table->integer('units_per_kit')->nullable()->after('position');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_quote_products', function (Blueprint $table) {
            $table->dropColumn('units_per_kit');
        });
    }
};
