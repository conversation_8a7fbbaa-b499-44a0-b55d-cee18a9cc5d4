<?php

use App\Enums\CustomerQuoteStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('customer_quotes', function (Blueprint $table) {
            $table->id();
            $table->string('quote_number')->nullable()->unique();
            $table->string('customer_name');
            $table->unsignedBigInteger('project_id')->nullable();
            $table->char('currency', 3);
            $table->date('valid_until')->nullable();
            $table->enum('status', array_column(CustomerQuoteStatus::cases(), 'value'))->default(CustomerQuoteStatus::Borrador->value);
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customer_quotes');
    }
};
