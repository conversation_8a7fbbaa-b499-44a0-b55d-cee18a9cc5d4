<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('product_categories', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->json('metadata')->nullable();
            $table->timestamps();
        });

        Schema::create('product_subcategories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('product_categories')->cascadeOnDelete();
            $table->string('code');
            $table->string('name');
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->unique(['category_id', 'code']);
        });

        Schema::create('product_types', function (Blueprint $table) {
            $table->id();
            $table->foreignId('subcategory_id')->constrained('product_subcategories')->cascadeOnDelete();
            $table->string('code')->unique();
            $table->string('name');
            $table->string('hs_code_sugerido')->nullable();
            $table->json('defaults')->nullable();
            $table->json('reglas')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('product_types');
        Schema::dropIfExists('product_subcategories');
        Schema::dropIfExists('product_categories');
    }
};

