<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_batches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('purchase_order_item_id')->constrained('purchase_order_items');
            $table->integer('quantity');
            $table->datetime('planned_start')->nullable();
            $table->datetime('planned_finish')->nullable();
            $table->enum('status', ['borrador', 'planificado', 'en_produccion', 'completado'])->default('borrador');
            $table->enum('pool_state', ['available', 'consumed'])->default('available');
            $table->string('correlation_id')->unique();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['purchase_order_item_id', 'status', 'pool_state']);
            $table->index(['correlation_id']);
        });

        // Add CHECK constraint (PostgreSQL syntax)
        if (DB::getDriverName() === 'pgsql') {
            DB::statement('ALTER TABLE production_batches ADD CONSTRAINT production_batches_quantity_check CHECK (quantity > 0)');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_batches');
    }
};
