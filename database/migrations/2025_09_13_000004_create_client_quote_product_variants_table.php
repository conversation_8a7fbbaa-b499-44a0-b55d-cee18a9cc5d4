<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('customer_quote_product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quote_product_id')->constrained('customer_quote_products')->cascadeOnDelete();
            $table->string('label');
            $table->json('attributes');
            $table->json('specs')->nullable();
            $table->string('hs_code')->nullable();
            $table->decimal('weight', 14, 3)->nullable();
            $table->decimal('volume', 14, 3)->nullable();
            $table->unsignedInteger('quantity');
            $table->char('price_currency', 3)->default('CLP');
            $table->decimal('fx_rate_to_quote', 18, 8)->nullable();
            $table->text('notes')->nullable();
            $table->unsignedInteger('position')->default(0);
            $table->timestamps();

            $table->index('position');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customer_quote_product_variants');
    }
};
