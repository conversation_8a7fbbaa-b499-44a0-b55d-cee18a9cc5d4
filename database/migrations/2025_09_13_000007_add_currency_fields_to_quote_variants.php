<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('customer_quote_product_variants', function (Blueprint $table) {
            if (! Schema::hasColumn('customer_quote_product_variants', 'price_currency')) {
                $table->char('price_currency', 3)->default('CLP')->after('quantity');
            }
            if (! Schema::hasColumn('customer_quote_product_variants', 'fx_rate_to_quote')) {
                $table->decimal('fx_rate_to_quote', 18, 8)->nullable()->after('price_currency');
            }
        });
    }

    public function down(): void
    {
        Schema::table('customer_quote_product_variants', function (Blueprint $table) {
            if (Schema::hasColumn('customer_quote_product_variants', 'fx_rate_to_quote')) {
                $table->dropColumn('fx_rate_to_quote');
            }
            if (Schema::hasColumn('customer_quote_product_variants', 'price_currency')) {
                $table->dropColumn('price_currency');
            }
        });
    }
};
