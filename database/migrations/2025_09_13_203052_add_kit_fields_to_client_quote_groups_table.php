<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_quote_groups', function (Blueprint $table) {
            // Campos para manejar kits con precio unitario fijo
            $table->integer('quantity')->default(1)->after('position');
            $table->bigInteger('unit_price_minor')->nullable()->after('quantity');
            $table->string('price_currency', 3)->nullable()->after('unit_price_minor');
            $table->decimal('fx_rate_to_quote', 8, 4)->default(1)->after('price_currency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_quote_groups', function (Blueprint $table) {
            $table->dropColumn([
                'quantity',
                'unit_price_minor',
                'price_currency',
                'fx_rate_to_quote',
            ]);
        });
    }
};
