<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('customer_quote_product_variants', function (Blueprint $table) {
            // Drop old FKs to products/variants if present
            if (Schema::hasColumn('customer_quote_product_variants', 'variant_id')) {
                $table->dropConstrainedForeignId('variant_id');
            }
            if (Schema::hasColumn('customer_quote_product_variants', 'product_id')) {
                $table->dropConstrainedForeignId('product_id');
            }

            // Rename variant_label -> label (or add if missing)
            if (Schema::hasColumn('customer_quote_product_variants', 'variant_label') && ! Schema::hasColumn('customer_quote_product_variants', 'label')) {
                $table->renameColumn('variant_label', 'label');
            } elseif (! Schema::hasColumn('customer_quote_product_variants', 'label')) {
                $table->string('label')->after('quote_product_id');
            }

            // Local fields
            if (! Schema::hasColumn('customer_quote_product_variants', 'attributes')) {
                $table->json('attributes')->after('label');
            }
            if (! Schema::hasColumn('customer_quote_product_variants', 'specs')) {
                $table->json('specs')->nullable()->after('attributes');
            }
            if (! Schema::hasColumn('customer_quote_product_variants', 'hs_code')) {
                $table->string('hs_code')->nullable()->after('specs');
            }
            if (! Schema::hasColumn('customer_quote_product_variants', 'weight')) {
                $table->decimal('weight', 14, 3)->nullable()->after('hs_code');
            }
            if (! Schema::hasColumn('customer_quote_product_variants', 'volume')) {
                $table->decimal('volume', 14, 3)->nullable()->after('weight');
            }

            // Ensure quantity exists and has min validation at application layer
            // pricing fields already exist in prior migrations
        });
    }

    public function down(): void
    {
        Schema::table('customer_quote_product_variants', function (Blueprint $table) {
            foreach (['volume', 'weight', 'hs_code', 'specs', 'attributes'] as $col) {
                if (Schema::hasColumn('customer_quote_product_variants', $col)) {
                    $table->dropColumn($col);
                }
            }
            if (Schema::hasColumn('customer_quote_product_variants', 'label') && ! Schema::hasColumn('customer_quote_product_variants', 'variant_label')) {
                $table->renameColumn('label', 'variant_label');
            }
            // Restore old FKs if base tables exist
            if (! Schema::hasColumn('customer_quote_product_variants', 'product_id') && Schema::hasTable('products')) {
                $table->foreignId('product_id')->constrained('products');
            }
            if (! Schema::hasColumn('customer_quote_product_variants', 'variant_id') && Schema::hasTable('product_variants')) {
                $table->foreignId('variant_id')->constrained('product_variants');
            }
        });
    }
};
