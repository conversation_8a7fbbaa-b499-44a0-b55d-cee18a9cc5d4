<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('customer_quote_products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_quote_id')->constrained('customer_quotes')->cascadeOnDelete();
            $table->foreignId('group_id')->nullable()->constrained('customer_quote_groups')->nullOnDelete();
            $table->foreignId('type_id')->constrained('product_types');
            $table->string('name');
            $table->json('attributes');
            $table->json('specs')->nullable();
            $table->string('hs_code')->nullable();
            $table->decimal('weight', 14, 3)->nullable();
            $table->decimal('volume', 14, 3)->nullable();
            $table->string('type_code')->nullable();
            $table->string('type_name')->nullable();
            $table->string('subcategory_code')->nullable();
            $table->string('category_code')->nullable();
            $table->text('notes')->nullable();
            $table->unsignedInteger('position')->default(0);
            $table->timestamps();

            $table->index('position');
            $table->index('type_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customer_quote_products');
    }
};
