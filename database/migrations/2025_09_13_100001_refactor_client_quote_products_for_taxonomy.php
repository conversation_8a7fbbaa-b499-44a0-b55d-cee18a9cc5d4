<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('customer_quote_products', function (Blueprint $table) {
            // Drop old FK to products if present
            if (Schema::hasColumn('customer_quote_products', 'product_id')) {
                $table->dropConstrainedForeignId('product_id');
            }

            // New taxonomy & local fields
            if (! Schema::hasColumn('customer_quote_products', 'type_id')) {
                $table->foreignId('type_id')->after('group_id')->constrained('product_types');
            }
            if (! Schema::hasColumn('customer_quote_products', 'name')) {
                $table->string('name')->after('type_id');
            }
            if (! Schema::hasColumn('customer_quote_products', 'attributes')) {
                $table->json('attributes')->after('name');
            }
            if (! Schema::hasColumn('customer_quote_products', 'specs')) {
                $table->json('specs')->nullable()->after('attributes');
            }
            if (! Schema::hasColumn('customer_quote_products', 'hs_code')) {
                $table->string('hs_code')->nullable()->after('specs');
            }
            if (! Schema::hasColumn('customer_quote_products', 'weight')) {
                $table->decimal('weight', 14, 3)->nullable()->after('hs_code');
            }
            if (! Schema::hasColumn('customer_quote_products', 'volume')) {
                $table->decimal('volume', 14, 3)->nullable()->after('weight');
            }

            // Snapshots
            if (! Schema::hasColumn('customer_quote_products', 'type_code')) {
                $table->string('type_code')->nullable()->after('volume');
            }
            if (! Schema::hasColumn('customer_quote_products', 'type_name')) {
                $table->string('type_name')->nullable()->after('type_code');
            }
            if (! Schema::hasColumn('customer_quote_products', 'subcategory_code')) {
                $table->string('subcategory_code')->nullable()->after('type_name');
            }
            if (! Schema::hasColumn('customer_quote_products', 'category_code')) {
                $table->string('category_code')->nullable()->after('subcategory_code');
            }

            // Index might already exist if column was created in base migration
        });
    }

    public function down(): void
    {
        Schema::table('customer_quote_products', function (Blueprint $table) {
            // Revert local/taxonomy fields (keep notes/position as they were)
            foreach (['category_code', 'subcategory_code', 'type_name', 'type_code', 'volume', 'weight', 'hs_code', 'specs', 'attributes', 'name', 'type_id'] as $col) {
                if (Schema::hasColumn('customer_quote_products', $col)) {
                    $table->dropColumn($col);
                }
            }

            // Restore product_id only if products table exists
            if (! Schema::hasColumn('customer_quote_products', 'product_id') && Schema::hasTable('products')) {
                $table->foreignId('product_id')->constrained('products');
            }
        });
    }
};
