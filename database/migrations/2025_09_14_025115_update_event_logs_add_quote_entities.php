<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            // Para PostgreSQL, necesitamos recrear la columna enum
            DB::statement('ALTER TABLE event_logs DROP CONSTRAINT IF EXISTS event_logs_entity_check');
            DB::statement('ALTER TABLE event_logs ALTER COLUMN entity TYPE VARCHAR(255)');
            DB::statement("ALTER TABLE event_logs ADD CONSTRAINT event_logs_entity_check CHECK (entity IN ('supplier', 'po', 'po_item', 'batch', 'quote', 'quote_product', 'quote_variant', 'quote_group'))");
        } else {
            // Para SQLite y otros motores, simplemente recrear la tabla
            Schema::table('event_logs', function (Blueprint $table) {
                $table->string('entity')->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $driver = DB::getDriverName();

        if ($driver === 'pgsql') {
            // Revertir al enum original
            DB::statement('ALTER TABLE event_logs DROP CONSTRAINT IF EXISTS event_logs_entity_check');
            DB::statement('ALTER TABLE event_logs ALTER COLUMN entity TYPE VARCHAR(255)');
            DB::statement("ALTER TABLE event_logs ADD CONSTRAINT event_logs_entity_check CHECK (entity IN ('supplier', 'po', 'po_item', 'batch'))");
        } else {
            // Para SQLite, no necesitamos hacer nada especial
            Schema::table('event_logs', function (Blueprint $table) {
                $table->string('entity')->change();
            });
        }
    }
};
