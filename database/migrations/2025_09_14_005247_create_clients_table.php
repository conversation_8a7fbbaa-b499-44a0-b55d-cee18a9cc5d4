<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique()->nullable();
            $table->string('phone')->nullable();
            $table->string('company')->nullable();
            $table->string('tax_id')->nullable(); // RUT, CUIT, etc.
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country', 2)->nullable(); // ISO 3166-1 alpha-2
            $table->string('postal_code')->nullable();
            $table->string('contact_person')->nullable();
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Para datos adicionales específicos del cliente
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Índices
            $table->index(['name']);
            $table->index(['email']);
            $table->index(['company']);
            $table->index(['country']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
