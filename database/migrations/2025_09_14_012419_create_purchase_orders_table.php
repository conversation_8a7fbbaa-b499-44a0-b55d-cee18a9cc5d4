<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quote_id')->constrained('customer_quotes');
            $table->foreignId('supplier_id')->constrained('suppliers');
            $table->string('currency', 3);
            $table->string('incoterm');
            $table->json('terms')->nullable();
            $table->enum('status', ['borrador', 'enviada', 'confirmada', 'cerrada'])->default('borrador');
            $table->string('correlation_id')->unique();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['quote_id', 'supplier_id', 'status']);
            $table->index(['correlation_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase_orders');
    }
};
