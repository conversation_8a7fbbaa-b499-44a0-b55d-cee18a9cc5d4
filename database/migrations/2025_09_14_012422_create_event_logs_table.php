<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_logs', function (Blueprint $table) {
            $table->id();
            $table->enum('entity', ['supplier', 'po', 'po_item', 'batch']);
            $table->unsignedBigInteger('entity_id');
            $table->string('action');
            $table->foreignId('actor_id')->nullable()->constrained('users');
            $table->json('payload')->nullable();
            $table->timestamp('created_at');

            $table->index(['entity', 'entity_id']);
            $table->index(['actor_id']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_logs');
    }
};
