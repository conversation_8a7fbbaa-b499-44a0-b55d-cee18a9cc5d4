<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('customer_quote_product_variants', function (Blueprint $table) {
            if (! Schema::hasColumn('customer_quote_product_variants', 'unit_price_minor')) {
                $table->unsignedBigInteger('unit_price_minor')->default(0)->after('fx_rate_to_quote');
            }
            if (Schema::hasColumn('customer_quote_product_variants', 'unit_price')) {
                $table->dropColumn('unit_price');
            }
        });
    }

    public function down(): void
    {
        Schema::table('customer_quote_product_variants', function (Blueprint $table) {
            if (! Schema::hasColumn('customer_quote_product_variants', 'unit_price')) {
                $table->decimal('unit_price', 12, 2)->nullable()->after('quantity');
            }
            if (Schema::hasColumn('customer_quote_product_variants', 'unit_price_minor')) {
                $table->dropColumn('unit_price_minor');
            }
        });
    }
};
