<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customer_quote_product_variants', function (Blueprint $table) {
            // Hacer que los campos de precio permitan valores null para componentes de kit
            $table->bigInteger('unit_price_minor')->nullable()->change();
            $table->string('price_currency', 3)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_quote_product_variants', function (Blueprint $table) {
            // Revertir a not null (solo si no hay datos null)
            $table->bigInteger('unit_price_minor')->nullable(false)->change();
            $table->string('price_currency', 3)->nullable(false)->change();
        });
    }
};
