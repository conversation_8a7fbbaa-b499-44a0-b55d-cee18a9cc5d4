<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('customer_quotes', function (Blueprint $table) {
            if (! Schema::hasColumn('customer_quotes', 'country')) {
                $table->char('country', 2)->nullable()->after('currency');
            }
        });
    }

    public function down(): void
    {
        Schema::table('customer_quotes', function (Blueprint $table) {
            if (Schema::hasColumn('customer_quotes', 'country')) {
                $table->dropColumn('country');
            }
        });
    }
};
