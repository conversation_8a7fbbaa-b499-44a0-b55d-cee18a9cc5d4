<?php

namespace Database\Seeders;

use App\Actions\AddItemToPurchaseOrderAction;
use App\Actions\CreateProductionBatchAction;
use App\Actions\CreatePurchaseOrderAction;
use App\Actions\GenerateCorrelationIdAction;
use App\DTOs\CreateProductionBatchDto;
use App\DTOs\CreatePurchaseOrderDto;
use App\DTOs\CreatePurchaseOrderItemDto;
use App\Models\CustomerQuote;
use App\Models\CustomerQuoteProductVariant;
use App\Models\Supplier;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CompleteQuoteSeeder extends Seeder
{
    public function run(): void
    {
        DB::transaction(function () {
            // Obtener la cotización existente
            $quote = CustomerQuote::query()->where('quote_number', 'COT-25-098')->first();

            if (! $quote) {
                $this->command->error('No se encontró la cotización COT-25-098. Ejecuta primero TemplateQuoteSeeder.');

                return;
            }

            // Crear suppliers
            $suppliers = $this->createSuppliers();

            // Crear órdenes de compra para cada supplier
            $this->createPurchaseOrders($quote, $suppliers);

            $this->command->info('✅ Seeder completado: Suppliers, órdenes de compra y lotes de producción creados.');
        });
    }

    private function createSuppliers(): array
    {
        $suppliers = [];

        // Supplier 1: Proveedor de memorias USB
        $suppliers['usb'] = Supplier::query()->firstOrCreate(
            ['code' => 'SUP-USB-001'],
            [
                'name' => 'Memorias Digitales S.A.',
                'contact' => [
                    'email' => '<EMAIL>',
                    'phone' => '+56 2 2345 6789',
                    'address' => 'Av. Providencia 1234, Santiago',
                    'contact_person' => 'María González',
                ],
                'active' => true,
                'notes' => 'Especialista en memorias USB y dispositivos de almacenamiento. Tiempo de entrega: 15-20 días.',
            ]
        );

        // Supplier 2: Proveedor de termos
        $suppliers['termo'] = Supplier::query()->firstOrCreate(
            ['code' => 'SUP-TERMO-002'],
            [
                'name' => 'Termos Premium Ltda.',
                'contact' => [
                    'email' => '<EMAIL>',
                    'phone' => '+56 2 3456 7890',
                    'address' => 'Camino a Melipilla 567, Santiago',
                    'contact_person' => 'Carlos Mendoza',
                ],
                'active' => true,
                'notes' => 'Fabricante de termos de acero inoxidable. Tiempo de entrega: 25-30 días.',
            ]
        );

        // Supplier 3: Proveedor de gorras
        $suppliers['gorra'] = Supplier::query()->firstOrCreate(
            ['code' => 'SUP-GORRA-003'],
            [
                'name' => 'Textiles Corporativos S.A.',
                'contact' => [
                    'email' => '<EMAIL>',
                    'phone' => '+56 2 4567 8901',
                    'address' => 'Zona Industrial 789, Valparaíso',
                    'contact_person' => 'Ana Rodríguez',
                ],
                'active' => true,
                'notes' => 'Especialista en gorras y textiles corporativos. Tiempo de entrega: 20-25 días.',
            ]
        );

        // Supplier 4: Proveedor de muebles
        $suppliers['muebles'] = Supplier::query()->firstOrCreate(
            ['code' => 'SUP-MUEBLES-004'],
            [
                'name' => 'Muebles Industriales del Sur',
                'contact' => [
                    'email' => '<EMAIL>',
                    'phone' => '+56 2 5678 9012',
                    'address' => 'Ruta 5 Sur Km 45, Rancagua',
                    'contact_person' => 'Roberto Silva',
                ],
                'active' => true,
                'notes' => 'Fabricante de muebles de melamina y sillas industriales. Tiempo de entrega: 30-35 días.',
            ]
        );

        // Supplier 5: Proveedor de toldos
        $suppliers['toldos'] = Supplier::query()->firstOrCreate(
            ['code' => 'SUP-TOLDOS-005'],
            [
                'name' => 'Toldos y Lonas del Pacífico',
                'contact' => [
                    'email' => '<EMAIL>',
                    'phone' => '+56 2 6789 0123',
                    'address' => 'Av. del Mar 234, Viña del Mar',
                    'contact_person' => 'Patricia Vega',
                ],
                'active' => true,
                'notes' => 'Especialista en toldos, lonas y textiles publicitarios. Tiempo de entrega: 15-20 días.',
            ]
        );

        $this->command->info('✅ Suppliers creados: '.count($suppliers).' proveedores');

        return $suppliers;
    }

    private function createPurchaseOrders(CustomerQuote $quote, array $suppliers): void
    {
        // Obtener variantes de productos de la cotización
        $variants = CustomerQuoteProductVariant::query()
            ->whereHas('quoteProduct.quote', fn ($q) => $q->where('id', $quote->id))
            ->with(['quoteProduct'])
            ->get();

        // Agrupar variantes por tipo de producto para asignar suppliers
        $variantsByType = $this->groupVariantsByType($variants);

        // Crear órdenes de compra
        $this->createUsbPurchaseOrder($quote, $suppliers['usb'], $variantsByType['usb'] ?? []);
        $this->createTermoPurchaseOrder($quote, $suppliers['termo'], $variantsByType['termo'] ?? []);
        $this->createGorraPurchaseOrder($quote, $suppliers['gorra'], $variantsByType['gorra'] ?? []);
        $this->createMueblesPurchaseOrder($quote, $suppliers['muebles'], $variantsByType['muebles'] ?? []);
        $this->createToldosPurchaseOrder($quote, $suppliers['toldos'], $variantsByType['toldos'] ?? []);
    }

    private function groupVariantsByType($variants): array
    {
        $grouped = [];

        foreach ($variants as $variant) {
            $typeCode = $variant->quoteProduct->type_code;

            if (str_contains($typeCode, 'USB')) {
                $grouped['usb'][] = $variant;
            } elseif (str_contains($typeCode, 'TERMO')) {
                $grouped['termo'][] = $variant;
            } elseif (str_contains($typeCode, 'GORRA')) {
                $grouped['gorra'][] = $variant;
            } elseif (str_contains($typeCode, 'MESA') || str_contains($typeCode, 'SILLA')) {
                $grouped['muebles'][] = $variant;
            } elseif (str_contains($typeCode, 'TOLDO')) {
                $grouped['toldos'][] = $variant;
            }
        }

        return $grouped;
    }

    private function createUsbPurchaseOrder(CustomerQuote $quote, Supplier $supplier, array $variants): void
    {
        if (empty($variants)) {
            return;
        }

        $correlationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrder(
            $quote->id,
            $supplier->id,
            'CLP',
            'FOB',
            ['Pago 30 días', 'Garantía 1 año']
        );

        $dto = new CreatePurchaseOrderDto(
            quoteId: $quote->id,
            supplierId: $supplier->id,
            currency: 'CLP',
            incoterm: 'FOB',
            terms: ['Pago 30 días', 'Garantía 1 año'],
            status: 'pending',
            notes: 'Orden para memorias USB del Kit Ejecutivo',
            correlationId: $correlationId
        );

        $purchaseOrder = app(CreatePurchaseOrderAction::class)->handle($dto);

        // Crear items para cada variante
        foreach ($variants as $variant) {
            $itemCorrelationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrderItem(
                $purchaseOrder->id,
                $variant->id,
                $variant->quantity
            );

            $itemDto = new CreatePurchaseOrderItemDto(
                purchaseOrderId: $purchaseOrder->id,
                quoteProductVariantId: $variant->id,
                quantity: $variant->quantity,
                unitPrice: 8500.00, // Precio unitario estimado
                leadTimeDays: 18,
                notes: "Memoria USB {$variant->label}",
                correlationId: $itemCorrelationId
            );

            $item = app(AddItemToPurchaseOrderAction::class)->handle($itemDto);

            // Crear lote de producción
            $this->createProductionBatch($item, $variant->quantity);
        }

        $this->command->info("✅ Orden de compra creada para {$supplier->name}: {$purchaseOrder->id}");
    }

    private function createTermoPurchaseOrder(CustomerQuote $quote, Supplier $supplier, array $variants): void
    {
        if (empty($variants)) {
            return;
        }

        $correlationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrder(
            $quote->id,
            $supplier->id,
            'CLP',
            'FOB',
            ['Pago 30 días', 'Garantía 2 años']
        );

        $dto = new CreatePurchaseOrderDto(
            quoteId: $quote->id,
            supplierId: $supplier->id,
            currency: 'CLP',
            incoterm: 'FOB',
            terms: ['Pago 30 días', 'Garantía 2 años'],
            status: 'pending',
            notes: 'Orden para termos del Kit Ejecutivo y producto individual',
            correlationId: $correlationId
        );

        $purchaseOrder = app(CreatePurchaseOrderAction::class)->handle($dto);

        foreach ($variants as $variant) {
            $itemCorrelationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrderItem(
                $purchaseOrder->id,
                $variant->id,
                $variant->quantity
            );

            $itemDto = new CreatePurchaseOrderItemDto(
                purchaseOrderId: $purchaseOrder->id,
                quoteProductVariantId: $variant->id,
                quantity: $variant->quantity,
                unitPrice: 12000.00, // Precio unitario estimado
                leadTimeDays: 28,
                notes: "Termo {$variant->label}",
                correlationId: $itemCorrelationId
            );

            $item = app(AddItemToPurchaseOrderAction::class)->handle($itemDto);
            $this->createProductionBatch($item, $variant->quantity);
        }

        $this->command->info("✅ Orden de compra creada para {$supplier->name}: {$purchaseOrder->id}");
    }

    private function createGorraPurchaseOrder(CustomerQuote $quote, Supplier $supplier, array $variants): void
    {
        if (empty($variants)) {
            return;
        }

        $correlationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrder(
            $quote->id,
            $supplier->id,
            'CLP',
            'FOB',
            ['Pago 30 días', 'Garantía 6 meses']
        );

        $dto = new CreatePurchaseOrderDto(
            quoteId: $quote->id,
            supplierId: $supplier->id,
            currency: 'CLP',
            incoterm: 'FOB',
            terms: ['Pago 30 días', 'Garantía 6 meses'],
            status: 'pending',
            notes: 'Orden para gorras corporativas ProStyle',
            correlationId: $correlationId
        );

        $purchaseOrder = app(CreatePurchaseOrderAction::class)->handle($dto);

        foreach ($variants as $variant) {
            $itemCorrelationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrderItem(
                $purchaseOrder->id,
                $variant->id,
                $variant->quantity
            );

            $itemDto = new CreatePurchaseOrderItemDto(
                purchaseOrderId: $purchaseOrder->id,
                quoteProductVariantId: $variant->id,
                quantity: $variant->quantity,
                unitPrice: 7500.00, // Precio unitario estimado
                leadTimeDays: 22,
                notes: "Gorra {$variant->label}",
                correlationId: $itemCorrelationId
            );

            $item = app(AddItemToPurchaseOrderAction::class)->handle($itemDto);
            $this->createProductionBatch($item, $variant->quantity);
        }

        $this->command->info("✅ Orden de compra creada para {$supplier->name}: {$purchaseOrder->id}");
    }

    private function createMueblesPurchaseOrder(CustomerQuote $quote, Supplier $supplier, array $variants): void
    {
        if (empty($variants)) {
            return;
        }

        $correlationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrder(
            $quote->id,
            $supplier->id,
            'CLP',
            'FOB',
            ['Pago 30 días', 'Garantía 1 año', 'Instalación incluida']
        );

        $dto = new CreatePurchaseOrderDto(
            quoteId: $quote->id,
            supplierId: $supplier->id,
            currency: 'CLP',
            incoterm: 'FOB',
            terms: ['Pago 30 días', 'Garantía 1 año', 'Instalación incluida'],
            status: 'pending',
            notes: 'Orden para muebles del Conjunto de Terraza',
            correlationId: $correlationId
        );

        $purchaseOrder = app(CreatePurchaseOrderAction::class)->handle($dto);

        foreach ($variants as $variant) {
            $itemCorrelationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrderItem(
                $purchaseOrder->id,
                $variant->id,
                $variant->quantity
            );
            $unitPrice = str_contains($variant->quoteProduct->name, 'Mesa') ? 18000.00 : 4500.00;

            $itemDto = new CreatePurchaseOrderItemDto(
                purchaseOrderId: $purchaseOrder->id,
                quoteProductVariantId: $variant->id,
                quantity: $variant->quantity,
                unitPrice: $unitPrice,
                leadTimeDays: 32,
                notes: "Mueble {$variant->label}",
                correlationId: $itemCorrelationId
            );

            $item = app(AddItemToPurchaseOrderAction::class)->handle($itemDto);
            $this->createProductionBatch($item, $variant->quantity);
        }

        $this->command->info("✅ Orden de compra creada para {$supplier->name}: {$purchaseOrder->id}");
    }

    private function createToldosPurchaseOrder(CustomerQuote $quote, Supplier $supplier, array $variants): void
    {
        if (empty($variants)) {
            return;
        }

        $correlationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrder(
            $quote->id,
            $supplier->id,
            'CLP',
            'FOB',
            ['Pago 30 días', 'Garantía 1 año', 'Instalación incluida']
        );

        $dto = new CreatePurchaseOrderDto(
            quoteId: $quote->id,
            supplierId: $supplier->id,
            currency: 'CLP',
            incoterm: 'FOB',
            terms: ['Pago 30 días', 'Garantía 1 año', 'Instalación incluida'],
            status: 'pending',
            notes: 'Orden para toldos del Conjunto de Terraza',
            correlationId: $correlationId
        );

        $purchaseOrder = app(CreatePurchaseOrderAction::class)->handle($dto);

        foreach ($variants as $variant) {
            $itemCorrelationId = app(GenerateCorrelationIdAction::class)->forPurchaseOrderItem(
                $purchaseOrder->id,
                $variant->id,
                $variant->quantity
            );

            $itemDto = new CreatePurchaseOrderItemDto(
                purchaseOrderId: $purchaseOrder->id,
                quoteProductVariantId: $variant->id,
                quantity: $variant->quantity,
                unitPrice: 15000.00, // Precio unitario estimado
                leadTimeDays: 18,
                notes: "Toldo {$variant->label}",
                correlationId: $itemCorrelationId
            );

            $item = app(AddItemToPurchaseOrderAction::class)->handle($itemDto);
            $this->createProductionBatch($item, $variant->quantity);
        }

        $this->command->info("✅ Orden de compra creada para {$supplier->name}: {$purchaseOrder->id}");
    }

    private function createProductionBatch($item, int $quantity): void
    {
        $correlationId = app(GenerateCorrelationIdAction::class)->forProductionBatch(
            $item->id,
            $quantity
        );

        $batchDto = new CreateProductionBatchDto(
            purchaseOrderItemId: $item->id,
            quantity: $quantity,
            plannedStart: now()->addDays(5),
            plannedFinish: now()->addDays($item->lead_time_days),
            status: 'planned',
            poolState: 'unassigned',
            notes: "Lote de producción para {$item->notes}",
            correlationId: $correlationId
        );

        app(CreateProductionBatchAction::class)->handle($batchDto);
    }
}
