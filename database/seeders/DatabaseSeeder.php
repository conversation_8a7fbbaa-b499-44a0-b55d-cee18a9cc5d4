<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            ProductTaxonomySeeder::class,
            RolesSeeder::class,
            ShieldPermissionsSeeder::class,
            AssignDefaultPermissionsSeeder::class,
            UsersSeeder::class,
            CustomerSeeder::class,
            // Carga la cotización de plantilla (COTIZACION_PLANTILLA.md)
            TemplateQuoteSeeder::class,
            // Completa la cotización con suppliers, órdenes de compra y lotes de producción
            CompleteQuoteSeeder::class,
        ]);
    }
}
