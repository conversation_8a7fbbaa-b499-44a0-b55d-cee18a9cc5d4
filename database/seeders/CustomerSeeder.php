<?php

namespace Database\Seeders;

use App\Models\Customer;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Crear algunos clientes de ejemplo
        $clients = [
            [
                'name' => 'TechCorp Solutions',
                'email' => '<EMAIL>',
                'phone' => '+56 2 2345 6789',
                'company' => 'TechCorp Solutions',
                'tax_id' => '76.123.456-7',
                'address' => 'Av. Providencia 1234',
                'city' => 'Santiago',
                'state' => 'Región Metropolitana',
                'country' => 'CL',
                'postal_code' => '7500000',
                'contact_person' => '<PERSON>',
                'notes' => 'Cliente preferencial con descuentos especiales',
                'metadata' => [
                    'preferred_currency' => 'USD',
                    'payment_terms' => '30 days',
                    'industry' => 'Technology',
                ],
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '+56 9 8765 4321',
                'company' => null,
                'tax_id' => '12.345.678-9',
                'address' => 'Calle Las Flores 567',
                'city' => 'Valparaíso',
                'state' => 'Región de Valparaíso',
                'country' => 'CL',
                'postal_code' => '2340000',
                'contact_person' => null,
                'notes' => 'Cliente individual, pago inmediato',
                'metadata' => [
                    'preferred_currency' => 'CLP',
                    'payment_terms' => 'immediate',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Industrias del Sur S.A.',
                'email' => '<EMAIL>',
                'phone' => '+56 65 123 4567',
                'company' => 'Industrias del Sur S.A.',
                'tax_id' => '98.765.432-1',
                'address' => 'Ruta 5 Sur Km 45',
                'city' => 'Temuco',
                'state' => 'Región de La Araucanía',
                'country' => 'CL',
                'postal_code' => '4780000',
                'contact_person' => 'Carlos Rodríguez',
                'notes' => 'Empresa manufacturera, grandes volúmenes',
                'metadata' => [
                    'preferred_currency' => 'USD',
                    'payment_terms' => '60 days',
                    'industry' => 'Manufacturing',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Servicios Profesionales Ltda.',
                'email' => '<EMAIL>',
                'phone' => '+56 2 3456 7890',
                'company' => 'Servicios Profesionales Ltda.',
                'tax_id' => '11.222.333-4',
                'address' => 'Av. Las Condes 890',
                'city' => 'Las Condes',
                'state' => 'Región Metropolitana',
                'country' => 'CL',
                'postal_code' => '7550000',
                'contact_person' => 'Ana Silva',
                'notes' => 'Cliente inactivo desde hace 6 meses',
                'metadata' => [
                    'preferred_currency' => 'CLP',
                    'payment_terms' => '30 days',
                    'industry' => 'Services',
                ],
                'is_active' => false,
            ],
        ];

        foreach ($clients as $clientData) {
            Customer::create($clientData);
        }

        // Crear clientes adicionales usando el factory
        Customer::factory()
            ->count(10)
            ->chilean()
            ->create();

        Customer::factory()
            ->count(5)
            ->argentine()
            ->create();

        Customer::factory()
            ->count(3)
            ->inactive()
            ->create();
    }
}
