<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductType;
use Illuminate\Database\Seeder;

class ProductTaxonomySeeder extends Seeder
{
    public function run(): void
    {
        // Categoría: Electronica
        $catElec = ProductCategory::query()->firstOrCreate(
            ['code' => 'ELEC'],
            ['name' => 'Electrónica']
        );
        $subUsb = ProductSubcategory::query()->firstOrCreate(
            ['category_id' => $catElec->id, 'code' => 'USB'],
            ['name' => 'Unidades Flash USB']
        );
        ProductType::query()->firstOrCreate(
            ['code' => 'USB-3-METAL'],
            [
                'subcategory_id' => $subUsb->id,
                'name' => 'USB 3.0 carcasa metal',
                'hs_code_sugerido' => '8523.51',
                'defaults' => [
                    'peso' => 30,
                    'lead_time_base' => 15,
                ],
                'reglas' => [
                    'producto' => ['capacidad' => 'required', 'interfaz' => ['in' => ['USB-A', 'USB-C']]],
                    'variante' => [],
                ],
            ]
        );

        // Categoría: Bebidas & Contenedores
        $catBeb = ProductCategory::query()->firstOrCreate(
            ['code' => 'BEB-CON'],
            ['name' => 'Bebidas & Contenedores']
        );
        $subTermos = ProductSubcategory::query()->firstOrCreate(
            ['category_id' => $catBeb->id, 'code' => 'TERMOS'],
            ['name' => 'Termos']
        );
        ProductType::query()->firstOrCreate(
            ['code' => 'TERMO-500ML-ACERO'],
            [
                'subcategory_id' => $subTermos->id,
                'name' => 'Termo acero 500ml',
                'hs_code_sugerido' => '9617.00',
                'defaults' => [
                    'peso' => 280,
                    'empaque' => ['unidades_por_caja' => 24],
                ],
                'reglas' => [
                    'producto' => ['material' => ['in' => ['Acero 304', 'Acero 201']], 'capacidad' => ['equals' => 500]],
                    'variante' => ['acabado' => 'required'],
                ],
            ]
        );

        // Categoría: Textil
        $catTextil = ProductCategory::query()->firstOrCreate(
            ['code' => 'TEXTIL'],
            ['name' => 'Textil']
        );
        $subGorras = ProductSubcategory::query()->firstOrCreate(
            ['category_id' => $catTextil->id, 'code' => 'GORRAS'],
            ['name' => 'Gorras']
        );
        ProductType::query()->firstOrCreate(
            ['code' => 'GORRA-BASEBALL'],
            [
                'subcategory_id' => $subGorras->id,
                'name' => 'Gorra tipo baseball',
                'hs_code_sugerido' => '6505.00',
                'defaults' => [],
                'reglas' => [
                    'variante' => ['color' => 'required'],
                ],
            ]
        );
    }
}

