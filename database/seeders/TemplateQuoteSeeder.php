<?php

namespace Database\Seeders;

use App\Enums\CustomerQuoteStatus;
use App\Models\CustomerQuote;
use App\Models\CustomerQuoteGroup;
use App\Models\CustomerQuoteProduct;
use App\Models\CustomerQuoteProductVariant;
use App\Models\ProductType;
use App\ValueObjects\Money;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TemplateQuoteSeeder extends Seeder
{
    public function run(): void
    {
        DB::transaction(function () {
            // Tipos desde taxonomía (solo los disponibles)
            $tipoUsb = ProductType::query()->where('code', 'USB-3-METAL')->first();
            $tipoTermo = ProductType::query()->where('code', 'TERMO-500ML-ACERO')->first();
            $tipoGorra = ProductType::query()->where('code', 'GORRA-BASEBALL')->first();

            // Cabecera de la cotización (upsert)
            $quote = CustomerQuote::query()->firstOrCreate(
                ['quote_number' => 'COT-25-098'],
                [
                    'customer_name' => 'Empresa Ejemplo S.A.',
                    'currency' => 'CLP',
                    'country' => 'CL',
                    'status' => CustomerQuoteStatus::Borrador,
                    'notes' => 'Plantilla de ejemplo basada en COTIZACION_PLANTILLA.md - Conferencia Anual 2025',
                ]
            );

            // Asegura valores actuales
            $quote->fill([
                'customer_name' => 'Empresa Ejemplo S.A.',
                'currency' => 'CLP',
                'country' => 'CL',
                'status' => CustomerQuoteStatus::Borrador,
                'notes' => 'Plantilla de ejemplo basada en COTIZACION_PLANTILLA.md - Conferencia Anual 2025',
            ])->save();

            // Limpia estructura previa para reconstruir
            $quoteProductIds = CustomerQuoteProduct::query()->where('customer_quote_id', $quote->id)->pluck('id');
            if ($quoteProductIds->isNotEmpty()) {
                CustomerQuoteProductVariant::query()->whereIn('quote_product_id', $quoteProductIds)->delete();
            }
            CustomerQuoteProduct::query()->where('customer_quote_id', $quote->id)->delete();
            CustomerQuoteGroup::query()->where('customer_quote_id', $quote->id)->delete();

            // Grupo 1: Kit de Bienvenida "Ejecutivo" (Kit con precio fijo)
            $kitEjecutivo = CustomerQuoteGroup::query()->create([
                'customer_quote_id' => $quote->id,
                'name' => 'Kit de Bienvenida "Ejecutivo"',
                'quantity' => 100, // 100 kits
                'unit_price' => Money::fromMajor(27000, 'CLP'), // $27.000 CLP por kit
                'position' => 1,
            ]);

            $pos = 1;

            // Componente 1: Memoria USB 32GB (1 unidad por kit)
            $qUsb = CustomerQuoteProduct::query()->create([
                'customer_quote_id' => $quote->id,
                'group_id' => $kitEjecutivo->id,
                'type_id' => $tipoUsb->id,
                'name' => 'Memoria USB 32GB',
                'attributes' => ['capacidad' => '32GB', 'interfaz' => 'USB-A'],
                'specs' => [],
                'hs_code' => $tipoUsb->hs_code_sugerido,
                'weight' => 30,
                'volume' => null,
                'type_code' => $tipoUsb->code,
                'type_name' => $tipoUsb->name,
                'subcategory_code' => $tipoUsb->subcategory->code ?? null,
                'category_code' => optional($tipoUsb->subcategory)->category->code ?? null,
                'units_per_kit' => 1, // 1 memoria por kit
                'position' => $pos++,
            ]);

            // Variante 1: Color Negro, Logo Láser (70 unidades)
            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qUsb->id,
                'label' => 'Color Negro, Logo Láser',
                'attributes' => ['color' => 'Negro', 'acabado' => 'Láser'],
                'quantity' => 70, // 70 memorias
                'unit_price' => null, // Sin precio (solo para costeo interno)
                'fx_rate_to_quote' => 1,
                'position' => 1,
            ]);

            // Variante 2: Color Blanco, Logo Grabado (30 unidades)
            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qUsb->id,
                'label' => 'Color Blanco, Logo Grabado',
                'attributes' => ['color' => 'Blanco', 'acabado' => 'Grabado'],
                'quantity' => 30, // 30 memorias
                'unit_price' => null, // Sin precio (solo para costeo interno)
                'fx_rate_to_quote' => 1,
                'position' => 2,
            ]);

            // Componente 2: Termo 500ml (1 unidad por kit)
            $qTermo = CustomerQuoteProduct::query()->create([
                'customer_quote_id' => $quote->id,
                'group_id' => $kitEjecutivo->id,
                'type_id' => $tipoTermo->id,
                'name' => 'Termo 500ml Acero',
                'attributes' => ['material' => 'Acero 304', 'capacidad' => 500],
                'specs' => [],
                'hs_code' => $tipoTermo->hs_code_sugerido,
                'weight' => 280,
                'volume' => null,
                'type_code' => $tipoTermo->code,
                'type_name' => $tipoTermo->name,
                'subcategory_code' => $tipoTermo->subcategory->code ?? null,
                'category_code' => optional($tipoTermo->subcategory)->category->code ?? null,
                'units_per_kit' => 1, // 1 termo por kit
                'position' => $pos++,
            ]);

            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qTermo->id,
                'label' => 'Acero Inoxidable, Logo Grabado Láser',
                'attributes' => ['acabado' => 'Grabado Láser'],
                'quantity' => 100, // 100 kits × 1 termo por kit = 100 termos
                'unit_price' => null, // Sin precio (solo para costeo interno)
                'fx_rate_to_quote' => 1,
                'position' => 1,
            ]);

            // Validar composición del Kit de Bienvenida
            $this->validateKitComposition($kitEjecutivo);

            // Producto 2: Termo 500ml "Traveler" (500 unidades) - Producto individual
            $qTermo2 = CustomerQuoteProduct::query()->create([
                'customer_quote_id' => $quote->id,
                'group_id' => null,
                'type_id' => $tipoTermo->id,
                'name' => 'Termo 500ml "Traveler"',
                'attributes' => ['material' => 'Acero Inoxidable', 'capacidad' => 500],
                'specs' => [],
                'hs_code' => $tipoTermo->hs_code_sugerido,
                'weight' => 350,
                'volume' => null,
                'type_code' => $tipoTermo->code,
                'type_name' => $tipoTermo->name,
                'subcategory_code' => $tipoTermo->subcategory->code ?? null,
                'category_code' => optional($tipoTermo->subcategory)->category->code ?? null,
                'position' => $pos++,
            ]);

            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qTermo2->id,
                'label' => 'Acero Inoxidable, Logo Grabado Láser',
                'attributes' => ['acabado' => 'Grabado Láser'],
                'quantity' => 500,
                'unit_price' => Money::fromMajor(12500, 'CLP'), // 12.500 CLP
                'fx_rate_to_quote' => 1,
                'position' => 1,
            ]);

            // Producto 3: Gorra Corporativa "ProStyle" (400 unidades) - Producto individual
            $qGorra = CustomerQuoteProduct::query()->create([
                'customer_quote_id' => $quote->id,
                'group_id' => null,
                'type_id' => $tipoGorra->id,
                'name' => 'Gorra Corporativa "ProStyle"',
                'attributes' => ['modelo' => 'Baseball', 'material' => 'Algodón'],
                'specs' => [],
                'hs_code' => $tipoGorra->hs_code_sugerido,
                'weight' => 150,
                'volume' => null,
                'type_code' => $tipoGorra->code,
                'type_name' => $tipoGorra->name,
                'subcategory_code' => $tipoGorra->subcategory->code ?? null,
                'category_code' => optional($tipoGorra->subcategory)->category->code ?? null,
                'position' => $pos++,
            ]);

            // Variante 1: Color Azul Marino, Logo Frontal (150 unidades)
            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qGorra->id,
                'label' => 'Color Azul Marino, Logo Frontal',
                'attributes' => ['color' => 'Azul Marino', 'posicion_logo' => 'Frontal'],
                'quantity' => 150,
                'unit_price' => Money::fromMajor(8750, 'CLP'), // 8.750 CLP
                'fx_rate_to_quote' => 1,
                'position' => 1,
            ]);

            // Variante 2: Color Blanco, Logo Lateral (150 unidades)
            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qGorra->id,
                'label' => 'Color Blanco, Logo Lateral',
                'attributes' => ['color' => 'Blanco', 'posicion_logo' => 'Lateral'],
                'quantity' => 150,
                'unit_price' => Money::fromMajor(8750, 'CLP'), // 8.750 CLP
                'fx_rate_to_quote' => 1,
                'position' => 2,
            ]);

            // Variante 3: Color Negro, Logo Reflectante (100 unidades)
            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qGorra->id,
                'label' => 'Color Negro, Logo Reflectante',
                'attributes' => ['color' => 'Negro', 'acabado' => 'Reflectante'],
                'quantity' => 100,
                'unit_price' => Money::fromMajor(8750, 'CLP'), // 8.750 CLP
                'fx_rate_to_quote' => 1,
                'position' => 3,
            ]);

            // Grupo 2: Conjunto de Terraza de Verano (Kit con precio fijo)
            $kitTerraza = CustomerQuoteGroup::query()->create([
                'customer_quote_id' => $quote->id,
                'name' => 'Conjunto de Terraza de Verano',
                'quantity' => 50, // 50 kits
                'unit_price' => Money::fromMajor(30000, 'CLP'), // $30.000 CLP por kit
                'position' => 2,
            ]);

            $posTerraza = 1;

            // Componente 1: Mesa de Melamina (1 unidad por kit)
            $qMesa = CustomerQuoteProduct::query()->create([
                'customer_quote_id' => $quote->id,
                'group_id' => $kitTerraza->id,
                'type_id' => $tipoTermo->id, // Usando tipo disponible como base
                'name' => 'Mesa de Melamina',
                'attributes' => ['material' => 'Melamina', 'dimensiones' => '120x80cm'],
                'specs' => [],
                'hs_code' => '9403.60.00', // Código HS para muebles
                'weight' => 15000, // 15 kg
                'volume' => 0.12, // 0.12 m³
                'type_code' => 'MESA-MELAMINA',
                'type_name' => 'Mesa de Melamina',
                'subcategory_code' => 'MUEBLES',
                'category_code' => 'HOGAR',
                'units_per_kit' => 1, // 1 mesa por kit
                'position' => $posTerraza++,
            ]);

            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qMesa->id,
                'label' => 'Color Blanco, Patas de Acero',
                'attributes' => ['color' => 'Blanco', 'patas' => 'Acero'],
                'quantity' => 50, // 50 kits × 1 mesa por kit
                'unit_price' => null, // Sin precio (solo para costeo interno)
                'fx_rate_to_quote' => 1,
                'position' => 1,
            ]);

            // Componente 2: Sillas Tolix (4 unidades por kit)
            $qSillas = CustomerQuoteProduct::query()->create([
                'customer_quote_id' => $quote->id,
                'group_id' => $kitTerraza->id,
                'type_id' => $tipoTermo->id, // Usando tipo disponible como base
                'name' => 'Sillas Tolix',
                'attributes' => ['material' => 'Acero', 'estilo' => 'Tolix'],
                'specs' => [],
                'hs_code' => '9401.79.00', // Código HS para sillas
                'weight' => 5000, // 5 kg cada una
                'volume' => 0.05, // 0.05 m³ cada una
                'type_code' => 'SILLA-TOLIX',
                'type_name' => 'Silla Tolix',
                'subcategory_code' => 'MUEBLES',
                'category_code' => 'HOGAR',
                'units_per_kit' => 4, // 4 sillas por kit
                'position' => $posTerraza++,
            ]);

            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qSillas->id,
                'label' => 'Color Negro, Estilo Industrial',
                'attributes' => ['color' => 'Negro', 'estilo' => 'Industrial'],
                'quantity' => 200, // 50 kits × 4 sillas por kit
                'unit_price' => null, // Sin precio (solo para costeo interno)
                'fx_rate_to_quote' => 1,
                'position' => 1,
            ]);

            // Componente 3: Toldo con Logo Impreso (1 unidad por kit)
            $qToldo = CustomerQuoteProduct::query()->create([
                'customer_quote_id' => $quote->id,
                'group_id' => $kitTerraza->id,
                'type_id' => $tipoTermo->id, // Usando tipo disponible como base
                'name' => 'Toldo con Logo Impreso',
                'attributes' => ['material' => 'Lona', 'dimensiones' => '3x2m'],
                'specs' => [],
                'hs_code' => '6306.12.00', // Código HS para toldos
                'weight' => 3000, // 3 kg
                'volume' => 0.02, // 0.02 m³
                'type_code' => 'TOLDO-LONA',
                'type_name' => 'Toldo de Lona',
                'subcategory_code' => 'TEXTILES',
                'category_code' => 'HOGAR',
                'units_per_kit' => 1, // 1 toldo por kit
                'position' => $posTerraza++,
            ]);

            CustomerQuoteProductVariant::query()->create([
                'quote_product_id' => $qToldo->id,
                'label' => 'Color Azul, Logo Impreso a Color',
                'attributes' => ['color' => 'Azul', 'acabado' => 'Logo Impreso'],
                'quantity' => 50, // 50 kits × 1 toldo por kit
                'unit_price' => null, // Sin precio (solo para costeo interno)
                'fx_rate_to_quote' => 1,
                'position' => 1,
            ]);

            // Validar composición de kits
            $this->validateKitComposition($kitTerraza);
        });
    }

    /**
     * Valida que la composición del kit sea correcta.
     */
    private function validateKitComposition(\App\Models\CustomerQuoteGroup $group): void
    {
        $errors = $group->validateKitComposition();

        if (! empty($errors)) {
            throw new \Exception("Error en composición del kit '{$group->name}': ".implode('; ', $errors));
        }

        echo "✅ Kit '{$group->name}' validado correctamente\n";
    }
}
