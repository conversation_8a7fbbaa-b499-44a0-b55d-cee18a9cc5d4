<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerQuoteGroup>
 */
class CustomerQuoteGroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true).' Kit',
            'notes' => $this->faker->optional()->sentence(),
            'position' => $this->faker->numberBetween(1, 10),
            'quantity' => $this->faker->numberBetween(1, 100),
            'unit_price_minor' => $this->faker->optional(0.5)->numberBetween(1000, 50000), // $10-$500
            'price_currency' => $this->faker->randomElement(['USD', 'EUR', 'CLP']),
            'fx_rate_to_quote' => $this->faker->randomFloat(8, 0.5, 2.0),
        ];
    }

    public function withFixedPrice(): static
    {
        return $this->state(fn (array $attributes) => [
            'unit_price_minor' => $this->faker->numberBetween(1000, 50000),
            'price_currency' => 'USD',
            'fx_rate_to_quote' => 1.0,
        ]);
    }

    public function withoutFixedPrice(): static
    {
        return $this->state(fn (array $attributes) => [
            'unit_price_minor' => null,
            'price_currency' => null,
            'fx_rate_to_quote' => 1.0,
        ]);
    }
}
