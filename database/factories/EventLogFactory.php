<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\EventLog>
 */
class EventLogFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $entity = $this->faker->randomElement([
            'supplier', 'po', 'po_item', 'batch',
            'quote', 'quote_product', 'quote_variant', 'quote_group',
        ]);

        $actions = match ($entity) {
            'quote' => ['created', 'updated', 'deleted', 'duplicated', 'exported_pdf', 'status_changed'],
            'quote_product' => ['created', 'updated', 'deleted'],
            'quote_variant' => ['created', 'updated', 'deleted'],
            'quote_group' => ['created', 'updated', 'deleted'],
            default => ['created', 'updated', 'deleted', 'sent', 'confirmed', 'closed', 'status_changed']
        };

        $payload = match ($entity) {
            'quote' => [
                'quote_number' => 'COT-'.$this->faker->year().'-'.$this->faker->numerify('###'),
                'customer_name' => $this->faker->company(),
                'currency' => $this->faker->randomElement(['USD', 'EUR', 'CLP']),
                'status' => $this->faker->randomElement(['borrador', 'enviada', 'confirmada', 'cerrada']),
            ],
            'quote_product' => [
                'product_name' => $this->faker->words(3, true),
                'type_code' => $this->faker->bothify('TYP-###'),
                'quantity' => $this->faker->numberBetween(1, 100),
            ],
            'quote_variant' => [
                'variant_name' => $this->faker->words(2, true),
                'quantity' => $this->faker->numberBetween(1, 50),
                'unit_price_minor' => $this->faker->numberBetween(1000, 100000),
                'price_currency' => $this->faker->randomElement(['USD', 'EUR', 'CLP']),
            ],
            default => [
                'previous_status' => $this->faker->optional()->randomElement(['borrador', 'enviada', 'confirmada', 'cerrada']),
                'new_status' => $this->faker->optional()->randomElement(['borrador', 'enviada', 'confirmada', 'cerrada']),
            ]
        };

        return [
            'entity' => $entity,
            'entity_id' => $this->faker->numberBetween(1, 100),
            'action' => $this->faker->randomElement($actions),
            'actor_id' => \App\Models\User::factory(),
            'payload' => $payload,
            'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
        ];
    }
}
