<?php

namespace Database\Factories;

use App\Models\ProductSubcategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductType>
 */
class ProductTypeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'subcategory_id' => ProductSubcategory::factory(),
            'code' => $this->faker->unique()->lexify('???'),
            'name' => $this->faker->words(2, true),
            'hs_code_sugerido' => $this->faker->optional(0.8)->numerify('####.##.##'),
            'defaults' => $this->faker->optional(0.6)->randomElement([
                ['capacity' => $this->faker->randomElement(['8GB', '16GB', '32GB'])],
                ['material' => $this->faker->randomElement(['Algodón', 'Poliester', 'Plástico'])],
                ['size' => $this->faker->randomElement(['S', 'M', 'L', 'XL'])],
                null,
            ]),
            'reglas' => $this->faker->optional(0.5)->randomElement([
                ['min_capacity' => '8GB', 'max_capacity' => '128GB'],
                ['allowed_materials' => ['Algodón', 'Poliester']],
                ['required_attributes' => ['color', 'size']],
                null,
            ]),
        ];
    }
}
