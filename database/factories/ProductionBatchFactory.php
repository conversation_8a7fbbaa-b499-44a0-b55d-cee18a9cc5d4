<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductionBatch>
 */
class ProductionBatchFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'purchase_order_item_id' => \App\Models\PurchaseOrderItem::factory(),
            'quantity' => $this->faker->numberBetween(10, 500),
            'planned_start' => $this->faker->optional()->dateTimeBetween('now', '+30 days'),
            'planned_finish' => $this->faker->optional()->dateTimeBetween('+1 day', '+60 days'),
            'status' => 'borrador',
            'pool_state' => 'available',
            'correlation_id' => $this->faker->unique()->sha256(),
            'notes' => $this->faker->optional()->sentence(),
        ];
    }
}
