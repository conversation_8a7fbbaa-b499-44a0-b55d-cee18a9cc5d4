<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerQuote>
 */
class CustomerQuoteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'quote_number' => 'Q-'.$this->faker->unique()->numberBetween(1000, 9999),
            'customer_name' => $this->faker->company(),
            'currency' => $this->faker->randomElement(['USD', 'EUR', 'CLP']),
            'status' => $this->faker->randomElement(['borrador', 'enviada', 'aprobada', 'rechazada']),
            'valid_until' => $this->faker->dateTimeBetween('+1 week', '+1 month'),
            'notes' => $this->faker->optional()->paragraph(),
            'country' => $this->faker->randomElement(['US', 'CL', 'ES', 'MX']),
        ];
    }
}
