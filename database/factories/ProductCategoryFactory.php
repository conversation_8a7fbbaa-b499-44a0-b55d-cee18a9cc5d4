<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductCategory>
 */
class ProductCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->lexify('???'),
            'name' => $this->faker->words(2, true),
            'metadata' => $this->faker->optional(0.7)->randomElement([
                ['description' => $this->faker->sentence()],
                ['target_audience' => $this->faker->randomElement(['B2B', 'B2C', 'Both'])],
                ['features' => $this->faker->randomElements(['premium', 'standard', 'budget'], 2)],
                null,
            ]),
        ];
    }
}
