<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerQuoteProduct>
 */
class CustomerQuoteProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true).' Product',
            'type_id' => \App\Models\ProductType::factory(),
            'type_code' => $this->faker->randomElement(['MEM', 'TER', 'GOR', 'CAM', 'BOL']),
            'type_name' => $this->faker->randomElement(['Memoria USB', 'Termo', 'Gorra', 'Camiseta', 'Bolsa']),
            'category_code' => $this->faker->randomElement(['PROM', 'MERCH', 'TECH']),
            'subcategory_code' => $this->faker->randomElement(['PROMO', 'BRAND', 'GADGET']),
            'hs_code' => $this->faker->numerify('####.##.##'),
            'weight' => $this->faker->randomFloat(3, 0.1, 5.0),
            'volume' => $this->faker->randomFloat(3, 0.001, 0.1),
            'notes' => $this->faker->optional()->sentence(),
            'position' => $this->faker->numberBetween(1, 20),
            'units_per_kit' => $this->faker->optional(0.3)->numberBetween(1, 10),
            'attributes' => [
                'color' => $this->faker->colorName(),
                'material' => $this->faker->randomElement(['Algodón', 'Poliester', 'Plástico', 'Metal']),
                'size' => $this->faker->randomElement(['S', 'M', 'L', 'XL']),
            ],
            'specs' => [
                'capacity' => $this->faker->optional()->randomElement(['8GB', '16GB', '32GB', '500ml', '1L']),
                'brand' => $this->faker->company(),
                'model' => $this->faker->bothify('MOD-###'),
            ],
        ];
    }

    public function withGroup(): static
    {
        return $this->state(fn (array $attributes) => [
            'group_id' => \App\Models\CustomerQuoteGroup::factory(),
        ]);
    }

    public function withQuote(): static
    {
        return $this->state(fn (array $attributes) => [
            'customer_quote_id' => \App\Models\CustomerQuote::factory(),
        ]);
    }

    public function forKit(): static
    {
        return $this->state(fn (array $attributes) => [
            'units_per_kit' => $this->faker->numberBetween(1, 5),
        ]);
    }
}
