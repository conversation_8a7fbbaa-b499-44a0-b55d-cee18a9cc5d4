<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PurchaseOrderItem>
 */
class PurchaseOrderItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'purchase_order_id' => \App\Models\PurchaseOrder::factory(),
            'quote_product_variant_id' => \App\Models\CustomerQuoteProductVariant::factory(),
            'quantity' => $this->faker->numberBetween(10, 1000),
            'unit_price' => $this->faker->optional()->randomFloat(2, 0.1, 100),
            'lead_time_days' => $this->faker->optional()->numberBetween(1, 90),
            'correlation_id' => $this->faker->unique()->sha256(),
            'notes' => $this->faker->optional()->sentence(),
        ];
    }
}
