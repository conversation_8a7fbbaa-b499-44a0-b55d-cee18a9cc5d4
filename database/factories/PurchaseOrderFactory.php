<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PurchaseOrder>
 */
class PurchaseOrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'quote_id' => \App\Models\CustomerQuote::factory(),
            'supplier_id' => \App\Models\Supplier::factory(),
            'currency' => $this->faker->randomElement(['USD', 'EUR', 'CLP']),
            'incoterm' => $this->faker->randomElement(['FOB', 'CIF', 'EXW', 'DDP']),
            'terms' => [
                'payment' => $this->faker->randomElement(['30/70', '50/50', '100% advance']),
                'remarks' => $this->faker->optional()->sentence(),
            ],
            'status' => 'borrador',
            'correlation_id' => $this->faker->unique()->sha256(),
            'notes' => $this->faker->optional()->sentence(),
        ];
    }
}
