<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isCompany = $this->faker->boolean(60); // 60% probabilidad de ser empresa

        return [
            'name' => $isCompany
                ? $this->faker->company()
                : $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => $this->faker->phoneNumber(),
            'company' => $isCompany ? $this->faker->company() : null,
            'tax_id' => $this->faker->optional(0.7)->numerify('##.###.###-#'), // RUT chileno
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'country' => $this->faker->randomElement(['CL', 'AR', 'PE', 'CO', 'MX', 'US']),
            'postal_code' => $this->faker->postcode(),
            'contact_person' => $this->faker->optional(0.5)->name(),
            'notes' => $this->faker->optional(0.3)->paragraph(),
            'metadata' => $this->faker->boolean(20) ? [
                'preferred_currency' => $this->faker->randomElement(['USD', 'CLP', 'EUR']),
                'payment_terms' => $this->faker->randomElement(['30 days', '60 days', 'immediate']),
                'industry' => $this->faker->randomElement(['Technology', 'Manufacturing', 'Services', 'Retail']),
            ] : null,
            'is_active' => true,
        ];
    }

    /**
     * Estado para cliente empresa
     */
    public function company(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->company(),
            'company' => $this->faker->company(),
            'contact_person' => $this->faker->name(),
        ]);
    }

    /**
     * Estado para cliente persona natural
     */
    public function individual(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->name(),
            'company' => null,
            'contact_person' => null,
        ]);
    }

    /**
     * Estado para cliente inactivo
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Estado para cliente chileno
     */
    public function chilean(): static
    {
        return $this->state(fn (array $attributes) => [
            'country' => 'CL',
            'tax_id' => $this->faker->numerify('##.###.###-#'),
        ]);
    }

    /**
     * Estado para cliente argentino
     */
    public function argentine(): static
    {
        return $this->state(fn (array $attributes) => [
            'country' => 'AR',
            'tax_id' => $this->faker->numerify('##-########-#'),
        ]);
    }
}
