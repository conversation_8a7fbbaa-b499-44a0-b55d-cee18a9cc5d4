<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerQuoteProductVariant>
 */
class CustomerQuoteProductVariantFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $colors = ['Rojo', 'Azul', 'Verde', 'Negro', 'Blanco', 'Gris', 'Amarillo', 'Rosa'];
        $sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
        $capacities = ['8GB', '16GB', '32GB', '64GB', '128GB', '256GB'];
        $materials = ['Algodón', 'Poliester', 'Plástico', 'Metal', 'Cuero', 'Lana'];

        $label = $this->faker->randomElement($colors).' - '.$this->faker->randomElement($sizes);
        $capacity = $this->faker->optional(0.3)->randomElement($capacities);
        $material = $this->faker->randomElement($materials);

        return [
            'label' => $label,
            'quantity' => $this->faker->numberBetween(1, 1000),
            'unit_price_minor' => $this->faker->optional(0.8)->numberBetween(100, 50000), // $1-$500
            'price_currency' => $this->faker->randomElement(['USD', 'EUR', 'CLP']),
            'fx_rate_to_quote' => $this->faker->randomFloat(8, 0.5, 2.0),
            'hs_code' => $this->faker->optional(0.7)->numerify('####.##.##'),
            'weight' => $this->faker->randomFloat(3, 0.01, 2.0),
            'volume' => $this->faker->randomFloat(3, 0.001, 0.05),
            'notes' => $this->faker->optional(0.3)->sentence(),
            'position' => $this->faker->numberBetween(1, 50),
            'attributes' => [
                'color' => $this->faker->randomElement($colors),
                'size' => $this->faker->randomElement($sizes),
                'material' => $material,
                'finish' => $this->faker->optional(0.5)->randomElement(['Mate', 'Brillante', 'Texturizado']),
            ],
            'specs' => [
                'capacity' => $capacity,
                'brand' => $this->faker->company(),
                'model' => $this->faker->bothify('VAR-###'),
                'warranty' => $this->faker->optional(0.6)->randomElement(['1 año', '2 años', '3 años']),
            ],
        ];
    }

    public function withProduct(): static
    {
        return $this->state(fn (array $attributes) => [
            'quote_product_id' => \App\Models\CustomerQuoteProduct::factory(),
        ]);
    }

    public function withPrice(): static
    {
        return $this->state(fn (array $attributes) => [
            'unit_price_minor' => $this->faker->numberBetween(1000, 10000), // $10-$100
            'price_currency' => 'USD',
            'fx_rate_to_quote' => 1.0,
        ]);
    }

    public function highQuantity(): static
    {
        return $this->state(fn (array $attributes) => [
            'quantity' => $this->faker->numberBetween(100, 5000),
        ]);
    }
}
