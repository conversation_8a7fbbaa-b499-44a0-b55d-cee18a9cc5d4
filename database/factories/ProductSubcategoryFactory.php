<?php

namespace Database\Factories;

use App\Models\ProductCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductSubcategory>
 */
class ProductSubcategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'category_id' => ProductCategory::factory(),
            'code' => $this->faker->unique()->lexify('???'),
            'name' => $this->faker->words(2, true),
            'metadata' => $this->faker->optional(0.6)->randomElement([
                ['description' => $this->faker->sentence()],
                ['target_audience' => $this->faker->randomElement(['professionals', 'consumers', 'students'])],
                ['seasonal' => $this->faker->boolean()],
                null,
            ]),
        ];
    }
}
