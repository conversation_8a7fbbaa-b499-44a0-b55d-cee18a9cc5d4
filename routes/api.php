<?php

use App\Http\Controllers\CustomerQuoteController;
use App\Http\Controllers\CustomerQuoteGroupController;
use App\Http\Controllers\CustomerQuoteProductController;
use App\Http\Controllers\CustomerQuoteProductVariantController;
use App\Http\Controllers\ProductionBatchController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\SourcingSummaryController;
use App\Http\Controllers\SupplierController;
use Illuminate\Support\Facades\Route;

// Customer quotes API endpoints
Route::post('/customer-quotes', [CustomerQuoteController::class, 'store']);
Route::put('/customer-quotes/{quote}', [CustomerQuoteController::class, 'update']);
Route::get('/customer-quotes/{quote}', [CustomerQuoteController::class, 'show']);

// Legacy routes - deprecated, will be removed in future version
Route::post('/client-quotes', [CustomerQuoteController::class, 'store']);
Route::get('/client-quotes/{quote}', [CustomerQuoteController::class, 'show']);

// Customer quote groups API endpoints
Route::post('/customer-quotes/{quote}/groups', [CustomerQuoteGroupController::class, 'store']);
Route::patch('/customer-quote-groups/{group}', [CustomerQuoteGroupController::class, 'update']);
Route::delete('/customer-quote-groups/{group}', [CustomerQuoteGroupController::class, 'destroy']);

// Legacy routes - deprecated, will be removed in future version
Route::post('/client-quotes/{quote}/groups', [CustomerQuoteGroupController::class, 'store']);
Route::patch('/client-quote-groups/{group}', [CustomerQuoteGroupController::class, 'update']);
Route::delete('/client-quote-groups/{group}', [CustomerQuoteGroupController::class, 'destroy']);

// Customer quote products API endpoints
Route::post('/customer-quotes/{quote}/products', [CustomerQuoteProductController::class, 'store']);
Route::patch('/customer-quote-products/{product}', [CustomerQuoteProductController::class, 'update']);
Route::delete('/customer-quote-products/{product}', [CustomerQuoteProductController::class, 'destroy']);

// Legacy routes - deprecated, will be removed in future version
Route::post('/client-quotes/{quote}/products', [CustomerQuoteProductController::class, 'store']);
Route::patch('/client-quote-products/{product}', [CustomerQuoteProductController::class, 'update']);
Route::delete('/client-quote-products/{product}', [CustomerQuoteProductController::class, 'destroy']);

// Customer quote product variants API endpoints
Route::post('/customer-quote-products/{product}/variants', [CustomerQuoteProductVariantController::class, 'store']);
Route::patch('/customer-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'update']);
Route::delete('/customer-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'destroy']);

// Legacy routes - deprecated, will be removed in future version
Route::post('/client-quote-products/{product}/variants', [CustomerQuoteProductVariantController::class, 'store']);
Route::patch('/client-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'update']);
Route::delete('/client-quote-product-variants/{variant}', [CustomerQuoteProductVariantController::class, 'destroy']);

// Suppliers
Route::apiResource('suppliers', SupplierController::class);

// Purchase Orders
// Purchase orders for customer quotes
Route::post('/customer-quotes/{quote}/purchase-orders', [PurchaseOrderController::class, 'store']);

// Legacy route - deprecated, will be removed in future version
Route::post('/client-quotes/{quote}/purchase-orders', [PurchaseOrderController::class, 'store']);
Route::get('/purchase-orders/{purchaseOrder}', [PurchaseOrderController::class, 'show']);
Route::post('/purchase-orders/{purchaseOrder}/items', [PurchaseOrderController::class, 'addItem']);
Route::post('/purchase-orders/{purchaseOrder}/send', [PurchaseOrderController::class, 'send']);
Route::post('/purchase-orders/{purchaseOrder}/confirm', [PurchaseOrderController::class, 'confirm']);
Route::post('/purchase-orders/{purchaseOrder}/close', [PurchaseOrderController::class, 'close']);

// Production Batches
Route::post('/purchase-order-items/{purchaseOrderItem}/batches', [ProductionBatchController::class, 'store']);
Route::get('/production-batches/{productionBatch}', [ProductionBatchController::class, 'show']);
Route::post('/production-batches/{productionBatch}/status', [ProductionBatchController::class, 'changeStatus']);

// Sourcing Summary
// Sourcing summary for customer quotes
Route::get('/customer-quotes/{quote}/sourcing/summary', [SourcingSummaryController::class, 'summary']);

// Legacy route - deprecated, will be removed in future version
Route::get('/client-quotes/{quote}/sourcing/summary', [SourcingSummaryController::class, 'summary']);
