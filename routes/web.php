<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');

    // Vista de cotización (redirigida a Filament)
    Route::get('quotes/{quote}', function ($quote) {
        return redirect()->route('filament.admin.resources.customer-quotes.view', $quote);
    })->name('quotes.show');

    // Vista interna para Sourcing: estructura planificada (grupos → productos → variantes)
    Volt::route('sourcing/quotes/{quote}/plan', 'sourcing.quote-plan')->name('sourcing.quote-plan');

    // Vista de plan de sourcing: órdenes de compra, suppliers y lotes de producción
    Volt::route('sourcing/quotes/{quote}/sourcing-plan', 'sourcing.sourcing-plan')->name('sourcing.sourcing-plan');
});

require __DIR__.'/auth.php';
