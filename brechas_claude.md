# Brechas de Calidad - Laravel 12 + Filament 4

## Problemas Críticos Pendientes

### 1. **Inconsistencia de Nomenclatura Base de Datos (CRÍTICO)**
- **Problema**: Confusión entre "Client" y "Customer" en esquema de base de datos
- **Evidencia del Schema**: 
  - Tabla `customer_quote_groups` tiene FK `customer_quote_id` pero constraint se llama `client_quote_groups_client_quote_id_foreign`
  - Índices tienen nombres legacy: `client_quote_groups_pkey`, `client_quote_products_pkey`
  - Foreign keys mantienen nombres `client_*` aunque tablas sean `customer_*`
- **Impacto**: Confusión en desarrollo, queries complejas, debugging difícil

### 2. **Relaciones de Modelo Duplicadas (CRÍTICO)**
- **Ubicación**: `app/Models/CustomerQuote.php:32-41`
- **Problema**: Métodos `client()` y `customer()` apuntan al mismo modelo
- **Anti-patrón**: Relaciones duplicadas crean ambigüedad y complejidad
```php
public function client(): BelongsTo // Confuso
public function customer(): BelongsTo // Duplicado
```
- **Estado**: Aún presente tras correcciones

### 3. **Uso de Patrones Obsoletos de Laravel (CRÍTICO)**
- **Ubicación**: 8 modelos siguen usando `$casts` propiedad
- **Modelos afectados**: `Customer`, `CustomerQuote`, `CustomerQuoteGroup`, `CustomerQuoteProduct`, `CustomerQuoteProductVariant`, `ProductCategory`, `ProductSubcategory`, `ProductType`
- **Problema**: Uso de `$casts` en lugar del método `casts()` de Laravel 12
- **Estado**: No corregido

## Violaciones de Mejores Prácticas

### 4. **Atributos con Patrón Obsoleto (PERSISTENTE)**
- **Ubicación**: `app/Models/Customer.php:48-124`
- **Problema**: Múltiples accessors usando patrón obsoleto de Laravel
- **Ejemplos**: `getFullNameAttribute()`, `getFullAddressAttribute()`, `getQuotesCountAttribute()`, `getLastQuoteAttribute()`
- **Estado**: No corregido, sigue usando patrones pre-Laravel 9

### 5. **Referencias de Modelos Correctas en Recursos Filament**
- **Ubicación**: `app/Filament/Resources/Customers/CustomerResource.php:23`
- **Estado**: ✅ **CORREGIDO** - Ahora usa `Customer::class` correctamente

### 6. **Convenciones de Enums (PERSISTENTE)**
- **Ubicación**: `app/Enums/CustomerQuoteStatus.php:7-10`
- **Estado**: ✅ **PARCIALMENTE CORREGIDO** - Enum mantiene casos en español pero estructura mejorada
- **Problema**: Casos siguen en español (convención de proyecto vs Laravel standard)
```php
// Actual
case Borrador = 'borrador';
case Enviada = 'enviada';
case Aprobada = 'aprobada';
case Rechazada = 'rechazada';
```

## Problemas de Organización Persistentes

### 7. **Falta de Type Hints en Scopes**
- **Ubicación**: `app/Models/Customer.php:76-98` y `app/Models/CustomerQuote.php:146-196`
- **Problema**: Múltiples scopes sin tipos de retorno
- **Estado**: No corregido
```php
public function scopeActive($query) // Falta return type
public function scopeSearch($query, string $search) // Falta return type
```

### 8. **Inconsistencia Crítica en Schema vs Código**
- **Schema Evidence**: 
  - Tabla `customer_quotes` con columna `customer_quote_id`
  - Foreign key constraint: `client_quote_groups_client_quote_id_foreign`
  - Índices: `client_quote_groups_pkey` (debería ser `customer_quote_groups_pkey`)
- **Problema**: Migración incompleta en level de database constraints

### 9. **Accessors/Mutators Híbridos Confusos**
- **Ubicación**: `app/Models/CustomerQuote.php:102-196`
- **Problema**: Mezcla de accessors para compatibilidad client/customer
- **Ejemplos**: `getClientNameAttribute()`, `getCustomerNameAttribute()`, `setClientIdAttribute()`
- **Anti-patrón**: Dualidad confusa en API del modelo

### 10. **Campos Fillable Duplicados para Transición**
- **Ubicación**: `app/Models/CustomerQuote.php:19-25`
- **Problema**: Acepta tanto `client_id`/`client_name` como `customer_id`/`customer_name`
- **Riesgo**: Datos inconsistentes en base de datos

## Problemas Nuevos Identificados

### 11. **Relaciones Incorrectas en Foreign Keys**
- **Schema Analysis**: 
  - `customer_quote_groups.customer_quote_id` → FK a `customer_quotes.id`
  - Pero el constraint se llama `client_quote_groups_client_quote_id_foreign`
- **Problema**: Naming inconsistente entre columnas y constraints

### 12. **Sequences con Nombres Legacy**
- **Database Sequences**: 
  - `client_quotes_id_seq` (debería ser `customer_quotes_id_seq`)
  - `client_quote_groups_id_seq`, etc.
- **Impacto**: Confusión en debugging y maintenance de DB

### 13. **Check Constraints con Valores en Español**
- **Ubicación**: Database check constraints
- **Ejemplo**: `client_quotes_status_check` valida `'borrador', 'enviada', 'aprobada', 'rechazada'`
- **Problema**: Mezcla de español en nivel de base de datos

## Estado de Correcciones

### ✅ **Corregido**
- Referencias de modelos en `CustomerResource.php`

### ❌ **Pendiente**
- 8 modelos usando `$casts` obsoleto
- Múltiples accessors usando patrón pre-Laravel 9
- Relaciones duplicadas client/customer
- Scopes sin type hints
- Inconsistencias en schema de base de datos
- Campos fillable duplicados

## Nuevas Recomendaciones Prioritarias

**Urgente:**
1. **Migración completa de database schema**: Renombrar constraints, índices y sequences
2. **Eliminar dualidad client/customer**: Elegir una nomenclatura y aplicarla consistentemente
3. **Actualizar todos los modelos a Laravel 12**: Migrar `$casts` y accessors

**Alta Prioridad:**
4. Agregar type hints faltantes a todos los scopes
5. Limpiar campos fillable duplicados
6. Corregir relaciones duplicadas

**Media Prioridad:**
7. Standardizar check constraints (español vs inglés)
8. Documentar decisión sobre naming convention del proyecto