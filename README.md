# Ambiente de desarrollo

## Credenciales sembradas (solo DEV)

- Admin: <EMAIL> / password
- Super Admin: <EMAIL> / password
- Panel User: <EMAIL> / password

## Notas de acceso

- Panel Filament: `/admin` (login en `/admin/login`)
- Acceso al panel: roles `admin` y `super_admin`
- `panel_user` no accede al panel (según `User::canAccessPanel`)

## RBAC (Spatie + Filament Shield)

- Roles base: `super_admin`, `admin`, `panel_user`
- Permisos generados por Shield (naming Pascal + ), con Policies
- Asignaciones por defecto (DEV):
  - `admin`: todos los permisos
  - `panel_user`: lectura de `User` (`ViewAny:User`, `View:User`)
  - `super_admin`: acceso total vía `Gate::before`

## Puesta en marcha

1. Docker Postgres
   - Host: 127.0.0.1, Puerto: 55432, DB: app, Usuario: app, Password: secret
2. <PERSON><PERSON>
   - Copiar `.env.example` a `.env`
   - `composer install`
   - `php artisan key:generate`
   - `php artisan migrate:fresh --seed`

## Convenciones de IDs

- PKs por convención: `id()` autoincremental (bigint). FKs con `foreignId()->constrained()`.
- Evitar UUID/ULID y `HasUuids` en modelos y migraciones.
- Validación en requests: `integer|exists:tabla,id`.

Checklist anti-UUID antes de mergear:
- Ejecuta: `rg -n "\buuid\b|HasUuids|ulid\(" --hidden --glob '!vendor/**' --glob '!public/js/**' --glob '!composer.lock'`
- Revisa migraciones y requests nuevos para asegurar `id()/foreignId()` y reglas `integer|exists`.
