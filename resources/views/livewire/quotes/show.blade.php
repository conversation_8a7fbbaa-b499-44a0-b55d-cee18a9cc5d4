<?php

use App\Models\CustomerQuote;
use App\DTOs\Currency;
use App\DTOs\Tax;
use Livewire\Attributes\Computed;
use Livewire\Volt\Component;

new class extends Component {
    public CustomerQuote $quote;

    public function mount(CustomerQuote $quote): void
    {
        // Validación básica
        if (!$quote) {
            abort(404, 'Cotización no encontrada');
        }

        // Eager loading optimizado para evitar N+1 (nueva estructura de taxonomía)
        $this->quote = $quote->loadMissing([
            'groups.products',
            'groups.products.variants',
            'products',
            'products.variants',
        ]);

    }

    public function groupUnitCount(\App\Models\CustomerQuoteGroup $group): ?int
    {
        $totals = $group->products
            ->map(fn($gp) => $gp->totalUnits())
            ->filter(fn($v) => $v > 0)
            ->unique()
            ->values();

        return $totals->count() === 1 ? $totals->first() : null;
    }

    #[Computed]
    public function subtotal(): float
    {
        try {
            // Suma total de grupos
            $groupTotal = $this->quote->groups->sum(fn($group) => $this->groupSubtotal($group));
            
            // Suma total de productos individuales
            $individualTotal = $this->quote->products->whereNull('group_id')->sum(fn($product) => $product->totalAmount());
            
            return $groupTotal + $individualTotal;
        } catch (\Exception $e) {
            \Log::error('Error calculating subtotal: ' . $e->getMessage());
            return 0.0;
        }
    }

    #[Computed]
    public function taxRate(): float
    {
        return Tax::rateFor($this->quote->country ?? null);
    }

    #[Computed]
    public function taxAmount(): float
    {
        return round($this->subtotal() * $this->taxRate(), 2);
    }

    #[Computed]
    public function total(): float
    {
        return round($this->subtotal() + $this->taxAmount(), 2);
    }

    public function moneySym(float $amount, ?string $currency = null): string
    {
        $code = strtoupper($currency ?: ($this->quote->currency ?? 'USD'));
        $dto = Currency::from($code);
        return $dto->format($amount);
    }

    public function groupSubtotal(\App\Models\CustomerQuoteGroup $group): float
    {
        try {
            return $group->products->sum(fn($gp) => $gp->totalAmount());
        } catch (\Exception $e) {
            \Log::error('Error calculating group subtotal: ' . $e->getMessage());
            return 0.0;
        }
    }

    public function avgUnitPrice(float $total, int $qty): ?float
    {
        if ($qty <= 0) return null;
        return round($total / $qty, 2);
    }

    public function refreshQuote(): void
    {
        try {
            $this->quote->refresh();
            $this->quote->loadMissing([
                'groups.products',
                'groups.products.variants',
                'products',
                'products.variants',
            ]);
        } catch (\Exception $e) {
            \Log::error('Error refreshing quote: ' . $e->getMessage());
        }
    }
}; ?>

<section class="max-w-5xl mx-auto p-6">
    <div class="mb-6">
        <div class="flex justify-between items-start">
            <h1 class="text-2xl font-semibold">Cotización Comercial</h1>
            <button 
                wire:click="refreshQuote" 
                class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                wire:loading.attr="disabled"
                wire:target="refreshQuote"
            >
                <span wire:loading.remove wire:target="refreshQuote">🔄 Refrescar</span>
                <span wire:loading wire:target="refreshQuote">⏳ Cargando...</span>
            </button>
        </div>
        <div class="mt-2 text-sm text-gray-600 dark:text-gray-300">
            <div><span class="font-medium">ID Interno:</span> <span class="font-mono">{{ $quote->id }}</span></div>
            <div><span class="font-medium">Cliente:</span> {{ $quote->customer_name }}</div>
            <div class="flex gap-4 flex-wrap">
                <span><span class="font-medium">Cotización No:</span> {{ $quote->quote_number ?? '—' }}</span>
                <span><span class="font-medium">Moneda:</span> {{ $quote->currency }}</span>
                <span><span class="font-medium">Estatus:</span> {{ is_string($quote->status) ? $quote->status : $quote->status->value }}</span>
                <span><span class="font-medium">Válida hasta:</span> {{ optional($quote->valid_until)?->toDateString() ?? '—' }}</span>
            </div>
        </div>
        @if ($quote->notes)
            <p class="mt-3 text-sm text-gray-700 dark:text-gray-200">{{ $quote->notes }}</p>
        @endif
    </div>

    <div>
        <h2 class="text-xl font-semibold mb-3">Artículos Cotizados</h2>
        @php($rootProducts = $quote->products->whereNull('group_id')->sortBy('position'))
        @if ($quote->groups->count() || $rootProducts->count())
            @php($i = 1)
            <div class="overflow-x-auto" wire:loading.class="opacity-50" wire:target="refreshQuote">
                <table class="min-w-[640px] text-sm">
                    <thead>
                        <tr class="text-left border-b border-gray-200 dark:border-gray-700">
                            <th class="py-2 pr-4 w-10">#</th>
                            <th class="py-2 pr-4">Descripción</th>
                            <th class="py-2 pr-4 text-right">Cantidad</th>
                            <th class="py-2 pr-4 text-right">Precio Unitario</th>
                            <th class="py-2 text-right">Total</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($quote->groups->sortBy('position') as $group)
                            @php($units = $group->totalUnitsUniform())
                            @php($gTotal = $group->totalAmount())
                            @php($gUnit = $group->avgUnitPrice())
                            <tr wire:key="group-{{ $group->id }}">
                                <td class="py-2 pr-4 align-top">{{ $i++ }}</td>
                                <td class="py-2 pr-4">
                                    <div class="font-medium">{{ $group->name }} <span class="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">[ID: <span class="font-mono">{{ $group->id }}</span>]</span></div>
                                    @if ($group->notes)
                                        <div class="text-gray-600 dark:text-gray-300">{{ $group->notes }}</div>
                                    @endif
                                    
                                    @if ($group->isKitWithFixedPrice())
                                        {{-- Kit con precio fijo --}}
                                        @php($kitErrors = $group->validateKitComposition())
                                        
                                        @if (!empty($kitErrors))
                                            <div class="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
                                                <div class="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                                                    ⚠️ Error en composición del kit:
                                                </div>
                                                @foreach ($kitErrors as $error)
                                                    <div class="text-xs text-red-700 dark:text-red-300">{{ $error }}</div>
                                                @endforeach
                                            </div>
                                        @endif
                                        
                                        <div class="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                                            <div class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                                                📦 Kit: {{ $group->quantity }} kits × {{ $group->unit_price->format() }} = {{ $quote->currencyDto()->format($group->totalAmount()) }}
                                            </div>
                                            <div class="text-xs text-blue-700 dark:text-blue-300">
                                                <div class="font-medium mb-1">Composición del kit:</div>
                                                @foreach ($group->products->sortBy('position') as $gp)
                                                    @if ($gp->units_per_kit)
                                                        <div wire:key="kit-composition-{{ $gp->id }}" class="ml-2">
                                                            • {{ $gp->name }}: {{ $gp->units_per_kit }} por kit
                                                            @if ($gp->variants->count())
                                                                <div class="ml-4 text-gray-600 dark:text-gray-400">
                                                                    @foreach ($gp->variants->sortBy('position') as $v)
                                                                        <div wire:key="kit-variant-{{ $v->id }}">
                                                                            ◦ {{ $v->label }}
                                                                            <span class="text-blue-600 dark:text-blue-400 font-medium">({{ $v->quantity }} unidades)</span>
                                                                        </div>
                                                                    @endforeach
                                                                </div>
                                                            @endif
                                                        </div>
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    @else
                                        {{-- Kit con precios de componentes (lógica original) --}}
                                        @if ($group->products->count())
                                            <div class="mt-2 text-xs text-gray-600 dark:text-gray-400 space-y-1">
                                                @foreach ($group->products->sortBy('position') as $gp)
                                                    <div wire:key="group-product-{{ $gp->id }}">{{ $gp->name }} — {{ $gp->totalUnits() }} uds <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">[item: <span class="font-mono">{{ $gp->id }}</span> · tipo: <span class="font-mono">{{ $gp->type_name }}</span>]</span></div>
                                                    @if ($gp->variants->count())
                                                        <div class="ms-4">
                                                            @foreach ($gp->variants->sortBy('position') as $v)
                                                                <div wire:key="group-variant-{{ $v->id }}">◦ Variante: {{ $v->label }} — {{ $v->quantity }} uds <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">[varItem: <span class="font-mono">{{ $v->id }}</span>]</span></div>
                                                            @endforeach
                                                        </div>
                                                    @endif
                                                @endforeach
                                            </div>
                                        @endif
                                    @endif
                                </td>
                                <td class="py-2 pr-4 text-right align-top">{{ $units ?? '—' }}</td>
                                <td class="py-2 pr-4 text-right align-top">{{ $gUnit !== null ? $quote->currencyDto()->format($gUnit) : '—' }}</td>
                                <td class="py-2 text-right align-top">{{ $quote->currencyDto()->format($gTotal) }}</td>
                            </tr>
                        @endforeach

                        @foreach ($rootProducts as $rp)
                            @php($pUnits = $rp->totalUnits())
                            @php($pTotal = $rp->totalAmount())
                            @php($pUnit = $rp->avgUnitPrice())
                            <tr wire:key="product-{{ $rp->id }}">
                                <td class="py-2 pr-4 align-top">{{ $i++ }}</td>
                                <td class="py-2 pr-4">
                                    <div class="font-medium">{{ $rp->name }} <span class="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">[item: <span class="font-mono">{{ $rp->id }}</span> · tipo: <span class="font-mono">{{ $rp->type_name }}</span>]</span></div>
                                    @if ($rp->notes)
                                        <div class="text-gray-600 dark:text-gray-300">{{ $rp->notes }}</div>
                                    @endif
                                    @if ($rp->variants->count())
                                        <div class="mt-2 text-xs text-gray-600 dark:text-gray-400 space-y-1">
                                            @foreach ($rp->variants->sortBy('position') as $v)
                                                <div wire:key="product-variant-{{ $v->id }}">◦ Variante: {{ $v->label }} — {{ $v->quantity }} × {{ $v->money('unit_price') }} = {{ $quote->currencyDto()->format($v->totalAmount()) }} <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">[varItem: <span class="font-mono">{{ $v->id }}</span>]</span></div>
                                            @endforeach
                                        </div>
                                    @endif
                                </td>
                                <td class="py-2 pr-4 text-right align-top">{{ $pUnits }}</td>
                                <td class="py-2 pr-4 text-right align-top">{{ $pUnit !== null ? $quote->currencyDto()->format($pUnit) : '—' }}</td>
                                <td class="py-2 text-right align-top">{{ $quote->currencyDto()->format($pTotal) }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-sm text-gray-600 dark:text-gray-300">No hay artículos para mostrar.</div>
        @endif
    </div>

    <div class="mt-10">
        <h2 class="text-xl font-semibold mb-3">Resumen Financiero</h2>
        @php($subtotal = $quote->subtotalAmount())
        <div class="overflow-x-auto">
            <table class="min-w-[320px] text-sm">
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    <tr>
                        <td class="py-2 pr-6 font-medium">Subtotal</td>
                        <td class="py-2 text-right">{{ $quote->money($subtotal) }}</td>
                    </tr>
                    <tr>
                        <td class="py-2 pr-6 font-medium">IVA {{ $quote->country ? '(' . strtoupper($quote->country) . ')' : '' }} ({{ number_format($quote->taxRate()*100, 0) }}%)</td>
                        <td class="py-2 text-right">{{ $quote->money($quote->taxAmount()) }}</td>
                    </tr>
                    <tr>
                        <td class="py-2 pr-6 font-semibold">Total a Pagar</td>
                        <td class="py-2 text-right font-semibold">{{ $quote->money($quote->totalAmount()) }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        @if ($subtotal == 0.0)
            <div class="mt-2 text-xs text-gray-600 dark:text-gray-400">Precios no definidos; el total se muestra como 0. Configure precios unitarios por variante para calcular montos.</div>
        @endif
    </div>
</section>
