<?php

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteGroup;
use App\Models\ProductType;
use App\Services\ProductTypeRules;
use Livewire\Attributes\Computed;
use Livewire\Volt\Component;

new class extends Component {
    public CustomerQuote $quote;
    public bool $showDetails = true;
    public bool $showVariantsOnly = false;
    public bool $attentionOnly = false;
    public ?string $filterCurrency = null; // null/todas
    public array $variantRequiredKeys = [];

    public function mount(CustomerQuote $quote): void
    {
        if (!$quote) abort(404, 'Cotización no encontrada');

        // Carga optimizada: grupos → productos → variantes, y productos raíz → variantes
        $this->quote = $quote->loadMissing([
            'groups.products.variants',
            'products.variants',
        ]);

        // Reglas requeridas por tipo (variante)
        $typeIds = collect([
            ...$this->quote->groups->flatMap(fn($g) => $g->products)->pluck('type_id'),
            ...$this->quote->products->pluck('type_id'),
        ])->filter()->unique();
        if ($typeIds->isNotEmpty()) {
            $types = ProductType::query()->whereIn('id', $typeIds)->get(['id','reglas']);
            foreach ($types as $t) {
                $rules = ProductTypeRules::variantAttributeRules($t);
                $req = [];
                foreach ($rules as $k => $r) {
                    $arr = is_array($r) ? $r : [$r];
                    if (in_array('required', $arr, true)) $req[] = (string) $k;
                }
                $this->variantRequiredKeys[(int) $t->id] = $req;
            }
        }
    }

    #[Computed]
    public function groupSubtotal(CustomerQuoteGroup $group): float
    {
        return (float) $group->products->sum(fn ($p) => $p->totalAmount());
    }

    #[Computed]
    public function kpis(): array
    {
        return [
            'groups' => $this->quote->groups->count(),
            'products' => $this->quote->products->count(),
            'variants' => $this->quote->products->sum(fn($p) => $p->variants->count()),
            'units' => (int) $this->quote->products->sum(fn($p) => $p->totalUnits()),
        ];
    }

    #[Computed]
    public function currencies(): array
    {
        return $this->quote->products->flatMap(fn($p) => $p->variants->pluck('price_currency'))
            ->filter()->unique()->sort()->values()->all();
    }

    public function missingRequiredForVariant($product, $variant): array
    {
        $req = $this->variantRequiredKeys[(int) ($product->type_id ?? 0)] ?? [];
        $attr = (array) ($variant->attributes ?? []);
        return array_values(array_filter($req, fn($k) => !array_key_exists($k, $attr) || $attr[$k] === null || $attr[$k] === ''));
    }

    #[Computed]
    public function variantRows(): array
    {
        $rows = [];
        $gIndex = 0;
        foreach ($this->quote->groups->sortBy('position') as $group) {
            $gIndex++;
            $pIndex = 0;
            foreach ($group->products->sortBy('position') as $p) {
                $pIndex++;
                $vIndex = 0;
                foreach ($p->variants->sortBy('position') as $v) {
                    $vIndex++;
                    $rows[] = [
                        'code' => sprintf('G%02d-P%02d-V%02d', $gIndex, $pIndex, $vIndex),
                        'group_name' => $group->name,
                        'product_name' => $p->name,
                        'product_id' => $p->id,
                        'variant_id' => $v->id,
                        'label' => $v->label,
                        'qty' => (int) $v->quantity,
                        'unit_price' => $v->money('unit_price'),
                        'price_currency' => $v->price_currency,
                        'fx' => (string) $v->fx_rate_to_quote,
                        'attrs' => (array) ($v->attributes ?? []),
                        'missing' => $this->missingRequiredForVariant($p, $v),
                    ];
                }
            }
        }
        // Root products
        $pIndex = 0;
        foreach ($this->quote->products->whereNull('group_id')->sortBy('position') as $p) {
            $pIndex++;
            $vIndex = 0;
            foreach ($p->variants->sortBy('position') as $v) {
                $vIndex++;
                $rows[] = [
                    'code' => sprintf('R-P%02d-V%02d', $pIndex, $vIndex),
                    'group_name' => '(Raíz)',
                    'product_name' => $p->name,
                    'product_id' => $p->id,
                    'variant_id' => $v->id,
                    'label' => $v->label,
                    'qty' => (int) $v->quantity,
                    'unit_price' => $v->money('unit_price'),
                    'price_currency' => $v->price_currency,
                    'fx' => (string) $v->fx_rate_to_quote,
                    'attrs' => (array) ($v->attributes ?? []),
                    'missing' => $this->missingRequiredForVariant($p, $v),
                ];
            }
        }

        return collect($rows)
            ->when($this->filterCurrency, fn($c) => $c->where('price_currency', $this->filterCurrency))
            ->when($this->attentionOnly, fn($c) => $c->filter(fn($r) => !empty($r['missing'])))
            ->values()->all();
    }
}; ?>

<section class="max-w-6xl mx-auto p-6">
    <div class="mb-6">
        <div class="flex items-start justify-between gap-4">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Plan de Producción — Estructura</h1>
                <p class="text-sm text-gray-700 dark:text-gray-300 mt-1">
                    Cotización:
                    <span class="inline-flex items-center rounded px-2 py-0.5 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 font-mono">{{ $quote->quote_number ?? '—' }}</span>
                    · Cliente: <span class="font-semibold">{{ $quote->customer_name }}</span>
                    · Moneda: <span class="font-mono">{{ $quote->currency }}</span>
                </p>
            </div>
            <div class="flex items-start gap-3">
                <button wire:click="$toggle('showDetails')" type="button"
                    class="px-3 py-1.5 text-xs rounded border border-gray-300 dark:border-gray-600 bg-transparent dark:bg-transparent text-gray-800 dark:text-gray-100 hover:bg-gray-50 hover:dark:bg-gray-800">
                    {{ $showDetails ? 'Ocultar detalles' : 'Mostrar detalles' }}
                </button>
                <button wire:click="$toggle('showVariantsOnly')" type="button"
                    class="px-3 py-1.5 text-xs rounded border border-gray-300 dark:border-gray-600 bg-transparent dark:bg-transparent text-gray-800 dark:text-gray-100 hover:bg-gray-50 hover:dark:bg-gray-800">
                    {{ $showVariantsOnly ? 'Ver estructura' : 'Sólo variantes' }}
                </button>
                <div class="text-right">
                    <div class="text-xs text-gray-600 dark:text-gray-300">Estado</div>
                    <div class="text-sm font-medium uppercase text-gray-900 dark:text-gray-50">{{ is_string($quote->status) ? $quote->status : ($quote->status?->value ?? '') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPIs -->
    <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6">
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Grupos</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $this->kpis()['groups'] }}</div>
        </div>
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Productos</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $this->kpis()['products'] }}</div>
        </div>
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Variantes</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $this->kpis()['variants'] }}</div>
        </div>
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Unidades</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $this->kpis()['units'] }}</div>
        </div>
    </div>

    <!-- Variantes a cotizar -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-2">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Variantes a cotizar</h2>
            <div class="flex items-center gap-2">
                <label class="text-xs text-gray-600 dark:text-gray-300">Moneda</label>
                <select wire:model.live="filterCurrency"
                        class="text-xs rounded border border-gray-300 dark:border-gray-600 bg-transparent dark:bg-transparent text-gray-800 dark:text-gray-100 px-2 py-1">
                    <option value="">Todas</option>
                    @foreach ($this->currencies() as $cur)
                        <option value="{{ $cur }}">{{ $cur }}</option>
                    @endforeach
                </select>
                <label class="inline-flex items-center gap-1 text-xs text-gray-800 dark:text-gray-100">
                    <input type="checkbox" wire:model.live="attentionOnly"
                           class="rounded border-gray-300 dark:border-gray-500 bg-white dark:bg-gray-900 text-indigo-500 focus:ring-indigo-500" />
                    Atención (faltan atributos)
                </label>
            </div>
        </div>
                <div class="overflow-x-auto rounded border border-gray-200 dark:border-gray-700 shadow-sm">
            <table class="min-w-full text-sm bg-transparent dark:bg-transparent">
                <thead class="sticky top-0 z-10 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-100">
                    <tr>
                        <th class="text-left px-3 py-2">Código</th>
                        <th class="text-left px-3 py-2">Producto</th>
                        <th class="text-left px-3 py-2">Variante</th>
                        <th class="text-right px-3 py-2">Unidades</th>
                        <th class="text-right px-3 py-2">Precio Unit.</th>
                        <th class="text-right px-3 py-2">Moneda</th>
                        <th class="text-right px-3 py-2">FX→{{ $quote->currency }}</th>
                        <th class="text-left px-3 py-2">Atributos</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-100 dark:divide-gray-700 text-gray-900 dark:text-gray-100 bg-transparent dark:bg-transparent">
                    @forelse ($this->variantRows() as $row)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-3 py-2 font-mono text-xs">{{ $row['code'] }}</td>
                            <td class="px-3 py-2">
                                <div class="text-sm font-medium">{{ $row['product_name'] }}</div>
                                <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ $row['group_name'] }}</div>
                            </td>
                            <td class="px-3 py-2">
                                <div class="text-sm">{{ $row['label'] }}</div>
                                @if(!empty($row['missing']))
                                    <div class="mt-0.5 text-[11px] text-red-600 dark:text-red-400">Faltan: {{ implode(', ', $row['missing']) }}</div>
                                @else
                                    <div class="mt-0.5 text-[11px] text-green-600 dark:text-green-400">✓ Completa</div>
                                @endif
                            </td>
                            <td class="px-3 py-2 text-right">{{ $row['qty'] }}</td>
                            <td class="px-3 py-2 text-right">{{ $row['unit_price'] }}</td>
                            <td class="px-3 py-2 text-right">
                                <span class="inline-flex items-center rounded px-2 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200">{{ $row['price_currency'] }}</span>
                            </td>
                            <td class="px-3 py-2 text-right">
                                @php($fxf = (float) $row['fx'])
                                <span class="inline-flex items-center rounded px-2 py-0.5 {{ $fxf !== 1.0 ? 'bg-amber-50 dark:bg-amber-800/60 text-amber-800 dark:text-amber-200 ring-1 ring-amber-200 dark:ring-amber-700' : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-100 ring-1 ring-gray-200 dark:ring-gray-600' }}">
                                    {{ $row['fx'] }}
                                </span>
                            </td>
                            <td class="px-3 py-2">
                                <div class="flex flex-wrap gap-1.5">
                                    @foreach ($row['attrs'] as $k => $v)
                                        <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 px-2 py-0.5 text-xs ring-1 ring-gray-200 dark:ring-gray-600">
                                            <span class="font-medium">{{ $k }}</span>
                                            <span class="mx-1 opacity-50">=</span>
                                            <span class="font-mono">{{ is_array($v) ? json_encode($v) : (string) $v }}</span>
                                        </span>
                                    @endforeach
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" class="px-3 py-4 text-center text-sm text-gray-600 dark:text-gray-300">No hay variantes para mostrar con los filtros actuales.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    @if (!$showVariantsOnly)
    <div class="space-y-8">
        @if ($quote->groups->count())
            <div>
                <h2 class="text-lg font-semibold mb-3">Grupos</h2>
                <div class="overflow-x-auto rounded border border-gray-200 dark:border-gray-700 shadow-sm">
                    <table class="min-w-full text-sm bg-transparent dark:bg-transparent">
                        <thead class="sticky top-0 z-10 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-100">
                            <tr>
                                <th class="text-left px-4 py-2">#</th>
                                <th class="text-left px-4 py-2">Grupo</th>
                                <th class="text-right px-4 py-2">Unidades</th>
                                <th class="text-right px-4 py-2">Subtotal</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-100 dark:divide-gray-700 text-gray-900 dark:text-gray-100 bg-transparent dark:bg-transparent">
                            @php($i = 1)
                            @foreach ($quote->groups->sortBy('position') as $group)
                                @php($gUnits = $group->products->sum(fn($p) => $p->totalUnits()))
                                @php($gTotal = $group->products->sum(fn($p) => $p->totalAmount()))
                                <tr>
                                    <td class="px-4 py-2 align-top text-gray-500">{{ $i++ }}</td>
                                    <td class="px-4 py-2 align-top">
                                        <div class="font-medium">{{ $group->name }} <span class="ml-2 text-xs text-gray-500">[ID: {{ $group->id }}]</span></div>
                                        @if($group->notes)
                                            <div class="text-gray-600 dark:text-gray-300">{{ $group->notes }}</div>
                                        @endif
                                        @if($group->products->count())
                                            <div class="mt-2 space-y-2">
                                                @foreach ($group->products->sortBy('position') as $p)
                                                    <div class="border-l-2 border-gray-200 dark:border-gray-700 pl-3">
                                                        <div class="flex items-start justify-between">
                                                            <div>
                                                                <div class="font-medium">{{ $p->name }}
                                                                    <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">
                                                                        <span class="inline-flex items-center rounded px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200">tipo: <span class="ml-1 font-mono">{{ $p->type_name }}</span> <span class="ml-1 font-mono">({{ $p->type_code }})</span></span>
                                                                        <span class="inline-flex items-center rounded px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 ml-1">sub: <span class="ml-1 font-mono">{{ $p->subcategory_code }}</span></span>
                                                                        <span class="inline-flex items-center rounded px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 ml-1">cat: <span class="ml-1 font-mono">{{ $p->category_code }}</span></span>
                                                                    </span>
                                                                </div>

                                                                @if($showDetails)
                                                                    <div class="mt-1 text-xs text-gray-700 dark:text-gray-300">
                                                                        <div class="flex flex-col gap-1">
                                                                            @if(is_array($p->attributes) && count($p->attributes))
                                                                                <div>
                                                                                    <span class="font-semibold">Atributos:</span>
                                        <ul class="mt-1 flex flex-wrap gap-1.5">
                                            @foreach ($p->attributes as $k => $v)
                                                <li class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 px-2 py-0.5 ring-1 ring-gray-200 dark:ring-gray-600">
                                                    <span class="font-medium">{{ $k }}</span>
                                                    <span class="mx-1 opacity-50">=</span>
                                                    <span class="font-mono">{{ is_array($v) ? json_encode($v) : (string) $v }}</span>
                                                </li>
                                            @endforeach
                                        </ul>
                                                                                </div>
                                                                            @endif
                                                                            @if(is_array($p->specs) && count($p->specs))
                                                                                <div>
                                                                                    <span class="font-semibold">Especificaciones:</span>
                                                                                    <ul class="mt-1 flex flex-wrap gap-1.5">
                                                                                        @foreach ($p->specs as $k => $v)
                                                                                            <li class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-2 py-0.5">
                                                                                                <span class="font-medium">{{ $k }}</span>
                                                                                                <span class="mx-1 opacity-50">=</span>
                                                                                                <span class="font-mono">{{ is_array($v) ? json_encode($v) : (string) $v }}</span>
                                                                                            </li>
                                                                                        @endforeach
                                                                                    </ul>
                                                                                </div>
                                                                            @endif
                                                                            <div class="flex flex-wrap gap-2">
                                                                                @if($p->hs_code)
                                                                                    <span class="inline-flex items-center rounded bg-indigo-50 dark:bg-indigo-900/40 text-indigo-700 dark:text-indigo-200 px-2 py-0.5 text-[11px]">HS: <span class="ml-1 font-mono">{{ $p->hs_code }}</span></span>
                                                                                @endif
                                                                                @if($p->weight)
                                                                                    <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-[11px]">Peso: <span class="ml-1 font-mono">{{ $p->weight }}</span></span>
                                                                                @endif
                                                                                @if($p->volume)
                                                                                    <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-[11px]">Volumen: <span class="ml-1 font-mono">{{ $p->volume }}</span></span>
                                                                                @endif
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                @endif
                                                            </div>
                                                            <div class="text-right text-xs text-gray-600 dark:text-gray-400">
                                                                <div>Unidades: <span class="font-semibold">{{ $p->totalUnits() }}</span></div>
                                                                <div>Subtotal: <span class="font-semibold">{{ $quote->currencyDto()->format($p->totalAmount()) }}</span></div>
                                                            </div>
                                                        </div>
                                                        @if($p->variants->count())
                                                            <div class="mt-1 ms-2 text-xs text-gray-800 dark:text-gray-200">
                                                                @foreach ($p->variants->sortBy('position') as $v)
                                                                    <div class="flex items-start justify-between py-1">
                                                                        <div>
                                                                            ◦ <span class="font-medium">{{ $v->label }}</span>
                                                                            <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">[var: {{ $v->id }}]</span>
                                                                            @if($showDetails && is_array($v->attributes) && count($v->attributes))
                                            <ul class="mt-0.5 inline-flex flex-wrap gap-1.5 align-middle">
                                                @foreach ($v->attributes as $k => $val)
                                                    <li class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100 px-2 py-0.5 ring-1 ring-gray-200 dark:ring-gray-600">
                                                        <span class="font-medium">{{ $k }}</span>
                                                        <span class="mx-1 opacity-50">=</span>
                                                        <span class="font-mono">{{ is_array($val) ? json_encode($val) : (string) $val }}</span>
                                                    </li>
                                                @endforeach
                                            </ul>
                                                                            @endif
                                                                            @if($showDetails)
                                                                                <span class="ml-2 space-x-2">
                                                                                    @if($v->hs_code)
                                                                                        <span class="inline-flex items-center rounded bg-indigo-50 dark:bg-indigo-900/40 text-indigo-700 dark:text-indigo-200 px-2 py-0.5 text-[11px]">HS: <span class="ml-1 font-mono">{{ $v->hs_code }}</span></span>
                                                                                    @endif
                                                                                    @if($v->weight)
                                                                                        <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-[11px]">Peso: <span class="ml-1 font-mono">{{ $v->weight }}</span></span>
                                                                                    @endif
                                                                                    @if($v->volume)
                                                                                        <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-[11px]">Volumen: <span class="ml-1 font-mono">{{ $v->volume }}</span></span>
                                                                                    @endif
                                                                                </span>
                                                                            @endif
                                                                        </div>
                                                                        <div class="text-right">
                                                                            <div>{{ $v->quantity }} × {{ $v->money('unit_price') }}</div>
                                                                            <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ $v->price_currency }} @if($v->fx_rate_to_quote && (float)$v->fx_rate_to_quote !== 1.0) · FX→{{ $quote->currency }}: {{ $v->fx_rate_to_quote }} @endif</div>
                                                                        </div>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        @endif
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </td>
                                    <td class="px-4 py-2 align-top text-right">{{ $gUnits }}</td>
                                    <td class="px-4 py-2 align-top text-right">{{ $quote->currencyDto()->format($gTotal) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif

        <div>
            <h2 class="text-lg font-semibold mb-3">Productos en Raíz</h2>
            @php($root = $quote->products->whereNull('group_id'))
            @if($root->count())
                <div class="overflow-x-auto rounded border border-gray-200 dark:border-gray-700 shadow-sm">
                    <table class="min-w-full text-sm">
                        <thead class="bg-gray-50 dark:bg-gray-800/60 text-gray-700 dark:text-gray-200">
                            <tr>
                                <th class="text-left px-4 py-2">#</th>
                                <th class="text-left px-4 py-2">Producto</th>
                                <th class="text-right px-4 py-2">Unidades</th>
                                <th class="text-right px-4 py-2">Subtotal</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-100 dark:divide-gray-800 text-gray-900 dark:text-gray-100">
                            @php($j = 1)
                            @foreach ($root->sortBy('position') as $p)
                                <tr>
                                    <td class="px-4 py-2 align-top text-gray-500">{{ $j++ }}</td>
                                    <td class="px-4 py-2 align-top">
                                        <div class="font-medium">{{ $p->name }} <span class="ml-2 text-[11px] text-gray-500">[item: {{ $p->id }} · tipo: {{ $p->type_name }} ({{ $p->type_code }}) · subcat: {{ $p->subcategory_code }} · cat: {{ $p->category_code }}]</span></div>
                                        @if($showDetails)
                                            <div class="text-xs text-gray-700 dark:text-gray-300">
                                                @if(is_array($p->attributes) && count($p->attributes))
                                                    <div class="mt-1">
                                                        <span class="font-semibold">Atributos:</span>
                                                        <ul class="mt-1 flex flex-wrap gap-1.5">
                                                            @foreach ($p->attributes as $k => $v)
                                                                <li class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-2 py-0.5">
                                                                    <span class="font-medium">{{ $k }}</span>
                                                                    <span class="mx-1 opacity-50">=</span>
                                                                    <span class="font-mono">{{ is_array($v) ? json_encode($v) : (string) $v }}</span>
                                                                </li>
                                                            @endforeach
                                                        </ul>
                                                    </div>
                                                @endif
                                                <div class="mt-1 flex flex-wrap gap-2">
                                                    @if($p->hs_code)
                                                        <span class="inline-flex items-center rounded bg-indigo-50 dark:bg-indigo-900/40 text-indigo-700 dark:text-indigo-200 px-2 py-0.5 text-[11px]">HS: <span class="ml-1 font-mono">{{ $p->hs_code }}</span></span>
                                                    @endif
                                                    @if($p->weight)
                                                        <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-[11px]">Peso: <span class="ml-1 font-mono">{{ $p->weight }}</span></span>
                                                    @endif
                                                    @if($p->volume)
                                                        <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-[11px]">Volumen: <span class="ml-1 font-mono">{{ $p->volume }}</span></span>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                        @if($p->variants->count())
                                            <div class="mt-1 ms-2 text-xs text-gray-800 dark:text-gray-200">
                                                @foreach ($p->variants->sortBy('position') as $v)
                                                    <div class="flex items-start justify-between py-0.5">
                                                        <div>
                                                            ◦ <span class="font-medium">{{ $v->label }}</span>
                                                            <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">[var: {{ $v->id }}]</span>
                                                            @if($showDetails && is_array($v->attributes) && count($v->attributes))
                                                                <ul class="mt-0.5 inline-flex flex-wrap gap-1.5 align-middle">
                                                                    @foreach ($v->attributes as $k => $val)
                                                                        <li class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-2 py-0.5">
                                                                            <span class="font-medium">{{ $k }}</span>
                                                                            <span class="mx-1 opacity-50">=</span>
                                                                            <span class="font-mono">{{ is_array($val) ? json_encode($val) : (string) $val }}</span>
                                                                        </li>
                                                                    @endforeach
                                                                </ul>
                                                            @endif
                                                            @if($showDetails)
                                                                <span class="ml-2 space-x-2">
                                                                    @if($v->hs_code)
                                                                        <span class="inline-flex items-center rounded bg-indigo-50 dark:bg-indigo-900/40 text-indigo-700 dark:text-indigo-200 px-2 py-0.5 text-[11px]">HS: <span class="ml-1 font-mono">{{ $v->hs_code }}</span></span>
                                                                    @endif
                                                                    @if($v->weight)
                                                                        <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-[11px]">Peso: <span class="ml-1 font-mono">{{ $v->weight }}</span></span>
                                                                    @endif
                                                                    @if($v->volume)
                                                                        <span class="inline-flex items-center rounded bg-gray-100 dark:bg-gray-800 px-2 py-0.5 text-[11px]">Volumen: <span class="ml-1 font-mono">{{ $v->volume }}</span></span>
                                                                    @endif
                                                                </span>
                                                            @endif
                                                        </div>
                                                        <div class="text-right">
                                                            <div>{{ $v->quantity }} × {{ $v->money('unit_price') }}</div>
                                                            <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ $v->price_currency }} @if($v->fx_rate_to_quote && (float)$v->fx_rate_to_quote !== 1.0) · FX→{{ $quote->currency }}: {{ $v->fx_rate_to_quote }} @endif</div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </td>
                                    <td class="px-4 py-2 align-top text-right">{{ $p->totalUnits() }}</td>
                                    <td class="px-4 py-2 align-top text-right">{{ $quote->currencyDto()->format($p->totalAmount()) }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-sm text-gray-600 dark:text-gray-300">No hay productos en raíz.</div>
            @endif
        </div>
    </div>
    @endif

    <div class="mt-8">
        <h2 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Totales</h2>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 text-sm">
            <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
                <div class="text-gray-600 dark:text-gray-300">Subtotal</div>
                <div class="text-base font-medium text-gray-900 dark:text-gray-50">{{ $quote->money($quote->subtotalAmount()) }}</div>
            </div>
            <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
                <div class="text-gray-600 dark:text-gray-300">IVA ({{ number_format($quote->taxRate()*100, 0) }}%)</div>
                <div class="text-base font-medium text-gray-900 dark:text-gray-50">{{ $quote->money($quote->taxAmount()) }}</div>
            </div>
            <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
                <div class="text-gray-600 dark:text-gray-300">Total</div>
                <div class="text-base font-semibold text-gray-900 dark:text-gray-50">{{ $quote->money($quote->totalAmount()) }}</div>
            </div>
        </div>
    </div>
</section>
