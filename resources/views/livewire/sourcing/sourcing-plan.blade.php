<?php

use App\Models\CustomerQuote;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\ProductionBatch;
use App\Models\Supplier;
use Livewire\Attributes\Computed;
use Livewire\Volt\Component;

new class extends Component {
    public CustomerQuote $quote;
    public bool $showDetails = true;
    public bool $showBatchesOnly = false;
    public bool $attentionOnly = false;
    public ?string $filterSupplier = null;
    public ?string $filterStatus = null;

    public function mount(CustomerQuote $quote): void
    {
        if (!$quote) abort(404, 'Cotización no encontrada');

        // Carga optimizada: cotización → órdenes de compra → items → lotes → suppliers
        $this->quote = $quote->loadMissing([
            'purchaseOrders.supplier',
            'purchaseOrders.items.quoteProductVariant.quoteProduct',
            'purchaseOrders.items.productionBatches',
        ]);
    }

    #[Computed]
    public function kpis(): array
    {
        $purchaseOrders = $this->quote->purchaseOrders;
        $items = $purchaseOrders->flatMap(fn($po) => $po->items);
        $batches = $items->flatMap(fn($item) => $item->productionBatches);

        return [
            'suppliers' => $purchaseOrders->pluck('supplier')->unique('id')->count(),
            'purchase_orders' => $purchaseOrders->count(),
            'items' => $items->count(),
            'batches' => $batches->count(),
            'total_quantity' => $items->sum('quantity'),
            'total_value' => $items->sum(fn($item) => $item->quantity * $item->unit_price),
        ];
    }

    #[Computed]
    public function suppliers(): array
    {
        return $this->quote->purchaseOrders
            ->pluck('supplier')
            ->unique('id')
            ->sortBy('name')
            ->values()
            ->all();
    }

    #[Computed]
    public function statuses(): array
    {
        return $this->quote->purchaseOrders
            ->pluck('status')
            ->unique()
            ->sort()
            ->values()
            ->all();
    }

    #[Computed]
    public function purchaseOrderRows(): array
    {
        $rows = [];
        
        foreach ($this->quote->purchaseOrders->sortBy('id') as $po) {
            $rows[] = [
                'po_id' => $po->id,
                'supplier_name' => $po->supplier->name,
                'supplier_code' => $po->supplier->code,
                'status' => $po->status,
                'currency' => $po->currency,
                'incoterm' => $po->incoterm,
                'terms' => $po->terms,
                'notes' => $po->notes,
                'items_count' => $po->items->count(),
                'total_quantity' => $po->items->sum('quantity'),
                'total_value' => $po->items->sum(fn($item) => $item->quantity * $item->unit_price),
                'batches_count' => $po->items->flatMap(fn($item) => $item->productionBatches)->count(),
            ];
        }

        return collect($rows)
            ->when($this->filterSupplier, fn($c) => $c->where('supplier_code', $this->filterSupplier))
            ->when($this->filterStatus, fn($c) => $c->where('status', $this->filterStatus))
            ->values()
            ->all();
    }

    #[Computed]
    public function itemRows(): array
    {
        $rows = [];
        
        foreach ($this->quote->purchaseOrders as $po) {
            foreach ($po->items as $item) {
                $variant = $item->quoteProductVariant;
                $product = $variant->quoteProduct;
                
                $rows[] = [
                    'po_id' => $po->id,
                    'item_id' => $item->id,
                    'supplier_name' => $po->supplier->name,
                    'supplier_code' => $po->supplier->code,
                    'product_name' => $product->name,
                    'variant_label' => $variant->label,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'lead_time_days' => $item->lead_time_days,
                    'total_value' => $item->quantity * $item->unit_price,
                    'batches_count' => $item->productionBatches->count(),
                    'batches_quantity' => $item->productionBatches->sum('quantity'),
                    'notes' => $item->notes,
                ];
            }
        }

        return collect($rows)
            ->when($this->filterSupplier, fn($c) => $c->where('supplier_code', $this->filterSupplier))
            ->values()
            ->all();
    }

    #[Computed]
    public function batchRows(): array
    {
        $rows = [];
        
        foreach ($this->quote->purchaseOrders as $po) {
            foreach ($po->items as $item) {
                foreach ($item->productionBatches as $batch) {
                    $variant = $item->quoteProductVariant;
                    $product = $variant->quoteProduct;
                    
                    $rows[] = [
                        'batch_id' => $batch->id,
                        'po_id' => $po->id,
                        'item_id' => $item->id,
                        'supplier_name' => $po->supplier->name,
                        'supplier_code' => $po->supplier->code,
                        'product_name' => $product->name,
                        'variant_label' => $variant->label,
                        'quantity' => $batch->quantity,
                        'status' => $batch->status,
                        'pool_state' => $batch->pool_state,
                        'planned_start' => $batch->planned_start,
                        'planned_finish' => $batch->planned_finish,
                        'notes' => $batch->notes,
                    ];
                }
            }
        }

        return collect($rows)
            ->when($this->filterSupplier, fn($c) => $c->where('supplier_code', $this->filterSupplier))
            ->when($this->filterStatus, fn($c) => $c->where('status', $this->filterStatus))
            ->values()
            ->all();
    }

    public function getStatusColor(string $status): string
    {
        return match($status) {
            'borrador' => 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
            'pending' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200',
            'confirmed' => 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200',
            'in_production' => 'bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-200',
            'completed' => 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200',
            'cancelled' => 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-200',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
        };
    }

    public function getPoolStateColor(string $poolState): string
    {
        return match($poolState) {
            'unassigned' => 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
            'available' => 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200',
            'assigned' => 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-200',
            'in_progress' => 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-200',
            'completed' => 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-200',
            default => 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
        };
    }
}; ?>

<section class="max-w-7xl mx-auto p-6">
    <div class="mb-6">
        <div class="flex items-start justify-between gap-4">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100">Plan de Sourcing — Órdenes de Compra</h1>
                <p class="text-sm text-gray-700 dark:text-gray-300 mt-1">
                    Cotización:
                    <span class="inline-flex items-center rounded px-2 py-0.5 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100 font-mono">{{ $quote->quote_number ?? '—' }}</span>
                    · Cliente: <span class="font-semibold">{{ $quote->customer_name }}</span>
                    · Moneda: <span class="font-mono">{{ $quote->currency }}</span>
                </p>
            </div>
            <div class="flex items-start gap-3">
                <button wire:click="$toggle('showDetails')" type="button"
                    class="px-3 py-1.5 text-xs rounded border border-gray-300 dark:border-gray-600 bg-transparent dark:bg-transparent text-gray-800 dark:text-gray-100 hover:bg-gray-50 hover:dark:bg-gray-800">
                    {{ $showDetails ? 'Ocultar detalles' : 'Mostrar detalles' }}
                </button>
                <button wire:click="$toggle('showBatchesOnly')" type="button"
                    class="px-3 py-1.5 text-xs rounded border border-gray-300 dark:border-gray-600 bg-transparent dark:bg-transparent text-gray-800 dark:text-gray-100 hover:bg-gray-50 hover:dark:bg-gray-800">
                    {{ $showBatchesOnly ? 'Ver órdenes' : 'Sólo lotes' }}
                </button>
                <div class="text-right">
                    <div class="text-xs text-gray-600 dark:text-gray-300">Estado</div>
                    <div class="text-sm font-medium uppercase text-gray-900 dark:text-gray-50">{{ is_string($quote->status) ? $quote->status : ($quote->status?->value ?? '') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPIs -->
    <div class="grid grid-cols-2 sm:grid-cols-6 gap-3 mb-6">
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Suppliers</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $this->kpis()['suppliers'] }}</div>
        </div>
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Órdenes</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $this->kpis()['purchase_orders'] }}</div>
        </div>
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Items</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $this->kpis()['items'] }}</div>
        </div>
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Lotes</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $this->kpis()['batches'] }}</div>
        </div>
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Unidades</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ number_format($this->kpis()['total_quantity']) }}</div>
        </div>
        <div class="rounded border border-gray-200 dark:border-gray-700 p-3 bg-transparent dark:bg-transparent">
            <div class="text-xs text-gray-600 dark:text-gray-300">Valor Total</div>
            <div class="text-xl font-semibold text-gray-900 dark:text-gray-50">{{ $quote->currencyDto()->format($this->kpis()['total_value']) }}</div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="mb-6">
        <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
                <label class="text-xs text-gray-600 dark:text-gray-300">Supplier</label>
                <select wire:model.live="filterSupplier"
                        class="text-xs rounded border border-gray-300 dark:border-gray-600 bg-transparent dark:bg-transparent text-gray-800 dark:text-gray-100 px-2 py-1">
                    <option value="">Todos</option>
                    @foreach ($this->suppliers() as $supplier)
                        <option value="{{ $supplier->code }}">{{ $supplier->name }}</option>
                    @endforeach
                </select>
            </div>
            <div class="flex items-center gap-2">
                <label class="text-xs text-gray-600 dark:text-gray-300">Estado</label>
                <select wire:model.live="filterStatus"
                        class="text-xs rounded border border-gray-300 dark:border-gray-600 bg-transparent dark:bg-transparent text-gray-800 dark:text-gray-100 px-2 py-1">
                    <option value="">Todos</option>
                    @foreach ($this->statuses() as $status)
                        <option value="{{ $status }}">{{ ucfirst($status) }}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>

    @if (!$showBatchesOnly)
    <!-- Órdenes de Compra -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-2">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Órdenes de Compra</h2>
        </div>
        <div class="overflow-x-auto rounded border border-gray-200 dark:border-gray-700 shadow-sm">
            <table class="min-w-full text-sm bg-transparent dark:bg-transparent">
                <thead class="sticky top-0 z-10 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-100">
                    <tr>
                        <th class="text-left px-3 py-2">PO ID</th>
                        <th class="text-left px-3 py-2">Supplier</th>
                        <th class="text-left px-3 py-2">Estado</th>
                        <th class="text-left px-3 py-2">Incoterm</th>
                        <th class="text-right px-3 py-2">Items</th>
                        <th class="text-right px-3 py-2">Unidades</th>
                        <th class="text-right px-3 py-2">Valor Total</th>
                        <th class="text-right px-3 py-2">Lotes</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-100 dark:divide-gray-700 text-gray-900 dark:text-gray-100 bg-transparent dark:bg-transparent">
                    @forelse ($this->purchaseOrderRows() as $row)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-3 py-2 font-mono text-xs">#{{ $row['po_id'] }}</td>
                            <td class="px-3 py-2">
                                <div class="text-sm font-medium">{{ $row['supplier_name'] }}</div>
                                <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ $row['supplier_code'] }}</div>
                            </td>
                            <td class="px-3 py-2">
                                <span class="inline-flex items-center rounded px-2 py-0.5 text-xs font-medium {{ $this->getStatusColor($row['status']) }}">
                                    {{ ucfirst($row['status']) }}
                                </span>
                            </td>
                            <td class="px-3 py-2">
                                <span class="inline-flex items-center rounded px-2 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-xs">
                                    {{ $row['incoterm'] }}
                                </span>
                            </td>
                            <td class="px-3 py-2 text-right">{{ $row['items_count'] }}</td>
                            <td class="px-3 py-2 text-right">{{ number_format($row['total_quantity']) }}</td>
                            <td class="px-3 py-2 text-right font-medium">{{ $quote->currencyDto()->format($row['total_value']) }}</td>
                            <td class="px-3 py-2 text-right">{{ $row['batches_count'] }}</td>
                        </tr>
                        @if ($showDetails)
                        <tr class="bg-gray-50 dark:bg-gray-800/50">
                            <td colspan="8" class="px-3 py-2">
                                <div class="text-xs text-gray-600 dark:text-gray-300">
                                    @if ($row['terms'])
                                        <div class="mb-1">
                                            <span class="font-semibold">Términos:</span>
                                            @foreach ($row['terms'] as $term)
                                                <span class="inline-flex items-center rounded bg-blue-50 dark:bg-blue-900/40 text-blue-700 dark:text-blue-200 px-2 py-0.5 text-xs ml-1">{{ $term }}</span>
                                            @endforeach
                                        </div>
                                    @endif
                                    @if ($row['notes'])
                                        <div>
                                            <span class="font-semibold">Notas:</span> {{ $row['notes'] }}
                                        </div>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endif
                    @empty
                        <tr>
                            <td colspan="8" class="px-3 py-4 text-center text-sm text-gray-600 dark:text-gray-300">No hay órdenes de compra para mostrar con los filtros actuales.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Items de Órdenes de Compra -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-2">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Items de Órdenes de Compra</h2>
        </div>
        <div class="overflow-x-auto rounded border border-gray-200 dark:border-gray-700 shadow-sm">
            <table class="min-w-full text-sm bg-transparent dark:bg-transparent">
                <thead class="sticky top-0 z-10 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-100">
                    <tr>
                        <th class="text-left px-3 py-2">PO ID</th>
                        <th class="text-left px-3 py-2">Supplier</th>
                        <th class="text-left px-3 py-2">Producto</th>
                        <th class="text-left px-3 py-2">Variante</th>
                        <th class="text-right px-3 py-2">Cantidad</th>
                        <th class="text-right px-3 py-2">Precio Unit.</th>
                        <th class="text-right px-3 py-2">Valor Total</th>
                        <th class="text-right px-3 py-2">Lead Time</th>
                        <th class="text-right px-3 py-2">Lotes</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-100 dark:divide-gray-700 text-gray-900 dark:text-gray-100 bg-transparent dark:bg-transparent">
                    @forelse ($this->itemRows() as $row)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-3 py-2 font-mono text-xs">#{{ $row['po_id'] }}</td>
                            <td class="px-3 py-2">
                                <div class="text-sm font-medium">{{ $row['supplier_name'] }}</div>
                                <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ $row['supplier_code'] }}</div>
                            </td>
                            <td class="px-3 py-2">
                                <div class="text-sm font-medium">{{ $row['product_name'] }}</div>
                            </td>
                            <td class="px-3 py-2">
                                <div class="text-sm">{{ $row['variant_label'] }}</div>
                            </td>
                            <td class="px-3 py-2 text-right">{{ number_format($row['quantity']) }}</td>
                            <td class="px-3 py-2 text-right">{{ $quote->currencyDto()->format($row['unit_price']) }}</td>
                            <td class="px-3 py-2 text-right font-medium">{{ $quote->currencyDto()->format($row['total_value']) }}</td>
                            <td class="px-3 py-2 text-right">
                                <span class="inline-flex items-center rounded px-2 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-xs">
                                    {{ $row['lead_time_days'] }} días
                                </span>
                            </td>
                            <td class="px-3 py-2 text-right">
                                <div class="text-sm">{{ $row['batches_count'] }}</div>
                                <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ number_format($row['batches_quantity']) }} unidades</div>
                            </td>
                        </tr>
                        @if ($showDetails && $row['notes'])
                        <tr class="bg-gray-50 dark:bg-gray-800/50">
                            <td colspan="9" class="px-3 py-2">
                                <div class="text-xs text-gray-600 dark:text-gray-300">
                                    <span class="font-semibold">Notas:</span> {{ $row['notes'] }}
                                </div>
                            </td>
                        </tr>
                        @endif
                    @empty
                        <tr>
                            <td colspan="9" class="px-3 py-4 text-center text-sm text-gray-600 dark:text-gray-300">No hay items para mostrar con los filtros actuales.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    @endif

    <!-- Lotes de Producción -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-2">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Lotes de Producción</h2>
        </div>
        <div class="overflow-x-auto rounded border border-gray-200 dark:border-gray-700 shadow-sm">
            <table class="min-w-full text-sm bg-transparent dark:bg-transparent">
                <thead class="sticky top-0 z-10 bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-100">
                    <tr>
                        <th class="text-left px-3 py-2">Lote ID</th>
                        <th class="text-left px-3 py-2">Supplier</th>
                        <th class="text-left px-3 py-2">Producto</th>
                        <th class="text-left px-3 py-2">Variante</th>
                        <th class="text-right px-3 py-2">Cantidad</th>
                        <th class="text-left px-3 py-2">Estado</th>
                        <th class="text-left px-3 py-2">Pool State</th>
                        <th class="text-left px-3 py-2">Inicio Planificado</th>
                        <th class="text-left px-3 py-2">Fin Planificado</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-100 dark:divide-gray-700 text-gray-900 dark:text-gray-100 bg-transparent dark:bg-transparent">
                    @forelse ($this->batchRows() as $row)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-3 py-2 font-mono text-xs">#{{ $row['batch_id'] }}</td>
                            <td class="px-3 py-2">
                                <div class="text-sm font-medium">{{ $row['supplier_name'] }}</div>
                                <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ $row['supplier_code'] }}</div>
                            </td>
                            <td class="px-3 py-2">
                                <div class="text-sm font-medium">{{ $row['product_name'] }}</div>
                            </td>
                            <td class="px-3 py-2">
                                <div class="text-sm">{{ $row['variant_label'] }}</div>
                            </td>
                            <td class="px-3 py-2 text-right">{{ number_format($row['quantity']) }}</td>
                            <td class="px-3 py-2">
                                <span class="inline-flex items-center rounded px-2 py-0.5 text-xs font-medium {{ $this->getStatusColor($row['status']) }}">
                                    {{ ucfirst($row['status']) }}
                                </span>
                            </td>
                            <td class="px-3 py-2">
                                <span class="inline-flex items-center rounded px-2 py-0.5 text-xs font-medium {{ $this->getPoolStateColor($row['pool_state']) }}">
                                    {{ ucfirst($row['pool_state']) }}
                                </span>
                            </td>
                            <td class="px-3 py-2">
                                @if ($row['planned_start'])
                                    <div class="text-sm">{{ $row['planned_start']->format('d/m/Y') }}</div>
                                    <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ $row['planned_start']->format('H:i') }}</div>
                                @else
                                    <span class="text-gray-400">—</span>
                                @endif
                            </td>
                            <td class="px-3 py-2">
                                @if ($row['planned_finish'])
                                    <div class="text-sm">{{ $row['planned_finish']->format('d/m/Y') }}</div>
                                    <div class="text-[11px] text-gray-500 dark:text-gray-400">{{ $row['planned_finish']->format('H:i') }}</div>
                                @else
                                    <span class="text-gray-400">—</span>
                                @endif
                            </td>
                        </tr>
                        @if ($showDetails && $row['notes'])
                        <tr class="bg-gray-50 dark:bg-gray-800/50">
                            <td colspan="9" class="px-3 py-2">
                                <div class="text-xs text-gray-600 dark:text-gray-300">
                                    <span class="font-semibold">Notas:</span> {{ $row['notes'] }}
                                </div>
                            </td>
                        </tr>
                        @endif
                    @empty
                        <tr>
                            <td colspan="9" class="px-3 py-4 text-center text-sm text-gray-600 dark:text-gray-300">No hay lotes de producción para mostrar con los filtros actuales.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Resumen de Suppliers -->
    <div class="mt-8">
        <h2 class="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">Resumen por Supplier</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach ($this->suppliers() as $supplier)
                @php
                    $supplierPOs = $this->quote->purchaseOrders->where('supplier_id', $supplier->id);
                    $supplierItems = $supplierPOs->flatMap(fn($po) => $po->items);
                    $supplierBatches = $supplierItems->flatMap(fn($item) => $item->productionBatches);
                    $supplierValue = $supplierItems->sum(fn($item) => $item->quantity * $item->unit_price);
                @endphp
                <div class="rounded border border-gray-200 dark:border-gray-700 p-4 bg-transparent dark:bg-transparent">
                    <div class="flex items-start justify-between mb-2">
                        <div>
                            <h3 class="font-medium text-gray-900 dark:text-gray-100">{{ $supplier->name }}</h3>
                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ $supplier->code }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $quote->currencyDto()->format($supplierValue) }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Valor total</div>
                        </div>
                    </div>
                    <div class="grid grid-cols-3 gap-2 text-xs">
                        <div class="text-center">
                            <div class="font-medium text-gray-900 dark:text-gray-100">{{ $supplierPOs->count() }}</div>
                            <div class="text-gray-500 dark:text-gray-400">Órdenes</div>
                        </div>
                        <div class="text-center">
                            <div class="font-medium text-gray-900 dark:text-gray-100">{{ $supplierItems->count() }}</div>
                            <div class="text-gray-500 dark:text-gray-400">Items</div>
                        </div>
                        <div class="text-center">
                            <div class="font-medium text-gray-900 dark:text-gray-100">{{ $supplierBatches->count() }}</div>
                            <div class="text-gray-500 dark:text-gray-400">Lotes</div>
                        </div>
                    </div>
                    @if ($supplier->contact && isset($supplier->contact['contact_person']))
                        <div class="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                            <div class="text-xs text-gray-600 dark:text-gray-300">
                                <span class="font-semibold">Contacto:</span> {{ $supplier->contact['contact_person'] }}
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</section>
