<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Formulario de selección -->
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Seleccionar Cotización</h3>
            {{ $this->form }}
        </div>

        <!-- Información de la cotización -->
        @if($selectedQuote)
            <!-- Información básica -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Información de la Cotización</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">ID Interno</label>
                            <p class="text-lg font-mono font-bold text-gray-900 dark:text-white">#{{ $selectedQuote->id }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Cliente</label>
                            <p class="text-lg font-bold text-gray-900 dark:text-white">{{ $selectedQuote->customer_name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Número de Cotización</label>
                            <p class="text-lg font-bold text-gray-900 dark:text-white">{{ $selectedQuote->quote_number }}</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Moneda</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                {{ $selectedQuote->currency }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Estado</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                {{ $selectedQuote->status }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Válida hasta</label>
                            <p class="text-gray-900 dark:text-white">{{ $selectedQuote->valid_until?->format('d/m/Y') }}</p>
                        </div>
                    </div>
                </div>
                @if($selectedQuote->notes)
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Notas Adicionales</label>
                        <p class="text-gray-900 dark:text-white">{{ $selectedQuote->notes }}</p>
                    </div>
                @endif
            </div>

            <!-- Resumen Financiero -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Resumen Financiero</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Subtotal</label>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ number_format($this->getSubtotal(), 2) }}</p>
                    </div>
                    <div class="text-center">
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Impuestos</label>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">${{ number_format($this->getTaxAmount(), 2) }}</p>
                    </div>
                    <div class="text-center">
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Total</label>
                        <p class="text-2xl font-bold text-green-600 dark:text-green-400">${{ number_format($this->getTotal(), 2) }}</p>
                    </div>
                </div>
            </div>

            <!-- Detalles de Productos -->
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Detalles de Productos</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Grupos</label>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getGroupsCount() }}</p>
                    </div>
                    <div class="text-center">
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Productos</label>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getProductsCount() }}</p>
                    </div>
                    <div class="text-center">
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400">Variantes</label>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $this->getVariantsCount() }}</p>
                    </div>
                </div>
            </div>
        @else
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
                <div class="text-center text-gray-500 dark:text-gray-400">
                    <p>Selecciona una cotización para ver su información</p>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>