<div class="space-y-4">
    <!-- Barr<PERSON> de b<PERSON>queda -->
    <div class="fi-fo-field-wrp">
        <div class="fi-fo-field-wrp-field">
            <div class="fi-input-wrp">
                <input type="text" 
                       placeholder="Buscar productos..."
                       class="fi-input w-full rounded-lg border-0 bg-white px-3 py-2 text-gray-950 shadow-sm ring-1 ring-inset ring-gray-950/10 transition duration-75 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-gray-950 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-950/10 dark:bg-white/5 dark:text-white dark:ring-white/20 dark:placeholder:text-gray-500 dark:focus:ring-primary-500 dark:disabled:bg-transparent dark:disabled:text-gray-400 dark:disabled:ring-white/10">
            </div>
        </div>
    </div>

    <!-- Grid de productos -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
        @foreach($this->getProductTypes() as $product)
            <div class="fi-card rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 cursor-pointer transition-all duration-200
                        {{ $this->selectedProducts && in_array($product->id, $this->selectedProducts) ? 'ring-2 ring-primary-500 bg-primary-50 dark:bg-primary-950/20' : 'hover:shadow-md' }}"
                 wire:click="selectProduct({{ $product->id }})">
                
                <div class="fi-card-content p-4">
                    <div class="fi-card-header flex items-start justify-between mb-3">
                        <input type="checkbox" 
                               {{ $this->selectedProducts && in_array($product->id, $this->selectedProducts) ? 'checked' : '' }}
                               class="fi-checkbox-input rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        <div class="fi-icon fi-color-gray fi-size-sm">
                            <svg class="fi-icon-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="fi-card-body">
                        <h4 class="fi-card-title text-sm font-semibold text-gray-950 dark:text-white mb-2">{{ $product->name }}</h4>
                        <p class="fi-card-text text-xs text-gray-500 dark:text-gray-400 mb-2">Código: {{ $product->code }}</p>
                        <div class="fi-badge fi-color-primary fi-size-xs">
                            <span class="fi-badge-label">{{ $product->subcategory->category->name ?? 'Sin categoría' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    
    @if($this->selectedProducts && count($this->selectedProducts) > 0)
        <div class="fi-callout fi-color-success fi-size-sm">
            <div class="fi-callout-content">
                <div class="fi-callout-header">
                    <h4 class="fi-callout-title">Productos seleccionados: {{ count($this->selectedProducts) }}</h4>
                </div>
            </div>
        </div>
    @endif
</div>
