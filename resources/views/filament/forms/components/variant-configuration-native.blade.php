<div class="space-y-4">
    @if($this->selectedProducts && count($this->selectedProducts) > 0)
        <div class="fi-callout fi-color-primary fi-size-sm">
            <div class="fi-callout-content">
                <div class="fi-callout-header">
                    <h4 class="fi-callout-title">Configuración de Variantes</h4>
                </div>
                <div class="fi-callout-body">
                    <p class="fi-callout-text">Crea y configura las variantes para cada producto seleccionado. Puedes agregar múltiples variantes por producto.</p>
                </div>
            </div>
        </div>
        
        @foreach($this->selectedProducts as $productId)
            @php $product = $this->getProductById($productId); @endphp
            @if($product)
                <div class="fi-section rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                    <div class="fi-section-content p-4">
                        <div class="fi-section-header flex items-center justify-between mb-4">
                            <h5 class="fi-section-header-heading text-lg font-semibold text-gray-950 dark:text-white">{{ $product->name }}</h5>
                            <button type="button" 
                                    wire:click="addVariant({{ $productId }})"
                                    class="fi-btn fi-btn-color-primary fi-btn-size-sm">
                                <svg class="fi-icon fi-color-white fi-size-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                Agregar Variante
                            </button>
                        </div>
                        
                        <!-- Lista de variantes existentes -->
                        <div class="space-y-3" id="variants-{{ $productId }}">
                            @if(isset($this->productVariants[$productId]) && count($this->productVariants[$productId]) > 0)
                                @foreach($this->productVariants[$productId] as $variantIndex => $variant)
                                    <div class="fi-card rounded-lg bg-gray-50 dark:bg-gray-800 p-4" wire:key="variant-{{ $productId }}-{{ $variantIndex }}">
                                        <div class="fi-fo-fieldset">
                                            <div class="fi-fo-fieldset-content">
                                                <div class="fi-fo-fieldset-content-grid grid grid-cols-1 md:grid-cols-4 gap-4">
                                                    <div class="fi-fo-field-wrp">
                                                        <div class="fi-fo-field-wrp-label">
                                                            <label class="fi-fo-field-wrp-label-label text-sm font-medium leading-6 text-gray-950 dark:text-white">
                                                                Variante
                                                            </label>
                                                        </div>
                                                        <div class="fi-fo-field-wrp-field">
                                                            <input type="text" 
                                                                   wire:model="productVariants.{{ $productId }}.{{ $variantIndex }}.name"
                                                                   placeholder="Nombre de la variante" 
                                                                   class="fi-input w-full rounded-lg border-0 bg-white px-3 py-2 text-gray-950 shadow-sm ring-1 ring-inset ring-gray-950/10 transition duration-75 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-gray-950 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-950/10 dark:bg-white/5 dark:text-white dark:ring-white/20 dark:placeholder:text-gray-500 dark:focus:ring-primary-500 dark:disabled:bg-transparent dark:disabled:text-gray-400 dark:disabled:ring-white/10">
                                                        </div>
                                                    </div>
                                                    <div class="fi-fo-field-wrp">
                                                        <div class="fi-fo-field-wrp-label">
                                                            <label class="fi-fo-field-wrp-label-label text-sm font-medium leading-6 text-gray-950 dark:text-white">
                                                                Cantidad
                                                            </label>
                                                        </div>
                                                        <div class="fi-fo-field-wrp-field">
                                                            <input type="number" 
                                                                   wire:model="productVariants.{{ $productId }}.{{ $variantIndex }}.quantity"
                                                                   min="1" 
                                                                   placeholder="1" 
                                                                   class="fi-input w-full rounded-lg border-0 bg-white px-3 py-2 text-gray-950 shadow-sm ring-1 ring-inset ring-gray-950/10 transition duration-75 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-gray-950 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-950/10 dark:bg-white/5 dark:text-white dark:ring-white/20 dark:placeholder:text-gray-500 dark:focus:ring-primary-500 dark:disabled:bg-transparent dark:disabled:text-gray-400 dark:disabled:ring-white/10">
                                                        </div>
                                                    </div>
                                                    <div class="fi-fo-field-wrp">
                                                        <div class="fi-fo-field-wrp-label">
                                                            <label class="fi-fo-field-wrp-label-label text-sm font-medium leading-6 text-gray-950 dark:text-white">
                                                                Especificaciones
                                                            </label>
                                                        </div>
                                                        <div class="fi-fo-field-wrp-field">
                                                            <input type="text" 
                                                                   wire:model="productVariants.{{ $productId }}.{{ $variantIndex }}.specifications"
                                                                   placeholder="Color, tamaño, etc." 
                                                                   class="fi-input w-full rounded-lg border-0 bg-white px-3 py-2 text-gray-950 shadow-sm ring-1 ring-inset ring-gray-950/10 transition duration-75 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-gray-950 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-950/10 dark:bg-white/5 dark:text-white dark:ring-white/20 dark:placeholder:text-gray-500 dark:focus:ring-primary-500 dark:disabled:bg-transparent dark:disabled:text-gray-400 dark:disabled:ring-white/10">
                                                        </div>
                                                    </div>
                                                    <div class="fi-fo-field-wrp">
                                                        <div class="fi-fo-field-wrp-label">
                                                            <label class="fi-fo-field-wrp-label-label text-sm font-medium leading-6 text-gray-950 dark:text-white">
                                                                Acciones
                                                            </label>
                                                        </div>
                                                        <div class="fi-fo-field-wrp-field">
                                                            <button type="button" 
                                                                    wire:click="removeVariant({{ $productId }}, {{ $variantIndex }})"
                                                                    class="fi-btn fi-btn-color-danger fi-btn-size-sm">
                                                                <svg class="fi-icon fi-color-white fi-size-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                                </svg>
                                                                Eliminar
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="fi-fo-field-wrp mt-3">
                                                    <div class="fi-fo-field-wrp-label">
                                                        <label class="fi-fo-field-wrp-label-label text-sm font-medium leading-6 text-gray-950 dark:text-white">
                                                            Notas
                                                        </label>
                                                    </div>
                                                    <div class="fi-fo-field-wrp-field">
                                                        <textarea wire:model="productVariants.{{ $productId }}.{{ $variantIndex }}.notes"
                                                                  rows="2" 
                                                                  placeholder="Notas adicionales para esta variante..." 
                                                                  class="fi-input w-full rounded-lg border-0 bg-white px-3 py-2 text-gray-950 shadow-sm ring-1 ring-inset ring-gray-950/10 transition duration-75 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-gray-950 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-950/10 dark:bg-white/5 dark:text-white dark:ring-white/20 dark:placeholder:text-gray-500 dark:focus:ring-primary-500 dark:disabled:bg-transparent dark:disabled:text-gray-400 dark:disabled:ring-white/10"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="fi-callout fi-color-gray fi-size-sm">
                                    <div class="fi-callout-content">
                                        <div class="fi-callout-body">
                                            <p class="fi-callout-text">No hay variantes creadas para este producto. Haz clic en "Agregar Variante" para comenzar.</p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
    @else
        <div class="fi-empty-state">
            <div class="fi-empty-state-content">
                <div class="fi-empty-state-icon mx-auto mb-4">
                    <svg class="fi-icon fi-color-gray fi-size-xl" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="fi-empty-state-text">
                    <h3 class="fi-empty-state-heading text-lg font-medium text-gray-900 dark:text-white mb-2">No hay productos seleccionados</h3>
                    <p class="fi-empty-state-description text-gray-500 dark:text-gray-400">Por favor, regresa al paso anterior y selecciona al menos un producto.</p>
                </div>
            </div>
        </div>
    @endif
</div>
