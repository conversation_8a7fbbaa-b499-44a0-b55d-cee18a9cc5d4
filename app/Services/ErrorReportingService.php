<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Centralized error reporting and monitoring service
 */
class ErrorReportingService
{
    /**
     * Report critical business errors that need immediate attention
     */
    public function reportCriticalError(string $message, array $context = [], ?Throwable $exception = null): void
    {
        $logContext = array_merge($context, [
            'severity' => 'critical',
            'environment' => app()->environment(),
            'timestamp' => now()->toISOString(),
        ]);

        if ($exception) {
            $logContext['exception'] = $exception;
        }

        Log::channel('slack')->critical($message, $logContext);
        Log::error($message, $logContext);
    }

    /**
     * Report business logic violations
     */
    public function reportBusinessError(string $message, array $context = []): void
    {
        $logContext = array_merge($context, [
            'severity' => 'business_error',
            'environment' => app()->environment(),
            'timestamp' => now()->toISOString(),
        ]);

        Log::warning($message, $logContext);
    }

    /**
     * Report performance issues
     */
    public function reportPerformanceIssue(string $operation, float $duration, array $context = []): void
    {
        $logContext = array_merge($context, [
            'severity' => 'performance',
            'operation' => $operation,
            'duration_ms' => $duration,
            'timestamp' => now()->toISOString(),
        ]);

        Log::info("Performance issue detected: {$operation} took {$duration}ms", $logContext);
    }

    /**
     * Report data integrity issues
     */
    public function reportDataIntegrityIssue(string $message, array $context = []): void
    {
        $logContext = array_merge($context, [
            'severity' => 'data_integrity',
            'environment' => app()->environment(),
            'timestamp' => now()->toISOString(),
        ]);

        Log::error($message, $logContext);
    }

    /**
     * Report user action for audit trail
     */
    public function reportUserAction(string $action, ?int $userId, array $context = []): void
    {
        $logContext = array_merge($context, [
            'action' => $action,
            'user_id' => $userId,
            'ip' => request()?->ip(),
            'user_agent' => request()?->userAgent(),
            'timestamp' => now()->toISOString(),
        ]);

        Log::channel('audit')->info("User action: {$action}", $logContext);
    }
}
