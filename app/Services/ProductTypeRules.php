<?php

namespace App\Services;

use App\Models\ProductType;
use Illuminate\Validation\Rule;

class ProductTypeRules
{
    public static function productAttributeRules(ProductType $type): array
    {
        $reglas = (array) ($type->reglas['producto'] ?? []);
        return self::toLaravelRules($reglas);
    }

    public static function variantAttributeRules(ProductType $type): array
    {
        $reglas = (array) ($type->reglas['variante'] ?? []);
        return self::toLaravelRules($reglas);
    }

    /**
     * Translate a simple rules definition array into Laravel validation rules.
     * Supported: 'required' (bool|string), 'in' (array), 'equals' (scalar).
     * Example input: ['capacidad' => 'required', 'interfaz' => ['in' => ['USB-A','USB-C']]]
     */
    private static function toLaravelRules(array $defs): array
    {
        $out = [];
        foreach ($defs as $attr => $ruleDef) {
            $rules = [];
            if (is_string($ruleDef)) {
                if ($ruleDef === 'required') {
                    $rules[] = 'required';
                } else {
                    // Pass-through custom string
                    $rules[] = $ruleDef;
                }
            } elseif (is_array($ruleDef)) {
                if (array_key_exists('required', $ruleDef)) {
                    if ($ruleDef['required'] === true || $ruleDef['required'] === 'required') {
                        $rules[] = 'required';
                    }
                }
                if (array_key_exists('in', $ruleDef) && is_array($ruleDef['in'])) {
                    $rules[] = 'required';
                    $rules[] = Rule::in(array_values($ruleDef['in']));
                }
                if (array_key_exists('equals', $ruleDef)) {
                    $val = $ruleDef['equals'];
                    $rules[] = 'required';
                    $rules[] = Rule::in([$val]);
                }
            }

            if (!empty($rules)) {
                $out[$attr] = $rules;
            }
        }
        return $out;
    }
}
