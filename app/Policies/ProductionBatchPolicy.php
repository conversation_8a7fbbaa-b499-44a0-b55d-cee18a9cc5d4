<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Foundation\Auth\User as AuthUser;
use App\Models\ProductionBatch;
use Illuminate\Auth\Access\HandlesAuthorization;

class ProductionBatchPolicy
{
    use HandlesAuthorization;
    
    public function viewAny(AuthUser $authUser): bool
    {
        return $authUser->can('ViewAny:ProductionBatch');
    }

    public function view(AuthUser $authUser, ProductionBatch $productionBatch): bool
    {
        return $authUser->can('View:ProductionBatch');
    }

    public function create(AuthUser $authUser): bool
    {
        return $authUser->can('Create:ProductionBatch');
    }

    public function update(AuthUser $authUser, ProductionBatch $productionBatch): bool
    {
        return $authUser->can('Update:ProductionBatch');
    }

    public function delete(AuthUser $authUser, ProductionBatch $productionBatch): bool
    {
        return $authUser->can('Delete:ProductionBatch');
    }

    public function restore(AuthUser $authUser, ProductionBatch $productionBatch): bool
    {
        return $authUser->can('Restore:ProductionBatch');
    }

    public function forceDelete(AuthUser $authUser, ProductionBatch $productionBatch): bool
    {
        return $authUser->can('ForceDelete:ProductionBatch');
    }

    public function forceDeleteAny(AuthUser $authUser): bool
    {
        return $authUser->can('ForceDeleteAny:ProductionBatch');
    }

    public function restoreAny(AuthUser $authUser): bool
    {
        return $authUser->can('RestoreAny:ProductionBatch');
    }

    public function replicate(AuthUser $authUser, ProductionBatch $productionBatch): bool
    {
        return $authUser->can('Replicate:ProductionBatch');
    }

    public function reorder(AuthUser $authUser): bool
    {
        return $authUser->can('Reorder:ProductionBatch');
    }

}