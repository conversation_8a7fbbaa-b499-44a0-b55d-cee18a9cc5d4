<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Foundation\Auth\User as AuthUser;
use App\Models\CustomerQuoteGroup;
use Illuminate\Auth\Access\HandlesAuthorization;

class CustomerQuoteGroupPolicy
{
    use HandlesAuthorization;
    
    public function viewAny(AuthUser $authUser): bool
    {
        return $authUser->can('ViewAny:CustomerQuoteGroup');
    }

    public function view(AuthUser $authUser, CustomerQuoteGroup $customerQuoteGroup): bool
    {
        return $authUser->can('View:CustomerQuoteGroup');
    }

    public function create(AuthUser $authUser): bool
    {
        return $authUser->can('Create:CustomerQuoteGroup');
    }

    public function update(AuthUser $authUser, CustomerQuoteGroup $customerQuoteGroup): bool
    {
        return $authUser->can('Update:CustomerQuoteGroup');
    }

    public function delete(AuthUser $authUser, CustomerQuoteGroup $customerQuoteGroup): bool
    {
        return $authUser->can('Delete:CustomerQuoteGroup');
    }

    public function restore(AuthUser $authUser, CustomerQuoteGroup $customerQuoteGroup): bool
    {
        return $authUser->can('Restore:CustomerQuoteGroup');
    }

    public function forceDelete(AuthUser $authUser, CustomerQuoteGroup $customerQuoteGroup): bool
    {
        return $authUser->can('ForceDelete:CustomerQuoteGroup');
    }

    public function forceDeleteAny(AuthUser $authUser): bool
    {
        return $authUser->can('ForceDeleteAny:CustomerQuoteGroup');
    }

    public function restoreAny(AuthUser $authUser): bool
    {
        return $authUser->can('RestoreAny:CustomerQuoteGroup');
    }

    public function replicate(AuthUser $authUser, CustomerQuoteGroup $customerQuoteGroup): bool
    {
        return $authUser->can('Replicate:CustomerQuoteGroup');
    }

    public function reorder(AuthUser $authUser): bool
    {
        return $authUser->can('Reorder:CustomerQuoteGroup');
    }

}