<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Foundation\Auth\User as AuthUser;
use App\Models\CustomerQuoteProductVariant;
use Illuminate\Auth\Access\HandlesAuthorization;

class CustomerQuoteProductVariantPolicy
{
    use HandlesAuthorization;
    
    public function viewAny(AuthUser $authUser): bool
    {
        return $authUser->can('ViewAny:CustomerQuoteProductVariant');
    }

    public function view(AuthUser $authUser, CustomerQuoteProductVariant $customerQuoteProductVariant): bool
    {
        return $authUser->can('View:CustomerQuoteProductVariant');
    }

    public function create(AuthUser $authUser): bool
    {
        return $authUser->can('Create:CustomerQuoteProductVariant');
    }

    public function update(AuthUser $authUser, CustomerQuoteProductVariant $customerQuoteProductVariant): bool
    {
        return $authUser->can('Update:CustomerQuoteProductVariant');
    }

    public function delete(AuthUser $authUser, CustomerQuoteProductVariant $customerQuoteProductVariant): bool
    {
        return $authUser->can('Delete:CustomerQuoteProductVariant');
    }

    public function restore(AuthUser $authUser, CustomerQuoteProductVariant $customerQuoteProductVariant): bool
    {
        return $authUser->can('Restore:CustomerQuoteProductVariant');
    }

    public function forceDelete(AuthUser $authUser, CustomerQuoteProductVariant $customerQuoteProductVariant): bool
    {
        return $authUser->can('ForceDelete:CustomerQuoteProductVariant');
    }

    public function forceDeleteAny(AuthUser $authUser): bool
    {
        return $authUser->can('ForceDeleteAny:CustomerQuoteProductVariant');
    }

    public function restoreAny(AuthUser $authUser): bool
    {
        return $authUser->can('RestoreAny:CustomerQuoteProductVariant');
    }

    public function replicate(AuthUser $authUser, CustomerQuoteProductVariant $customerQuoteProductVariant): bool
    {
        return $authUser->can('Replicate:CustomerQuoteProductVariant');
    }

    public function reorder(AuthUser $authUser): bool
    {
        return $authUser->can('Reorder:CustomerQuoteProductVariant');
    }

}