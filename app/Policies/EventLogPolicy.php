<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Foundation\Auth\User as AuthUser;
use App\Models\EventLog;
use Illuminate\Auth\Access\HandlesAuthorization;

class EventLogPolicy
{
    use HandlesAuthorization;
    
    public function viewAny(AuthUser $authUser): bool
    {
        return $authUser->can('ViewAny:EventLog');
    }

    public function view(AuthUser $authUser, EventLog $eventLog): bool
    {
        return $authUser->can('View:EventLog');
    }

    public function create(AuthUser $authUser): bool
    {
        return $authUser->can('Create:EventLog');
    }

    public function update(AuthUser $authUser, EventLog $eventLog): bool
    {
        return $authUser->can('Update:EventLog');
    }

    public function delete(AuthUser $authUser, EventLog $eventLog): bool
    {
        return $authUser->can('Delete:EventLog');
    }

    public function restore(AuthUser $authUser, EventLog $eventLog): bool
    {
        return $authUser->can('Restore:EventLog');
    }

    public function forceDelete(AuthUser $authUser, EventLog $eventLog): bool
    {
        return $authUser->can('ForceDelete:EventLog');
    }

    public function forceDeleteAny(AuthUser $authUser): bool
    {
        return $authUser->can('ForceDeleteAny:EventLog');
    }

    public function restoreAny(AuthUser $authUser): bool
    {
        return $authUser->can('RestoreAny:EventLog');
    }

    public function replicate(AuthUser $authUser, EventLog $eventLog): bool
    {
        return $authUser->can('Replicate:EventLog');
    }

    public function reorder(AuthUser $authUser): bool
    {
        return $authUser->can('Reorder:EventLog');
    }

}