<?php

declare(strict_types=1);

namespace App\Policies;

use Illuminate\Foundation\Auth\User as AuthUser;
use App\Models\CustomerQuoteProduct;
use Illuminate\Auth\Access\HandlesAuthorization;

class CustomerQuoteProductPolicy
{
    use HandlesAuthorization;
    
    public function viewAny(AuthUser $authUser): bool
    {
        return $authUser->can('ViewAny:CustomerQuoteProduct');
    }

    public function view(AuthUser $authUser, CustomerQuoteProduct $customerQuoteProduct): bool
    {
        return $authUser->can('View:CustomerQuoteProduct');
    }

    public function create(AuthUser $authUser): bool
    {
        return $authUser->can('Create:CustomerQuoteProduct');
    }

    public function update(AuthUser $authUser, CustomerQuoteProduct $customerQuoteProduct): bool
    {
        return $authUser->can('Update:CustomerQuoteProduct');
    }

    public function delete(AuthUser $authUser, CustomerQuoteProduct $customerQuoteProduct): bool
    {
        return $authUser->can('Delete:CustomerQuoteProduct');
    }

    public function restore(AuthUser $authUser, CustomerQuoteProduct $customerQuoteProduct): bool
    {
        return $authUser->can('Restore:CustomerQuoteProduct');
    }

    public function forceDelete(AuthUser $authUser, CustomerQuoteProduct $customerQuoteProduct): bool
    {
        return $authUser->can('ForceDelete:CustomerQuoteProduct');
    }

    public function forceDeleteAny(AuthUser $authUser): bool
    {
        return $authUser->can('ForceDeleteAny:CustomerQuoteProduct');
    }

    public function restoreAny(AuthUser $authUser): bool
    {
        return $authUser->can('RestoreAny:CustomerQuoteProduct');
    }

    public function replicate(AuthUser $authUser, CustomerQuoteProduct $customerQuoteProduct): bool
    {
        return $authUser->can('Replicate:CustomerQuoteProduct');
    }

    public function reorder(AuthUser $authUser): bool
    {
        return $authUser->can('Reorder:CustomerQuoteProduct');
    }

}