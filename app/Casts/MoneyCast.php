<?php

namespace App\Casts;

use App\ValueObjects\Money;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class MoneyCast implements CastsAttributes
{
    public function get(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        $minor = (int) ($attributes['unit_price_minor'] ?? 0);
        $currency = (string) ($attributes['price_currency'] ?? '');
        
        // Si no hay precio o moneda, retornar null
        if ($minor === 0 || empty($currency)) {
            return null;
        }
        
        return new Money($minor, $currency);
    }

    public function set(Model $model, string $key, mixed $value, array $attributes): array
    {
        // Manejar valores null
        if (is_null($value)) {
            return [
                'unit_price_minor' => null,
                'price_currency' => null,
            ];
        }

        if ($value instanceof Money) {
            return [
                'unit_price_minor' => $value->amountMinor,
                'price_currency' => strtoupper($value->currency),
            ];
        }

        // If numeric provided, assume MINOR units already.
        if (is_int($value)) {
            return ['unit_price_minor' => $value];
        }

        if (is_array($value) && isset($value['amountMinor'], $value['currency'])) {
            return [
                'unit_price_minor' => (int) $value['amountMinor'],
                'price_currency' => strtoupper((string) $value['currency']),
            ];
        }

        throw new \InvalidArgumentException('Invalid value for Money cast. Provide Money, null, or [amountMinor, currency].');
    }
}

