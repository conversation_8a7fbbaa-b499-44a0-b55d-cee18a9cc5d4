<?php

namespace App\Http\Requests;

use App\Rules\ValidContactField;
use App\Rules\ValidContactValue;
use Illuminate\Foundation\Http\FormRequest;

class StoreSupplierRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'code' => ['required', 'string', 'max:50', 'unique:suppliers,code'],
            'name' => ['required', 'string', 'max:255'],
            'contact' => ['nullable', 'array'],
            'contact.*' => ['required', 'string', 'max:255'],
            'active' => ['boolean'],
            'notes' => ['nullable', 'string'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'contact.*.required' => 'El valor del campo de contacto es obligatorio.',
            'contact.*.string' => 'El valor del campo de contacto debe ser texto.',
            'contact.*.max' => 'El valor del campo de contacto no puede exceder 255 caracteres.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if ($this->has('contact') && is_array($this->contact)) {
                foreach ($this->contact as $key => $value) {
                    // Validar el nombre del campo
                    $keyValidator = validator(['key' => $key], [
                        'key' => ['required', 'string', new ValidContactField],
                    ]);

                    if ($keyValidator->fails()) {
                        $validator->errors()->add("contact.{$key}", 'Nombre de campo inválido: '.$keyValidator->errors()->first('key'));
                    }

                    // Validar el valor del campo
                    $valueValidator = validator(['value' => $value], [
                        'value' => ['required', 'string', new ValidContactValue],
                    ]);

                    if ($valueValidator->fails()) {
                        $validator->errors()->add("contact.{$key}", 'Valor inválido: '.$valueValidator->errors()->first('value'));
                    }
                }
            }
        });
    }
}
