<?php

namespace App\Http\Requests;

use App\Models\CustomerQuoteProduct;
use App\Models\ProductType;
use App\Services\ProductTypeRules;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCustomerQuoteProductRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $base = [
            'type_id' => ['sometimes', 'integer', 'exists:product_types,id'],
            'name' => ['sometimes', 'string', 'min:1'],
            'attributes' => ['sometimes', 'array'],
            'specs' => ['sometimes', 'array'],
            'hs_code' => ['sometimes', 'string'],
            'weight' => ['sometimes', 'numeric'],
            'volume' => ['sometimes', 'numeric'],
            'group_id' => ['nullable', 'integer', 'exists:customer_quote_groups,id'],
            'notes' => ['nullable', 'string'],
            'position' => ['nullable', 'integer', 'min:0'],
        ];

        // Dynamic attribute rules: use provided type_id or current product's type
        $typeId = $this->input('type_id');
        if (! $typeId && ($product = $this->route('product')) instanceof CustomerQuoteProduct) {
            $typeId = $product->type_id;
        }
        if ($typeId) {
            $type = ProductType::query()->find($typeId);
            if ($type) {
                foreach (ProductTypeRules::productAttributeRules($type) as $attr => $rules) {
                    // On update, mark rules as 'sometimes'
                    $base['attributes.'.$attr] = array_merge(['sometimes'], $rules);
                }
            }
        }

        return $base;
    }
}
