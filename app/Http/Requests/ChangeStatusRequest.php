<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ChangeStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $entity = $this->route('entity'); // po, batch, etc.

        $statusRules = match ($entity) {
            'po' => ['borrador', 'enviada', 'confirmada', 'cerrada'],
            'batch' => ['borrador', 'planificado', 'en_produccion', 'completado'],
            default => [],
        };

        return [
            'status' => ['required', 'string', Rule::in($statusRules)],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.required' => 'El estado es obligatorio.',
            'status.in' => 'El estado seleccionado no es válido para esta entidad.',
        ];
    }
}
