<?php

namespace App\Http\Requests;

use App\Models\CustomerQuoteProductVariant;
use App\Models\PurchaseOrder;
use Illuminate\Foundation\Http\FormRequest;

class StorePurchaseOrderItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'quote_product_variant_id' => [
                'required',
                'integer',
                'exists:customer_quote_product_variants,id',
                function ($attribute, $value, $fail) {
                    $purchaseOrder = PurchaseOrder::find($this->route('po'));
                    if (! $purchaseOrder) {
                        $fail('La orden de compra no existe.');

                        return;
                    }

                    $variant = CustomerQuoteProductVariant::find($value);
                    if (! $variant) {
                        $fail('La variante de producto no existe.');

                        return;
                    }

                    // Verificar que la variante pertenece a la cotización de la OC
                    if ($variant->quoteProduct->customer_quote_id !== $purchaseOrder->quote_id) {
                        $fail('La variante de producto no pertenece a la cotización de esta orden de compra.');
                    }
                },
            ],
            'quantity' => ['required', 'integer', 'min:1'],
            'unit_price' => ['nullable', 'numeric', 'min:0'],
            'lead_time_days' => ['nullable', 'integer', 'min:1'],
            'notes' => ['nullable', 'string'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'quote_product_variant_id.required' => 'La variante de producto es obligatoria.',
            'quote_product_variant_id.exists' => 'La variante de producto seleccionada no existe.',
            'quantity.required' => 'La cantidad es obligatoria.',
            'quantity.integer' => 'La cantidad debe ser un número entero.',
            'quantity.min' => 'La cantidad debe ser mayor a 0.',
            'unit_price.numeric' => 'El precio unitario debe ser un número válido.',
            'unit_price.min' => 'El precio unitario no puede ser negativo.',
            'lead_time_days.integer' => 'El tiempo de entrega debe ser un número entero.',
            'lead_time_days.min' => 'El tiempo de entrega debe ser mayor a 0.',
        ];
    }
}
