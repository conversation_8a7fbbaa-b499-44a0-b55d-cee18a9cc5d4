<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerQuoteRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // Aceptamos customer_name (nuevo) o client_name (legacy)
            'customer_name' => ['nullable', 'string', 'min:1'],
            'client_name' => ['nullable', 'string', 'min:1'],
            'currency' => ['required', 'string', 'size:3'],
            'country' => ['nullable', 'string', 'size:2'],
            'valid_until' => ['nullable', 'date', 'after:today'],
            'notes' => ['nullable', 'string'],
        ];
    }

    public function messages(): array
    {
        return [
            'customer_name.required' => 'El nombre del cliente es obligatorio.',
            'currency.required' => 'La moneda es obligatoria.',
            'currency.size' => 'La moneda debe tener 3 caracteres (ISO 4217).',
            'valid_until.after' => 'La fecha de validez debe ser posterior a hoy.',
        ];
    }
}
