<?php

namespace App\Http\Requests;

use App\Models\CustomerQuoteProduct;
use App\Models\ProductType;
use App\Services\ProductTypeRules;
use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerQuoteProductVariantRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        $priceCurrency = strtoupper((string) ($this->input('price_currency') ?: $this->input('currency') ?: ''));
        $unitPrice = $this->input('unit_price');
        if ($priceCurrency && $unitPrice !== null && $this->input('unit_price_minor') === null) {
            $minor = \App\Support\MoneyInput::parseToMinor($unitPrice, $priceCurrency);
            if ($minor !== null) {
                $this->merge(['unit_price_minor' => $minor, 'price_currency' => $priceCurrency]);
            }
        }
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $base = [
            'label' => ['required', 'string', 'min:1'],
            'attributes' => ['required', 'array'],
            'specs' => ['nullable', 'array'],
            'hs_code' => ['nullable', 'string'],
            'weight' => ['nullable', 'numeric'],
            'volume' => ['nullable', 'numeric'],
            'quantity' => ['required', 'integer', 'min:1'],
            'price_currency' => ['required', 'string', 'size:3'],
            'unit_price' => ['nullable', 'string', 'required_without:unit_price_minor'],
            'unit_price_minor' => ['nullable', 'integer', 'min:0', 'required_without:unit_price'],
            'fx_rate_to_quote' => ['nullable', 'numeric', 'min:0'],
            'notes' => ['nullable', 'string'],
            'position' => ['nullable', 'integer', 'min:0'],
        ];

        // Dynamic variant attribute rules from product's type
        $product = $this->route('product');
        if ($product instanceof CustomerQuoteProduct) {
            $type = ProductType::query()->find($product->type_id);
            if ($type) {
                foreach (ProductTypeRules::variantAttributeRules($type) as $attr => $rules) {
                    $base['attributes.'.$attr] = $rules;
                }
            }
        }

        return $base;
    }

    public function messages(): array
    {
        return [
            'label.required' => 'La etiqueta de la variante es obligatoria.',
            'quantity.min' => 'La cantidad debe ser mayor que 0.',
            'unit_price.required_without' => 'Debe proporcionar el precio unitario o el precio en unidades mínimas.',
            'unit_price_minor.required_without' => 'Debe proporcionar el precio en unidades mínimas o el precio unitario.',
        ];
    }
}
