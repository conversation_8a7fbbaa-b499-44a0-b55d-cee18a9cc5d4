<?php

namespace App\Http\Requests;

use App\Models\CustomerQuoteProductVariant;
use App\Models\ProductType;
use App\Services\ProductTypeRules;
use Illuminate\Foundation\Http\FormRequest;

class UpdateCustomerQuoteProductVariantRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        $priceCurrency = strtoupper((string) ($this->input('price_currency') ?: $this->input('currency') ?: ''));
        $unitPrice = $this->input('unit_price');
        if ($priceCurrency && $unitPrice !== null) {
            $minor = \App\Support\MoneyInput::parseToMinor($unitPrice, $priceCurrency);
            if ($minor !== null) {
                $this->merge(['unit_price_minor' => $minor, 'price_currency' => $priceCurrency]);
            }
        }
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $base = [
            'label' => ['sometimes', 'string', 'min:1'],
            'attributes' => ['sometimes', 'array'],
            'specs' => ['sometimes', 'array'],
            'hs_code' => ['sometimes', 'string'],
            'weight' => ['sometimes', 'numeric'],
            'volume' => ['sometimes', 'numeric'],
            'quantity' => ['sometimes', 'integer', 'min:1'],
            'price_currency' => ['sometimes', 'string', 'size:3'],
            'unit_price' => ['nullable', 'string'],
            'unit_price_minor' => ['nullable', 'integer', 'min:0'],
            'fx_rate_to_quote' => ['nullable', 'numeric', 'min:0'],
            'notes' => ['nullable', 'string'],
            'position' => ['nullable', 'integer', 'min:0'],
        ];

        // Dynamic variant rules using the variant's product type
        $variant = $this->route('variant');
        if ($variant instanceof CustomerQuoteProductVariant) {
            $product = $variant->quoteProduct;
            if ($product) {
                $type = ProductType::query()->find($product->type_id);
                if ($type) {
                    foreach (ProductTypeRules::variantAttributeRules($type) as $attr => $rules) {
                        $base['attributes.'.$attr] = array_merge(['sometimes'], $rules);
                    }
                }
            }
        }

        return $base;
    }
}
