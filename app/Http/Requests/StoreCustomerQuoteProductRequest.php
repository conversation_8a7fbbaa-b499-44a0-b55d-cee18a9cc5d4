<?php

namespace App\Http\Requests;

use App\Models\ProductType;
use App\Services\ProductTypeRules;
use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerQuoteProductRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $base = [
            'type_id' => ['required', 'integer', 'exists:product_types,id'],
            'name' => ['required', 'string', 'min:1'],
            'attributes' => ['required', 'array'],
            'specs' => ['nullable', 'array'],
            'hs_code' => ['nullable', 'string'],
            'weight' => ['nullable', 'numeric'],
            'volume' => ['nullable', 'numeric'],
            'group_id' => ['nullable', 'integer', 'exists:customer_quote_groups,id'],
            'notes' => ['nullable', 'string'],
            'position' => ['nullable', 'integer', 'min:0'],
        ];

        // Dynamic rules for attributes according to product type
        $typeId = (int) $this->input('type_id');
        if ($typeId) {
            $type = ProductType::query()->find($typeId);
            if ($type) {
                foreach (ProductTypeRules::productAttributeRules($type) as $attr => $rules) {
                    $base['attributes.'.$attr] = $rules;
                }
            }
        }

        return $base;
    }

    public function messages(): array
    {
        return [
            'type_id.required' => 'Debe elegir un tipo de producto.',
        ];
    }
}
