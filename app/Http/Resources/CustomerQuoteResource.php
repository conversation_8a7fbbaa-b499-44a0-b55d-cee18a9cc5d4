<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerQuoteResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $this->resource->loadMissing([
            'groups.products.variants',
            'products.variants',
        ]);

        return [
            'id' => $this->id,
            'customer_id' => $this->customer_id,
            // Legacy (mantener temporalmente para compatibilidad)
            'client_id' => $this->client_id,
            'quote_number' => $this->quote_number,
            // Nuevo estándar
            'customer_name' => $this->customer_name,
            // Legacy (mantener temporalmente para compatibilidad)
            'client_name' => $this->client_name,
            'project_id' => $this->project_id,
            'currency' => $this->currency,
            'valid_until' => optional($this->valid_until)?->toDateString(),
            'status' => $this->status->value ?? (string) $this->status,
            'notes' => $this->notes,
            'groups' => $this->groups->sortBy('position')->values()->map(function ($group) {
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'notes' => $group->notes,
                    'position' => $group->position,
                    'products' => $group->products->sortBy('position')->values()->map(function ($product) {
                        return [
                            'id' => $product->id,
                            'type_id' => $product->type_id,
                            'type_code' => $product->type_code,
                            'type_name' => $product->type_name,
                            'subcategory_code' => $product->subcategory_code,
                            'category_code' => $product->category_code,
                            'name' => $product->name,
                            'attributes' => $product->attributes,
                            'specs' => $product->specs,
                            'hs_code' => $product->hs_code,
                            'weight' => $product->weight,
                            'volume' => $product->volume,
                            'notes' => $product->notes,
                            'position' => $product->position,
                            'variants' => $product->variants->sortBy('position')->values()->map(function ($variant) {
                                return [
                                    'id' => $variant->id,
                                    'label' => $variant->label,
                                    'attributes' => $variant->attributes,
                                    'specs' => $variant->specs,
                                    'hs_code' => $variant->hs_code,
                                    'weight' => $variant->weight,
                                    'volume' => $variant->volume,
                                    'quantity' => $variant->quantity,
                                    'unit_price_minor' => $variant->unit_price_minor,
                                    'price_currency' => $variant->price_currency,
                                    'fx_rate_to_quote' => $variant->fx_rate_to_quote,
                                    'position' => $variant->position,
                                    'notes' => $variant->notes,
                                ];
                            }),
                        ];
                    }),
                ];
            }),
            'products' => $this->products
                ->whereNull('group_id')
                ->sortBy('position')->values()->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'type_id' => $product->type_id,
                        'type_code' => $product->type_code,
                        'type_name' => $product->type_name,
                        'subcategory_code' => $product->subcategory_code,
                        'category_code' => $product->category_code,
                        'name' => $product->name,
                        'attributes' => $product->attributes,
                        'specs' => $product->specs,
                        'hs_code' => $product->hs_code,
                        'weight' => $product->weight,
                        'volume' => $product->volume,
                        'notes' => $product->notes,
                        'position' => $product->position,
                        'variants' => $product->variants->sortBy('position')->values()->map(function ($variant) {
                            return [
                                'id' => $variant->id,
                                'label' => $variant->label,
                                'attributes' => $variant->attributes,
                                'specs' => $variant->specs,
                                'hs_code' => $variant->hs_code,
                                'weight' => $variant->weight,
                                'volume' => $variant->volume,
                                'quantity' => $variant->quantity,
                                'unit_price_minor' => $variant->unit_price_minor,
                                'price_currency' => $variant->price_currency,
                                'fx_rate_to_quote' => $variant->fx_rate_to_quote,
                                'position' => $variant->position,
                                'notes' => $variant->notes,
                            ];
                        }),
                    ];
                }),
        ];
    }
}
