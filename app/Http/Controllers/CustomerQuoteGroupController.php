<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCustomerQuoteGroupRequest;
use App\Http\Requests\UpdateCustomerQuoteGroupRequest;
use App\Models\CustomerQuote;
use App\Models\CustomerQuoteGroup;
use Illuminate\Http\JsonResponse;

class CustomerQuoteGroupController extends Controller
{
    public function store(StoreCustomerQuoteGroupRequest $request, CustomerQuote $quote): JsonResponse
    {
        $data = $request->validated();
        $group = $quote->groups()->create($data);

        return response()->json($group->fresh(), 201);
    }

    public function update(UpdateCustomerQuoteGroupRequest $request, CustomerQuoteGroup $group): JsonResponse
    {
        $group->update($request->validated());

        return response()->json($group->fresh());
    }

    public function destroy(CustomerQuoteGroup $group): JsonResponse
    {
        $group->delete();

        return response()->json([], 204);
    }
}
