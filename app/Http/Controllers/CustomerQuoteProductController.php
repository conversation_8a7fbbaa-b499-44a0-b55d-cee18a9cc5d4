<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCustomerQuoteProductRequest;
use App\Http\Requests\UpdateCustomerQuoteProductRequest;
use App\Models\CustomerQuote;
use App\Models\CustomerQuoteGroup;
use App\Models\CustomerQuoteProduct;
use App\Models\EventLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class CustomerQuoteProductController extends Controller
{
    public function store(StoreCustomerQuoteProductRequest $request, CustomerQuote $quote): JsonResponse
    {
        $data = $request->validated();

        if (! empty($data['group_id'])) {
            $group = CustomerQuoteGroup::query()->findOrFail($data['group_id']);
            if ($group->customer_quote_id !== $quote->id) {
                throw ValidationException::withMessages([
                    'group_id' => 'El grupo no pertenece a la misma cotización.',
                ]);
            }
        }

        // Build taxonomy snapshots from type_id
        $type = \App\Models\ProductType::query()->findOrFail($data['type_id']);
        $sub = \App\Models\ProductSubcategory::query()->findOrFail($type->subcategory_id);
        $cat = \App\Models\ProductCategory::query()->findOrFail($sub->category_id);

        $snap = [
            'type_code' => $type->code,
            'type_name' => $type->name,
            'subcategory_code' => $sub->code,
            'category_code' => $cat->code,
        ];

        $product = $quote->products()->create($data + $snap);

        // Registrar evento de creación
        EventLog::create([
            'entity' => 'quote_product',
            'entity_id' => $product->id,
            'action' => 'created',
            'actor_id' => auth()->id(),
            'payload' => [
                'quote_id' => $quote->id,
                'quote_number' => $quote->quote_number,
                'product_name' => $product->name,
                'type_id' => $product->type_id,
                'type_code' => $product->type_code,
                'type_name' => $product->type_name,
                'category_code' => $product->category_code,
                'subcategory_code' => $product->subcategory_code,
                'group_id' => $product->group_id,
                'quantity' => $product->quantity,
                'notes' => $product->notes,
            ],
            'created_at' => now(),
        ]);

        return response()->json($product->fresh(), 201);
    }

    public function update(UpdateCustomerQuoteProductRequest $request, CustomerQuoteProduct $product): JsonResponse
    {
        $data = $request->validated();

        if (array_key_exists('group_id', $data) && ! empty($data['group_id'])) {
            $group = CustomerQuoteGroup::query()->findOrFail($data['group_id']);
            if ($group->customer_quote_id !== $product->customer_quote_id) {
                throw ValidationException::withMessages([
                    'group_id' => 'El grupo no pertenece a la misma cotización.',
                ]);
            }
        }

        // If type_id provided, refresh snapshots
        if (array_key_exists('type_id', $data) && ! empty($data['type_id'])) {
            $type = \App\Models\ProductType::query()->findOrFail($data['type_id']);
            $sub = \App\Models\ProductSubcategory::query()->findOrFail($type->subcategory_id);
            $cat = \App\Models\ProductCategory::query()->findOrFail($sub->category_id);
            $data = $data + [
                'type_code' => $type->code,
                'type_name' => $type->name,
                'subcategory_code' => $sub->code,
                'category_code' => $cat->code,
            ];
        }

        // Capturar datos anteriores para el log
        $previousData = $product->only([
            'name', 'type_id', 'group_id', 'quantity', 'notes',
        ]);

        $product->update($data);

        // Registrar evento de actualización
        EventLog::create([
            'entity' => 'quote_product',
            'entity_id' => $product->id,
            'action' => 'updated',
            'actor_id' => auth()->id(),
            'payload' => [
                'quote_id' => $product->customer_quote_id,
                'quote_number' => $product->quote->quote_number,
                'product_name' => $product->name,
                'previous_data' => $previousData,
                'new_data' => $product->only([
                    'name', 'type_id', 'group_id', 'quantity', 'notes',
                ]),
            ],
            'created_at' => now(),
        ]);

        return response()->json($product->fresh());
    }

    public function destroy(CustomerQuoteProduct $product): JsonResponse
    {
        // Capturar datos antes de eliminar para el log
        $productData = $product->only([
            'name', 'type_id', 'group_id', 'quantity', 'notes',
        ]);

        $product->delete();

        // Registrar evento de eliminación
        EventLog::create([
            'entity' => 'quote_product',
            'entity_id' => $product->id,
            'action' => 'deleted',
            'actor_id' => auth()->id(),
            'payload' => [
                'quote_id' => $product->customer_quote_id,
                'quote_number' => $product->quote->quote_number,
                'deleted_product_data' => $productData,
            ],
            'created_at' => now(),
        ]);

        return response()->json([], 204);
    }
}
