<?php

namespace App\Http\Controllers;

use App\Enums\CustomerQuoteStatus;
use App\Http\Concerns\HandlesApiErrors;
use App\Http\Requests\StoreCustomerQuoteRequest;
use App\Http\Requests\UpdateCustomerQuoteRequest;
use App\Http\Resources\CustomerQuoteResource;
use App\Models\CustomerQuote;
use App\Models\EventLog;
use App\Services\ErrorReportingService;
use Illuminate\Http\JsonResponse;
use Throwable;

class CustomerQuoteController extends Controller
{
    use HandlesApiErrors;

    public function store(StoreCustomerQuoteRequest $request): JsonResponse
    {
        try {
            $data = $request->validated();

            // Compatibilidad: si viene client_name, mapear a customer_name
            if (empty($data['customer_name']) && ! empty($data['client_name'])) {
                $data['customer_name'] = $data['client_name'];
                unset($data['client_name']);
            }

            $quote = new CustomerQuote($data);
            $quote->status = CustomerQuoteStatus::Borrador;
            $quote->save();

            // Registrar evento de creación
            EventLog::create([
                'entity' => 'quote',
                'entity_id' => $quote->id,
                'action' => 'created',
                'actor_id' => auth()->id(),
                'payload' => [
                    'quote_number' => $quote->quote_number,
                    'customer_id' => $quote->customer_id,
                    'customer_name' => $quote->customer_name,
                    'project_id' => $quote->project_id,
                    'currency' => $quote->currency,
                    'country' => $quote->country,
                    'status' => $quote->status->value,
                ],
                'created_at' => now(),
            ]);

            app(ErrorReportingService::class)->reportUserAction(
                'quote_created',
                auth()->id(),
                ['quote_id' => $quote->id, 'quote_number' => $quote->quote_number]
            );

            return $this->createdResponse(
                new CustomerQuoteResource($quote->fresh()),
                'Quote created successfully'
            );

        } catch (Throwable $e) {
            return $this->handleApiError($e, 'create_customer_quote');
        }
    }

    public function show(CustomerQuote $quote): CustomerQuoteResource
    {
        return new CustomerQuoteResource($quote);
    }

    public function update(UpdateCustomerQuoteRequest $request, CustomerQuote $quote): JsonResponse
    {
        $data = $request->validated();

        // Compatibilidad: si viene client_name, mapear a customer_name
        if (empty($data['customer_name']) && ! empty($data['client_name'])) {
            $data['customer_name'] = $data['client_name'];
            unset($data['client_name']);
        }

        // Capturar estado anterior para el log
        $previousStatus = $quote->status;
        $previousData = $quote->only([
            'customer_id', 'customer_name', 'project_id',
            'currency', 'country', 'valid_until', 'notes',
        ]);

        $quote->update($data);

        // Verificar si realmente hubo cambios
        $statusChanged = $previousStatus !== $quote->status;
        $dataChanged = $previousData !== $quote->only([
            'customer_id', 'customer_name', 'project_id',
            'currency', 'country', 'valid_until', 'notes',
        ]);

        // Solo crear event log si hubo cambios reales
        if ($statusChanged || $dataChanged) {
            EventLog::create([
                'entity' => 'quote',
                'entity_id' => $quote->id,
                'action' => 'updated',
                'actor_id' => auth()->id(),
                'payload' => [
                    'quote_number' => $quote->quote_number,
                    'previous_data' => $previousData,
                    'new_data' => $quote->only([
                        'customer_id', 'customer_name', 'project_id',
                        'currency', 'country', 'valid_until', 'notes',
                    ]),
                    'status_changed' => $statusChanged,
                    'data_changed' => $dataChanged,
                    'previous_status' => $previousStatus->value,
                    'new_status' => $quote->status->value,
                ],
                'created_at' => now(),
            ]);
        }

        return response()->json(new CustomerQuoteResource($quote->fresh()));
    }
}
