<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCustomerQuoteProductVariantRequest;
use App\Http\Requests\UpdateCustomerQuoteProductVariantRequest;
use App\Models\CustomerQuoteProduct;
use App\Models\CustomerQuoteProductVariant;
use App\Models\EventLog;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class CustomerQuoteProductVariantController extends Controller
{
    public function store(StoreCustomerQuoteProductVariantRequest $request, CustomerQuoteProduct $product): JsonResponse
    {
        $data = $request->validated();

        // Catálogo maestro ya no aplica; sólo validaciones locales

        // Moneda y FX: si la moneda del precio difiere de la moneda de la cotización, fx_rate_to_quote es obligatoria (>0)
        $quoteCurrency = strtoupper($product->quote->currency ?? '');
        $priceCurrency = strtoupper($data['price_currency'] ?? $quoteCurrency);
        if ($priceCurrency === $quoteCurrency) {
            $data['fx_rate_to_quote'] = 1;
        } else {
            if (empty($data['fx_rate_to_quote']) || (float) $data['fx_rate_to_quote'] <= 0) {
                throw ValidationException::withMessages([
                    'fx_rate_to_quote' => 'Se requiere la tasa de conversión a la moneda de la cotización.',
                ]);
            }
        }
        // Nunca persistimos 'unit_price' directo (usamos unit_price_minor + cast)
        if (isset($data['unit_price'])) {
            unset($data['unit_price']);
        }
        $variant = $product->variants()->create($data + [
            'quote_product_id' => $product->id,
        ]);

        // Registrar evento de creación
        EventLog::create([
            'entity' => 'quote_variant',
            'entity_id' => $variant->id,
            'action' => 'created',
            'actor_id' => auth()->id(),
            'payload' => [
                'quote_id' => $product->customer_quote_id,
                'quote_number' => $product->quote->quote_number,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'variant_label' => $variant->label,
                'quantity' => $variant->quantity,
                'unit_price_minor' => $variant->unit_price_minor,
                'price_currency' => $variant->price_currency,
                'fx_rate_to_quote' => $variant->fx_rate_to_quote,
                'notes' => $variant->notes,
            ],
            'created_at' => now(),
        ]);

        return response()->json($variant->fresh(), 201);
    }

    public function update(UpdateCustomerQuoteProductVariantRequest $request, CustomerQuoteProductVariant $variant): JsonResponse
    {
        $data = $request->validated();

        // Moneda y FX en update: validar coherencia
        $quoteCurrency = strtoupper($variant->quoteProduct->quote->currency ?? '');
        $priceCurrency = strtoupper($data['price_currency'] ?? $variant->price_currency ?? $quoteCurrency);
        $fx = $data['fx_rate_to_quote'] ?? $variant->fx_rate_to_quote;
        if ($priceCurrency === $quoteCurrency) {
            $data['fx_rate_to_quote'] = 1;
        } else {
            if (empty($fx) || (float) $fx <= 0) {
                throw ValidationException::withMessages([
                    'fx_rate_to_quote' => 'Se requiere la tasa de conversión a la moneda de la cotización.',
                ]);
            }
        }
        if (isset($data['unit_price'])) {
            unset($data['unit_price']);
        }

        // Capturar datos anteriores para el log
        $previousData = $variant->only([
            'label', 'quantity', 'unit_price_minor', 'price_currency',
            'fx_rate_to_quote', 'notes',
        ]);

        $variant->update($data);

        // Registrar evento de actualización
        EventLog::create([
            'entity' => 'quote_variant',
            'entity_id' => $variant->id,
            'action' => 'updated',
            'actor_id' => auth()->id(),
            'payload' => [
                'quote_id' => $variant->quoteProduct->customer_quote_id,
                'quote_number' => $variant->quoteProduct->quote->quote_number,
                'product_id' => $variant->quote_product_id,
                'product_name' => $variant->quoteProduct->name,
                'variant_label' => $variant->label,
                'previous_data' => $previousData,
                'new_data' => $variant->only([
                    'label', 'quantity', 'unit_price_minor', 'price_currency',
                    'fx_rate_to_quote', 'notes',
                ]),
            ],
            'created_at' => now(),
        ]);

        return response()->json($variant->fresh());
    }

    public function destroy(CustomerQuoteProductVariant $variant): JsonResponse
    {
        // Capturar datos antes de eliminar para el log
        $variantData = $variant->only([
            'label', 'quantity', 'unit_price_minor', 'price_currency',
            'fx_rate_to_quote', 'notes',
        ]);

        $variant->delete();

        // Registrar evento de eliminación
        EventLog::create([
            'entity' => 'quote_variant',
            'entity_id' => $variant->id,
            'action' => 'deleted',
            'actor_id' => auth()->id(),
            'payload' => [
                'quote_id' => $variant->quoteProduct->customer_quote_id,
                'quote_number' => $variant->quoteProduct->quote->quote_number,
                'product_id' => $variant->quote_product_id,
                'product_name' => $variant->quoteProduct->name,
                'deleted_variant_data' => $variantData,
            ],
            'created_at' => now(),
        ]);

        return response()->json([], 204);
    }
}
