<?php

namespace App\Http\Controllers;

use App\Models\CustomerQuote;
use App\Models\CustomerQuoteProductVariant;
use App\Models\ProductionBatch;
use App\Models\PurchaseOrderItem;
use Illuminate\Http\JsonResponse;

class SourcingSummaryController extends Controller
{
    /**
     * Get sourcing summary for a quote.
     */
    public function summary(CustomerQuote $clientQuote): JsonResponse
    {
        // Obtener todas las variantes de la cotización
        $variants = CustomerQuoteProductVariant::whereHas('quoteProduct', function ($query) use ($clientQuote) {
            $query->where('customer_quote_id', $clientQuote->id);
        })->with(['quoteProduct'])->get();

        $summary = [];

        foreach ($variants as $variant) {
            // Calcular cantidades asignadas en OCs
            $poItems = PurchaseOrderItem::where('quote_product_variant_id', $variant->id)->get();
            $totalPoQuantity = $poItems->sum('quantity');

            // Calcular cantidades en lotes de producción
            $batches = ProductionBatch::whereIn('purchase_order_item_id', $poItems->pluck('id'))->get();
            $totalBatchQuantity = $batches->sum('quantity');

            // Calcular diferencias
            $poDifference = $totalPoQuantity - $variant->quantity;
            $batchDifference = $totalBatchQuantity - $totalPoQuantity;

            $summary[] = [
                'variant_id' => $variant->id,
                'variant_name' => $variant->name,
                'variant_quantity' => $variant->quantity,
                'po_quantity' => $totalPoQuantity,
                'batch_quantity' => $totalBatchQuantity,
                'po_difference' => $poDifference,
                'batch_difference' => $batchDifference,
                'is_reconciled' => $poDifference === 0 && $batchDifference === 0,
                'po_items' => $poItems->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'purchase_order_id' => $item->purchase_order_id,
                        'quantity' => $item->quantity,
                        'unit_price' => $item->unit_price,
                        'status' => $item->purchaseOrder->status,
                    ];
                }),
                'batches' => $batches->map(function ($batch) {
                    return [
                        'id' => $batch->id,
                        'quantity' => $batch->quantity,
                        'status' => $batch->status,
                        'pool_state' => $batch->pool_state,
                    ];
                }),
            ];
        }

        // Resumen general
        $totalVariants = $variants->count();
        $reconciledVariants = collect($summary)->where('is_reconciled', true)->count();
        $totalPoQuantity = collect($summary)->sum('po_quantity');
        $totalBatchQuantity = collect($summary)->sum('batch_quantity');

        return response()->json([
            'quote_id' => $clientQuote->id,
            'quote_number' => $clientQuote->quote_number,
            'summary' => [
                'total_variants' => $totalVariants,
                'reconciled_variants' => $reconciledVariants,
                'unreconciled_variants' => $totalVariants - $reconciledVariants,
                'total_po_quantity' => $totalPoQuantity,
                'total_batch_quantity' => $totalBatchQuantity,
                'is_fully_reconciled' => $reconciledVariants === $totalVariants,
            ],
            'variants' => $summary,
        ]);
    }
}
