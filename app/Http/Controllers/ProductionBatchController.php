<?php

namespace App\Http\Controllers;

use App\Actions\CreateProductionBatchAction;
use App\Actions\GenerateCorrelationIdAction;
use App\DTOs\CreateProductionBatchDto;
use App\Http\Requests\ChangeStatusRequest;
use App\Http\Requests\StoreProductionBatchRequest;
use App\Models\EventLog;
use App\Models\ProductionBatch;
use App\Models\PurchaseOrderItem;
use Illuminate\Http\JsonResponse;

class ProductionBatchController extends Controller
{
    public function __construct(
        private CreateProductionBatchAction $createProductionBatchAction,
        private GenerateCorrelationIdAction $generateCorrelationIdAction,
    ) {}

    /**
     * Store a newly created resource in storage.
     */
    public function store(PurchaseOrderItem $purchaseOrderItem, StoreProductionBatchRequest $request): JsonResponse
    {
        $correlationId = $this->generateCorrelationIdAction->forProductionBatch(
            $purchaseOrderItem->id,
            $request->quantity
        );

        $dto = new CreateProductionBatchDto(
            purchaseOrderItemId: $purchaseOrderItem->id,
            quantity: $request->quantity,
            plannedStart: $request->planned_start ? new \DateTime($request->planned_start) : null,
            plannedFinish: $request->planned_finish ? new \DateTime($request->planned_finish) : null,
            notes: $request->notes,
            correlationId: $correlationId,
        );

        $batch = $this->createProductionBatchAction->handle($dto);

        return response()->json($batch->load(['purchaseOrderItem']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ProductionBatch $productionBatch): JsonResponse
    {
        $productionBatch->load(['purchaseOrderItem.purchaseOrder', 'purchaseOrderItem.quoteProductVariant']);

        return response()->json($productionBatch);
    }

    /**
     * Change batch status.
     */
    public function changeStatus(ProductionBatch $productionBatch, ChangeStatusRequest $request): JsonResponse
    {
        $validTransitions = [
            'borrador' => ['planificado'],
            'planificado' => ['en_produccion'],
            'en_produccion' => ['completado'],
        ];

        $currentStatus = $productionBatch->status;
        $newStatus = $request->status;

        if (! isset($validTransitions[$currentStatus]) || ! in_array($newStatus, $validTransitions[$currentStatus])) {
            return response()->json([
                'error' => "No se puede cambiar de estado '{$currentStatus}' a '{$newStatus}'.",
            ], 422);
        }

        $productionBatch->update(['status' => $newStatus]);

        EventLog::create([
            'entity' => 'batch',
            'entity_id' => $productionBatch->id,
            'action' => 'status_changed',
            'actor_id' => auth()->id() ?? 1,
            'payload' => [
                'previous_status' => $currentStatus,
                'new_status' => $newStatus,
            ],
            'created_at' => now(),
        ]);

        return response()->json($productionBatch);
    }
}
