<?php

namespace App\Http\Controllers;

use App\Actions\AddItemToPurchaseOrderAction;
use App\Actions\CreatePurchaseOrderAction;
use App\Actions\GenerateCorrelationIdAction;
use App\DTOs\CreatePurchaseOrderDto;
use App\DTOs\CreatePurchaseOrderItemDto;
use App\Http\Requests\StorePurchaseOrderItemRequest;
use App\Http\Requests\StorePurchaseOrderRequest;
use App\Models\CustomerQuote;
use App\Models\EventLog;
use App\Models\PurchaseOrder;
use Illuminate\Http\JsonResponse;

class PurchaseOrderController extends Controller
{
    public function __construct(
        private CreatePurchaseOrderAction $createPurchaseOrderAction,
        private AddItemToPurchaseOrderAction $addItemToPurchaseOrderAction,
        private GenerateCorrelationIdAction $generateCorrelationIdAction,
    ) {}

    /**
     * Store a newly created resource in storage.
     */
    public function store(CustomerQuote $clientQuote, StorePurchaseOrderRequest $request): JsonResponse
    {
        $correlationId = $this->generateCorrelationIdAction->forPurchaseOrder(
            $clientQuote->id,
            $request->supplier_id,
            $request->currency,
            $request->incoterm,
            $request->terms
        );

        $dto = new CreatePurchaseOrderDto(
            quoteId: $clientQuote->id,
            supplierId: $request->supplier_id,
            currency: $request->currency,
            incoterm: $request->incoterm,
            terms: $request->terms,
            status: 'borrador',
            notes: $request->notes,
            correlationId: $correlationId,
        );

        $purchaseOrder = $this->createPurchaseOrderAction->handle($dto);

        return response()->json($purchaseOrder->load(['supplier', 'quote']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(PurchaseOrder $purchaseOrder): JsonResponse
    {
        $purchaseOrder->load(['supplier', 'quote', 'items.quoteProductVariant', 'items.productionBatches']);

        return response()->json($purchaseOrder);
    }

    /**
     * Add item to purchase order.
     */
    public function addItem(PurchaseOrder $purchaseOrder, StorePurchaseOrderItemRequest $request): JsonResponse
    {
        $correlationId = $this->generateCorrelationIdAction->forPurchaseOrderItem(
            $purchaseOrder->id,
            $request->quote_product_variant_id,
            $request->quantity
        );

        $dto = new CreatePurchaseOrderItemDto(
            purchaseOrderId: $purchaseOrder->id,
            quoteProductVariantId: $request->quote_product_variant_id,
            quantity: $request->quantity,
            unitPrice: $request->unit_price,
            leadTimeDays: $request->lead_time_days,
            notes: $request->notes,
            correlationId: $correlationId,
        );

        $item = $this->addItemToPurchaseOrderAction->handle($dto);

        return response()->json($item->load(['quoteProductVariant']), 201);
    }

    /**
     * Send purchase order.
     */
    public function send(PurchaseOrder $purchaseOrder): JsonResponse
    {
        if ($purchaseOrder->status !== 'borrador') {
            return response()->json(['error' => 'Solo se pueden enviar órdenes en estado borrador.'], 422);
        }

        $purchaseOrder->update(['status' => 'enviada']);

        EventLog::create([
            'entity' => 'po',
            'entity_id' => $purchaseOrder->id,
            'action' => 'sent',
            'actor_id' => auth()->id() ?? 1,
            'payload' => ['previous_status' => 'borrador'],
            'created_at' => now(),
        ]);

        return response()->json($purchaseOrder);
    }

    /**
     * Confirm purchase order.
     */
    public function confirm(PurchaseOrder $purchaseOrder): JsonResponse
    {
        if (! in_array($purchaseOrder->status, ['enviada', 'borrador'])) {
            return response()->json(['error' => 'Solo se pueden confirmar órdenes enviadas o en borrador.'], 422);
        }

        $previousStatus = $purchaseOrder->status;
        $purchaseOrder->update(['status' => 'confirmada']);

        EventLog::create([
            'entity' => 'po',
            'entity_id' => $purchaseOrder->id,
            'action' => 'confirmed',
            'actor_id' => auth()->id() ?? 1,
            'payload' => ['previous_status' => $previousStatus],
            'created_at' => now(),
        ]);

        return response()->json($purchaseOrder);
    }

    /**
     * Close purchase order.
     */
    public function close(PurchaseOrder $purchaseOrder): JsonResponse
    {
        if ($purchaseOrder->status !== 'confirmada') {
            return response()->json(['error' => 'Solo se pueden cerrar órdenes confirmadas.'], 422);
        }

        $purchaseOrder->update(['status' => 'cerrada']);

        EventLog::create([
            'entity' => 'po',
            'entity_id' => $purchaseOrder->id,
            'action' => 'closed',
            'actor_id' => auth()->id() ?? 1,
            'payload' => ['previous_status' => 'confirmada'],
            'created_at' => now(),
        ]);

        return response()->json($purchaseOrder);
    }
}
