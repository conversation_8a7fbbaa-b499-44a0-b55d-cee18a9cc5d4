<?php

namespace App\Http\Controllers;

use App\Models\Supplier;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class SupplierController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $suppliers = Supplier::where('active', true)
            ->orderBy('name')
            ->get();

        return response()->json($suppliers);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'code' => ['required', 'string', 'unique:suppliers,code'],
            'name' => ['required', 'string', 'max:255'],
            'contact' => ['nullable', 'array'],
            'active' => ['boolean'],
            'notes' => ['nullable', 'string'],
        ], [
            'code.required' => 'El código es obligatorio.',
            'code.unique' => 'El código ya existe.',
            'name.required' => 'El nombre es obligatorio.',
        ]);

        $supplier = Supplier::create($validated);

        return response()->json($supplier, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Supplier $supplier): JsonResponse
    {
        return response()->json($supplier);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Supplier $supplier): JsonResponse
    {
        $validated = $request->validate([
            'code' => ['required', 'string', Rule::unique('suppliers')->ignore($supplier->id)],
            'name' => ['required', 'string', 'max:255'],
            'contact' => ['nullable', 'array'],
            'active' => ['boolean'],
            'notes' => ['nullable', 'string'],
        ]);

        $supplier->update($validated);

        return response()->json($supplier);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Supplier $supplier): JsonResponse
    {
        $supplier->delete();

        return response()->json(null, 204);
    }
}
