<?php

namespace App\Http\Concerns;

use App\Exceptions\BusinessLogicException;
use App\Services\ErrorReportingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Throwable;

trait HandlesApiErrors
{
    /**
     * Handle exceptions in API controllers with consistent response format
     */
    protected function handleApiError(Throwable $exception, string $operation = 'operation'): JsonResponse
    {
        $errorService = app(ErrorReportingService::class);

        return match (true) {
            $exception instanceof ValidationException => $this->handleValidationError($exception),
            $exception instanceof BusinessLogicException => $this->handleBusinessError($exception),
            default => $this->handleGenericError($exception, $operation, $errorService),
        };
    }

    /**
     * Handle validation errors
     */
    private function handleValidationError(ValidationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $exception->errors(),
            'error_code' => 'VALIDATION_ERROR',
            'timestamp' => now()->toISOString(),
        ], Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Handle business logic errors
     */
    private function handleBusinessError(BusinessLogicException $exception): JsonResponse
    {
        app(ErrorReportingService::class)->reportBusinessError($exception->getMessage(), [
            'context' => $exception->context,
        ]);

        return response()->json([
            'success' => false,
            'message' => $exception->getMessage(),
            'error_code' => 'BUSINESS_ERROR',
            'context' => $exception->context,
            'timestamp' => now()->toISOString(),
        ], Response::HTTP_UNPROCESSABLE_ENTITY);
    }

    /**
     * Handle generic errors
     */
    private function handleGenericError(Throwable $exception, string $operation, ErrorReportingService $errorService): JsonResponse
    {
        $errorService->reportCriticalError("Unexpected error in {$operation}", [
            'operation' => $operation,
            'url' => request()->fullUrl(),
            'user_id' => auth()->id(),
        ], $exception);

        $message = config('app.debug') ? $exception->getMessage() : 'An unexpected error occurred';

        return response()->json([
            'success' => false,
            'message' => $message,
            'error_code' => 'INTERNAL_ERROR',
            'timestamp' => now()->toISOString(),
        ], Response::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * Create a success response with consistent format
     */
    protected function successResponse(mixed $data = null, string $message = 'Success', int $status = Response::HTTP_OK): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => now()->toISOString(),
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $status);
    }

    /**
     * Create a created response (201)
     */
    protected function createdResponse(mixed $data, string $message = 'Resource created successfully'): JsonResponse
    {
        return $this->successResponse($data, $message, Response::HTTP_CREATED);
    }

    /**
     * Create a no content response (204)
     */
    protected function noContentResponse(): Response
    {
        return response()->noContent();
    }
}
