<?php

namespace App\Actions;

class GenerateCorrelationIdAction
{
    public function forPurchaseOrder(int $quoteId, int $supplierId, string $currency, string $incoterm, ?array $terms): string
    {
        $data = [
            'quote_id' => $quoteId,
            'supplier_id' => $supplierId,
            'currency' => $currency,
            'incoterm' => $incoterm,
            'terms' => $terms ? json_encode($terms, JSON_UNESCAPED_SLASHES) : null,
        ];

        return hash('sha256', json_encode($data, JSON_UNESCAPED_SLASHES));
    }

    public function forPurchaseOrderItem(int $purchaseOrderId, int $quoteProductVariantId, int $quantity): string
    {
        $data = [
            'purchase_order_id' => $purchaseOrderId,
            'quote_product_variant_id' => $quoteProductVariantId,
            'quantity' => $quantity,
        ];

        return hash('sha256', json_encode($data, JSON_UNESCAPED_SLASHES));
    }

    public function forProductionBatch(int $purchaseOrderItemId, int $quantity, ?string $splitKey = null): string
    {
        $data = [
            'purchase_order_item_id' => $purchaseOrderItemId,
            'quantity' => $quantity,
            'split_key' => $splitKey,
        ];

        return hash('sha256', json_encode($data, JSON_UNESCAPED_SLASHES));
    }
}
