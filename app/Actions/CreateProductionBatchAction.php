<?php

namespace App\Actions;

use App\DTOs\CreateProductionBatchDto;
use App\Models\EventLog;
use App\Models\ProductionBatch;
use Illuminate\Support\Facades\DB;

class CreateProductionBatchAction
{
    public function handle(CreateProductionBatchDto $dto): ProductionBatch
    {
        return DB::transaction(function () use ($dto) {
            // Verificar si ya existe un lote con el mismo correlation_id
            $existing = ProductionBatch::where('correlation_id', $dto->correlationId)->first();

            if ($existing) {
                return $existing;
            }

            $batch = ProductionBatch::create([
                'purchase_order_item_id' => $dto->purchaseOrderItemId,
                'quantity' => $dto->quantity,
                'planned_start' => $dto->plannedStart,
                'planned_finish' => $dto->plannedFinish,
                'notes' => $dto->notes,
                'correlation_id' => $dto->correlationId,
                'status' => 'borrador',
                'pool_state' => 'available',
            ]);

            // Registrar evento
            EventLog::create([
                'entity' => 'batch',
                'entity_id' => $batch->id,
                'action' => 'created',
                'actor_id' => auth()->id() ?? 1,
                'payload' => [
                    'purchase_order_item_id' => $dto->purchaseOrderItemId,
                    'quantity' => $dto->quantity,
                    'planned_start' => $dto->plannedStart?->toISOString(),
                    'planned_finish' => $dto->plannedFinish?->toISOString(),
                ],
                'created_at' => now(),
            ]);

            return $batch;
        });
    }
}
