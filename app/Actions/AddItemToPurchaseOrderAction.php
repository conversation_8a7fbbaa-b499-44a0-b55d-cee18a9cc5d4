<?php

namespace App\Actions;

use App\DTOs\CreatePurchaseOrderItemDto;
use App\Models\EventLog;
use App\Models\PurchaseOrderItem;
use Illuminate\Support\Facades\DB;

class AddItemToPurchaseOrderAction
{
    public function handle(CreatePurchaseOrderItemDto $dto): PurchaseOrderItem
    {
        return DB::transaction(function () use ($dto) {
            // Verificar si ya existe un ítem con el mismo correlation_id
            $existing = PurchaseOrderItem::where('correlation_id', $dto->correlationId)->first();

            if ($existing) {
                return $existing;
            }

            $item = PurchaseOrderItem::create([
                'purchase_order_id' => $dto->purchaseOrderId,
                'quote_product_variant_id' => $dto->quoteProductVariantId,
                'quantity' => $dto->quantity,
                'unit_price' => $dto->unitPrice,
                'lead_time_days' => $dto->leadTimeDays,
                'notes' => $dto->notes,
                'correlation_id' => $dto->correlationId,
            ]);

            // Registrar evento
            EventLog::create([
                'entity' => 'po_item',
                'entity_id' => $item->id,
                'action' => 'created',
                'actor_id' => auth()->id() ?? 1,
                'payload' => [
                    'purchase_order_id' => $dto->purchaseOrderId,
                    'quote_product_variant_id' => $dto->quoteProductVariantId,
                    'quantity' => $dto->quantity,
                    'unit_price' => $dto->unitPrice,
                ],
                'created_at' => now(),
            ]);

            return $item;
        });
    }
}
