<?php

namespace App\Actions;

use App\DTOs\CreatePurchaseOrderDto;
use App\Models\EventLog;
use App\Models\PurchaseOrder;
use Illuminate\Support\Facades\DB;

class CreatePurchaseOrderAction
{
    public function handle(CreatePurchaseOrderDto $dto): PurchaseOrder
    {
        return DB::transaction(function () use ($dto) {
            // Verificar si ya existe una OC con el mismo correlation_id
            $existing = PurchaseOrder::where('correlation_id', $dto->correlationId)->first();

            if ($existing) {
                return $existing;
            }

            $purchaseOrder = PurchaseOrder::create([
                'quote_id' => $dto->quoteId,
                'supplier_id' => $dto->supplierId,
                'currency' => $dto->currency,
                'incoterm' => $dto->incoterm,
                'terms' => $dto->terms,
                'notes' => $dto->notes,
                'correlation_id' => $dto->correlationId,
                'status' => 'borrador',
            ]);

            // Registrar evento
            EventLog::create([
                'entity' => 'po',
                'entity_id' => $purchaseOrder->id,
                'action' => 'created',
                'actor_id' => auth()->id(),
                'payload' => [
                    'quote_id' => $dto->quoteId,
                    'supplier_id' => $dto->supplierId,
                    'currency' => $dto->currency,
                    'incoterm' => $dto->incoterm,
                ],
                'created_at' => now(),
            ]);

            return $purchaseOrder;
        });
    }
}
