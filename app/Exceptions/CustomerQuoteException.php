<?php

namespace App\Exceptions;

/**
 * Exception for customer quote specific business logic violations
 */
class CustomerQuoteException extends BusinessLogicException
{
    public static function invalidStatus(string $currentStatus, string $attemptedStatus): self
    {
        return new self(
            "Cannot change quote status from {$currentStatus} to {$attemptedStatus}",
            [
                'current_status' => $currentStatus,
                'attempted_status' => $attemptedStatus,
            ]
        );
    }

    public static function expiredQuote(string $quoteNumber): self
    {
        return new self(
            "Quote {$quoteNumber} has expired and cannot be modified",
            ['quote_number' => $quoteNumber]
        );
    }

    public static function invalidQuantity(int $quantity): self
    {
        return new self(
            "Quantity must be greater than 0, received: {$quantity}",
            ['quantity' => $quantity]
        );
    }

    public static function variantNotBelongsToProduct(int $variantId, int $productId): self
    {
        return new self(
            "Variant {$variantId} does not belong to product {$productId}",
            [
                'variant_id' => $variantId,
                'product_id' => $productId,
            ]
        );
    }

    public static function noVariantsForProduct(int $productId): self
    {
        return new self(
            "Product {$productId} must have at least one variant",
            ['product_id' => $productId]
        );
    }
}
