<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Exception for business logic violations
 */
class BusinessLogicException extends Exception
{
    public function __construct(
        string $message = 'Business logic violation',
        public readonly array $context = [],
        int $code = 0,
        ?\Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Render the exception as an HTTP response
     */
    public function render(Request $request): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $this->getMessage(),
            'error_code' => 'BUSINESS_LOGIC_ERROR',
            'context' => $this->context,
            'timestamp' => now()->toISOString(),
        ];

        return response()->json($response, 422);
    }

    /**
     * Report the exception
     */
    public function report(): bool
    {
        logger()->warning('Business logic violation: '.$this->getMessage(), [
            'context' => $this->context,
            'exception' => $this,
        ]);

        return true;
    }
}
