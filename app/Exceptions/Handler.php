<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $e): Response
    {
        // Handle API errors with consistent JSON response
        if ($request->expectsJson()) {
            return $this->handleApiException($request, $e);
        }

        return parent::render($request, $e);
    }

    /**
     * Handle API exceptions with consistent JSON format
     */
    private function handleApiException(Request $request, Throwable $e): Response
    {
        $statusCode = $this->getStatusCode($e);

        $response = [
            'success' => false,
            'message' => $e->getMessage() ?: 'An error occurred',
            'error_code' => get_class($e),
            'timestamp' => now()->toISOString(),
            'path' => $request->path(),
        ];

        // Add validation errors for validation exceptions
        if ($e instanceof ValidationException) {
            $response['errors'] = $e->errors();
        }

        // Add debug information in non-production environments
        if (config('app.debug')) {
            $response['debug'] = [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => collect($e->getTrace())->take(5)->toArray(),
            ];
        }

        // Log the error for monitoring
        $this->logError($request, $e, $statusCode);

        return response()->json($response, $statusCode);
    }

    /**
     * Get appropriate HTTP status code for exception
     */
    private function getStatusCode(Throwable $e): int
    {
        return match (true) {
            $e instanceof ValidationException => 422,
            $e instanceof \Illuminate\Auth\AuthenticationException => 401,
            $e instanceof \Illuminate\Auth\Access\AuthorizationException => 403,
            $e instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException => 404,
            $e instanceof \Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException => 405,
            method_exists($e, 'getStatusCode') => $e->getStatusCode(),
            default => 500,
        };
    }

    /**
     * Log error with context for monitoring
     */
    private function logError(Request $request, Throwable $e, int $statusCode): void
    {
        $context = [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'user_id' => $request->user()?->id,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'status_code' => $statusCode,
        ];

        if ($statusCode >= 500) {
            logger()->error($e->getMessage(), $context + ['exception' => $e]);
        } else {
            logger()->warning($e->getMessage(), $context);
        }
    }
}
