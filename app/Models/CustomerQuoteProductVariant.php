<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerQuoteProductVariant extends Model
{
    use HasFactory;

    protected static function booted(): void
    {
        static::saved(function (CustomerQuoteProductVariant $variant) {
            $variant->quoteProduct?->customerQuote?->clearAmountCache();
        });

        static::deleted(function (CustomerQuoteProductVariant $variant) {
            $variant->quoteProduct?->customerQuote?->clearAmountCache();
        });
    }

    protected $table = 'customer_quote_product_variants';

    protected $fillable = [
        'quote_product_id', 'label', 'attributes', 'specs', 'hs_code', 'weight', 'volume',
        'quantity', 'unit_price', 'unit_price_minor', 'price_currency', 'fx_rate_to_quote', 'notes', 'position',
    ];

    protected function casts(): array
    {
        return [
            'attributes' => 'array',
            'specs' => 'array',
            'unit_price' => \App\Casts\MoneyCast::class,
            'fx_rate_to_quote' => 'decimal:8',
        ];
    }

    public function quoteProduct(): BelongsTo
    {
        return $this->belongsTo(CustomerQuoteProduct::class, 'quote_product_id');
    }

    // product() and variant() relationships removed; using local fields only

    public function totalAmount(): float
    {
        $qty = (int) ($this->quantity ?? 0);
        $unit = $this->unit_price; // Money VO (price currency)
        if (! $unit instanceof \App\ValueObjects\Money) {
            return 0.0;
        }
        $line = $unit->multiply($qty); // Money in price_currency
        $quoteCurrency = strtoupper($this->quoteProduct?->quote?->currency ?? '');
        $priceCurrency = strtoupper($unit->currency);
        $fx = (float) ($this->fx_rate_to_quote ?? 1);
        if ($priceCurrency !== '' && $quoteCurrency !== '' && $priceCurrency !== $quoteCurrency) {
            if ($fx <= 0) {
                $fx = 1;
            }
            $line = $line->convert($quoteCurrency, $fx);
        }

        return $line->toMajor();
    }

    public function money(string $field = 'unit_price'): string
    {
        if ($field === 'unit_price') {
            $money = $this->unit_price; // Money VO
            if ($money instanceof \App\ValueObjects\Money) {
                return $money->format();
            }
        }
        $amount = (float) ($this->{$field} ?? 0);
        $quote = $this->quoteProduct?->quote;
        if (! $quote) {
            return number_format($amount, 2, '.', ',');
        }

        return $quote->currencyDto()->format($amount);
    }
}
