<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CustomerQuoteGroup extends Model
{
    use HasFactory;

    protected $table = 'customer_quote_groups';

    protected $fillable = [
        'customer_quote_id', 'name', 'notes', 'position',
        'quantity', 'unit_price_minor', 'price_currency', 'fx_rate_to_quote',
    ];

    protected function casts(): array
    {
        return [
            'unit_price' => \App\Casts\MoneyCast::class,
            'fx_rate_to_quote' => 'decimal:8',
        ];
    }

    protected $appends = [
        'has_fixed_price',
    ];

    public function quote(): BelongsTo
    {
        return $this->belongsTo(CustomerQuote::class, 'customer_quote_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(CustomerQuoteProduct::class, 'group_id');
    }

    public function getHasFixedPriceAttribute(): bool
    {
        return $this->isKitWithFixedPrice();
    }

    public function isKitWithFixedPrice(): bool
    {
        return ! is_null($this->unit_price_minor);
    }

    public function totalUnitsUniform(): ?int
    {
        if ($this->isKitWithFixedPrice()) {
            // Kit con precio fijo: cantidad de kits
            return $this->quantity;
        }

        // Kit con precios de componentes (lógica original)
        $totals = $this->products
            ->map(fn (CustomerQuoteProduct $gp) => (int) ($gp->variants?->sum('quantity') ?? 0))
            ->filter(fn ($v) => $v > 0)
            ->unique()
            ->values();

        return $totals->count() === 1 ? $totals->first() : null;
    }

    public function totalAmount(): float
    {
        if ($this->isKitWithFixedPrice()) {
            // Kit con precio fijo: cantidad × precio unitario
            $unitPrice = $this->unit_price; // Money VO
            if (! $unitPrice instanceof \App\ValueObjects\Money) {
                return 0.0;
            }
            $line = $unitPrice->multiply($this->quantity);

            // Conversión de moneda si es necesario
            $quoteCurrency = strtoupper($this->quote?->currency ?? '');
            $priceCurrency = strtoupper($unitPrice->currency);
            $fx = (float) ($this->fx_rate_to_quote ?? 1);

            if ($priceCurrency !== '' && $quoteCurrency !== '' && $priceCurrency !== $quoteCurrency) {
                if ($fx <= 0) {
                    $fx = 1;
                }
                $line = $line->convert($quoteCurrency, $fx);
            }

            return $line->toMajor();
        }

        // Kit con precios de componentes (lógica original)
        return (float) $this->products->sum(fn (CustomerQuoteProduct $p) => $p->totalAmount());
    }

    public function avgUnitPrice(): ?float
    {
        if ($this->isKitWithFixedPrice()) {
            // Kit con precio fijo: precio unitario del kit
            $unitPrice = $this->unit_price;
            if (! $unitPrice instanceof \App\ValueObjects\Money) {
                return null;
            }

            return $unitPrice->toMajor();
        }

        // Kit con precios de componentes (lógica original)
        $units = $this->totalUnitsUniform();
        if (! $units || $units <= 0) {
            return null;
        }

        return round($this->totalAmount() / $units, 2);
    }

    public function totalUnits(): int
    {
        if ($this->isKitWithFixedPrice()) {
            // Kit con precio fijo: cantidad de kits
            return $this->quantity;
        }

        // Kit con precios de componentes: suma de todas las variantes
        return $this->products->sum(fn ($p) => $p->totalUnits());
    }

    /**
     * Valida que la composición del kit sea correcta.
     * Verifica que la suma de cantidades de variantes coincida con:
     * quantity (kits) × units_per_kit (por producto)
     */
    public function validateKitComposition(): array
    {
        $errors = [];

        if (! $this->isKitWithFixedPrice()) {
            return $errors; // No validar si no es kit con precio fijo
        }

        foreach ($this->products as $product) {
            if (! $product->units_per_kit) {
                continue;
            }

            $expectedTotal = $this->quantity * $product->units_per_kit;
            $actualTotal = $product->variants->sum('quantity');

            if ($expectedTotal !== $actualTotal) {
                $errors[] = "Producto '{$product->name}': Esperado {$expectedTotal} unidades ({$this->quantity} kits × {$product->units_per_kit}), pero tiene {$actualTotal} unidades en variantes";
            }
        }

        return $errors;
    }

    /**
     * Verifica si la composición del kit es válida.
     */
    public function isValidKitComposition(): bool
    {
        return empty($this->validateKitComposition());
    }
}
