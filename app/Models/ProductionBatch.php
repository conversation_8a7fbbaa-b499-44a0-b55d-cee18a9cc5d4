<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductionBatch extends Model
{
    /** @use HasFactory<\Database\Factories\ProductionBatchFactory> */
    use HasFactory;

    protected $fillable = [
        'purchase_order_item_id',
        'quantity',
        'planned_start',
        'planned_finish',
        'status',
        'pool_state',
        'correlation_id',
        'notes',
    ];

    protected function casts(): array
    {
        return [
            'planned_start' => 'datetime',
            'planned_finish' => 'datetime',
        ];
    }

    public function purchaseOrderItem(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrderItem::class);
    }
}
