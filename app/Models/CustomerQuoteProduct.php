<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CustomerQuoteProduct extends Model
{
    use HasFactory;

    protected $table = 'customer_quote_products';

    protected $fillable = [
        'customer_quote_id', 'group_id', 'type_id', 'name', 'attributes', 'specs', 'hs_code', 'weight', 'volume',
        'type_code', 'type_name', 'subcategory_code', 'category_code',
        'notes', 'position', 'units_per_kit',
    ];

    protected function casts(): array
    {
        return [
            'attributes' => 'array',
            'specs' => 'array',
            'weight' => 'decimal:3',
            'volume' => 'decimal:3',
        ];
    }

    public function quote(): BelongsTo
    {
        return $this->belongsTo(CustomerQuote::class, 'customer_quote_id');
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(CustomerQuoteGroup::class, 'group_id');
    }

    // product() relationship removed; using taxonomy + local fields

    public function variants(): HasMany
    {
        return $this->hasMany(CustomerQuoteProductVariant::class, 'quote_product_id');
    }

    public function totalUnits(): int
    {
        return (int) ($this->variants?->sum('quantity') ?? 0);
    }

    public function totalAmount(): float
    {
        return (float) $this->variants->sum(fn (CustomerQuoteProductVariant $v) => $v->totalAmount());
    }

    public function avgUnitPrice(): ?float
    {
        $qty = $this->totalUnits();
        if ($qty <= 0) {
            return null;
        }

        return round($this->totalAmount() / $qty, 2);
    }

    public function isPartOfKitWithFixedPrice(): bool
    {
        return $this->group && $this->group->isKitWithFixedPrice();
    }

    public function getUnitsPerKit(): ?int
    {
        if (! $this->isPartOfKitWithFixedPrice()) {
            return null;
        }

        return $this->units_per_kit;
    }
}
