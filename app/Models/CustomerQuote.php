<?php

namespace App\Models;

use App\DTOs\Currency;
use App\DTOs\Tax;
use App\Enums\CustomerQuoteStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Cache;

class CustomerQuote extends Model
{
    use HasFactory;

    protected static function booted(): void
    {
        static::saved(function (CustomerQuote $quote) {
            $quote->clearAmountCache();
        });

        static::deleted(function (CustomerQuote $quote) {
            $quote->clearAmountCache();
        });
    }

    protected $table = 'customer_quotes';

    protected $fillable = [
        'quote_number',
        'customer_id', 'customer_name',
        'project_id', 'currency', 'country', 'valid_until', 'status', 'notes',
    ];

    protected function casts(): array
    {
        return [
            'status' => CustomerQuoteStatus::class,
            'valid_until' => 'date',
        ];
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function groups(): HasMany
    {
        return $this->hasMany(CustomerQuoteGroup::class, 'customer_quote_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(CustomerQuoteProduct::class, 'customer_quote_id');
    }

    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(PurchaseOrder::class, 'quote_id');
    }

    public function currencyDto(): Currency
    {
        return Currency::from($this->currency ?? 'USD');
    }

    public function taxRate(): float
    {
        return Tax::rateFor($this->country ?? null);
    }

    public function subtotalAmount(): float
    {
        return Cache::remember(
            "customer_quote_{$this->id}_subtotal_{$this->updated_at->timestamp}",
            now()->addMinutes(30),
            function () {
                $sum = 0.0;
                $products = $this->relationLoaded('products')
                    ? $this->products
                    : $this->products()->with('variants')->get();

                foreach ($products as $product) {
                    foreach ($product->variants as $variant) {
                        $sum += $variant->totalAmount();
                    }
                }

                return $sum;
            }
        );
    }

    public function taxAmount(): float
    {
        return Cache::remember(
            "customer_quote_{$this->id}_tax_{$this->updated_at->timestamp}",
            now()->addMinutes(30),
            fn () => round($this->subtotalAmount() * $this->taxRate(), 2)
        );
    }

    public function totalAmount(): float
    {
        return Cache::remember(
            "customer_quote_{$this->id}_total_{$this->updated_at->timestamp}",
            now()->addMinutes(30),
            fn () => round($this->subtotalAmount() + $this->taxAmount(), 2)
        );
    }

    public function money(float $amount): string
    {
        return $this->currencyDto()->format($amount);
    }

    /**
     * Obtener el nombre del cliente
     */
    public function getCustomerNameAttribute(): string
    {
        return $this->customer?->name ?? $this->attributes['customer_name'] ?? 'Cliente no especificado';
    }

    /**
     * Obtener el nombre completo del cliente (nombre + empresa)
     */
    public function getCustomerFullNameAttribute(): string
    {
        if ($this->customer) {
            return $this->customer->full_name;
        }

        return $this->customer_name;
    }

    /**
     * Obtener el país del cliente
     */
    public function getCustomerCountryAttribute(): ?string
    {
        return $this->customer?->country ?? $this->country;
    }

    /**
     * Scope para filtrar por cliente
     */
    public function scopeForCustomer(Builder $query, int $customerId): Builder
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope para cotizaciones con cliente asociado
     */
    public function scopeWithCustomer(Builder $query): Builder
    {
        return $query->whereNotNull('customer_id');
    }

    /**
     * Scope para cotizaciones sin cliente asociado
     */
    public function scopeWithoutCustomer(Builder $query): Builder
    {
        return $query->whereNull('customer_id');
    }

    /**
     * Clear cached amount calculations
     */
    public function clearAmountCache(): void
    {
        $timestamp = $this->updated_at?->timestamp ?? now()->timestamp;

        Cache::forget("customer_quote_{$this->id}_subtotal_{$timestamp}");
        Cache::forget("customer_quote_{$this->id}_tax_{$timestamp}");
        Cache::forget("customer_quote_{$this->id}_total_{$timestamp}");

        // Also clear with current timestamp in case of immediate recalculation
        $currentTimestamp = now()->timestamp;
        if ($timestamp !== $currentTimestamp) {
            Cache::forget("customer_quote_{$this->id}_subtotal_{$currentTimestamp}");
            Cache::forget("customer_quote_{$this->id}_tax_{$currentTimestamp}");
            Cache::forget("customer_quote_{$this->id}_total_{$currentTimestamp}");
        }
    }
}
