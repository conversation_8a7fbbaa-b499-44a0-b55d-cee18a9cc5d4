<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory;

    protected $table = 'customers';

    protected $fillable = [
        'name',
        'email',
        'phone',
        'company',
        'tax_id',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'contact_person',
        'notes',
        'metadata',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'metadata' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relación con las cotizaciones del cliente
     */
    public function quotes(): HasMany
    {
        return $this->hasMany(CustomerQuote::class, 'customer_id');
    }

    /**
     * Obtener el nombre completo del cliente (nombre + empresa si existe)
     */
    protected function fullName(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->company
                ? "{$this->name} ({$this->company})"
                : $this->name,
        );
    }

    /**
     * Obtener la dirección completa formateada
     */
    protected function fullAddress(): Attribute
    {
        return Attribute::make(
            get: fn () => implode(', ', array_filter([
                $this->address,
                $this->city,
                $this->state,
                $this->postal_code,
                $this->country,
            ])),
        );
    }

    /**
     * Scope para clientes activos
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope para buscar por nombre o empresa
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('company', 'like', "%{$search}%")
                ->orWhere('email', 'like', "%{$search}%");
        });
    }

    /**
     * Scope para filtrar por país
     */
    public function scopeByCountry(Builder $query, string $country): Builder
    {
        return $query->where('country', $country);
    }

    /**
     * Verificar si el cliente tiene cotizaciones
     */
    public function hasQuotes(): bool
    {
        return $this->quotes()->exists();
    }

    /**
     * Obtener el total de cotizaciones del cliente
     */
    protected function quotesCount(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->quotes()->count(),
        );
    }

    /**
     * Obtener la última cotización del cliente
     */
    protected function lastQuote(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->quotes()->latest()->first(),
        );
    }
}
