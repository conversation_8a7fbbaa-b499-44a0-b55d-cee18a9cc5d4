<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductType extends Model
{
    use HasFactory;

    protected $table = 'product_types';

    protected $fillable = ['subcategory_id', 'code', 'name', 'hs_code_sugerido', 'defaults', 'reglas'];

    protected function casts(): array
    {
        return [
            'defaults' => 'array',
            'reglas' => 'array',
        ];
    }

    public function subcategory(): BelongsTo
    {
        return $this->belongsTo(ProductSubcategory::class, 'subcategory_id');
    }
}
