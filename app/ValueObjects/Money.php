<?php

namespace App\ValueObjects;

use App\DTOs\Currency;

final readonly class Money
{
    public function __construct(
        public int $amountMinor,
        public string $currency,
    ) {}

    public static function fromMajor(float|int $amount, string $currency): self
    {
        $c = Currency::from($currency);
        $minor = (int) round((float) $amount * (10 ** $c->decimals));
        return new self($minor, strtoupper($c->code));
    }

    public function toMajor(): float
    {
        $c = Currency::from($this->currency);
        return $this->amountMinor / (10 ** $c->decimals);
    }

    public function format(?Currency $currency = null): string
    {
        $c = $currency ?: Currency::from($this->currency);
        return $c->format($this->toMajor());
    }

    public function add(self $other): self
    {
        $this->assertSameCurrency($other);
        return new self($this->amountMinor + $other->amountMinor, $this->currency);
    }

    public function multiply(int|float $factor): self
    {
        $major = $this->toMajor() * (float) $factor;
        return self::fromMajor($major, $this->currency);
    }

    public function convert(string $toCurrency, float $rate): self
    {
        $major = $this->toMajor();
        $converted = $major * $rate;
        return self::fromMajor($converted, $toCurrency);
    }

    private function assertSameCurrency(self $other): void
    {
        if (strtoupper($this->currency) !== strtoupper($other->currency)) {
            throw new \InvalidArgumentException('Currency mismatch');
        }
    }
}

