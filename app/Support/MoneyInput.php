<?php

namespace App\Support;

use App\DTOs\Currency;

class MoneyInput
{
    /**
     * Parse a human input amount (major units) into minor units (int) for the given currency.
     * Accepts formats like "1,234.56", "1.234,56", "2500", "2.500" (CLP), etc.
     */
    public static function parseToMinor(string|int|float $value, string $currencyCode): ?int
    {
        $code = strtoupper((string) $currencyCode);
        $c = Currency::from($code);
        $decimals = $c->decimals;

        if (is_int($value)) {
            // Treat as major units integer (e.g., 2500 CLP) → convert to minor
            return (int) ($value * (10 ** $decimals));
        }

        if (is_float($value)) {
            $major = (float) $value;
            return (int) round($major * (10 ** $decimals));
        }

        $str = trim((string) $value);
        if ($str === '') {
            return null;
        }

        // Remove spaces and currency symbols
        $str = preg_replace('/\s+/u', '', $str);
        $str = preg_replace('/[\p{Sc}]/u', '', $str); // currency symbols

        $hasDot = str_contains($str, '.');
        $hasComma = str_contains($str, ',');

        $normalized = $str;
        if ($hasDot && $hasComma) {
            // Assume the right-most separator is the decimal separator, the other is thousands
            $lastDot = strrpos($str, '.');
            $lastComma = strrpos($str, ',');
            $decimalSep = $lastDot > $lastComma ? '.' : ',';
            $thousandSep = $decimalSep === '.' ? ',' : '.';
            $normalized = str_replace($thousandSep, '', $str);
            $normalized = str_replace($decimalSep, '.', $normalized);
        } elseif ($hasDot || $hasComma) {
            $sep = $hasDot ? '.' : ',';
            // If currency has 0 decimals, treat any separator as thousands
            if ($decimals === 0) {
                $normalized = str_replace($sep, '', $str);
            } else {
                // Decide if separator is decimal by inspecting trailing digits
                $parts = explode($sep, $str);
                if (count($parts) === 2 && strlen($parts[1]) <= max(3, $decimals)) {
                    // likely decimal
                    $normalized = $parts[0] . '.' . $parts[1];
                } else {
                    // likely thousands
                    $normalized = str_replace($sep, '', $str);
                }
            }
        } else {
            // Digits only
            $normalized = $str;
        }

        // Now, $normalized should be digits with optional single '.' as decimal point
        if (!preg_match('/^-?\d+(?:\.\d+)?$/', $normalized)) {
            return null;
        }

        $major = (float) $normalized;
        // Enforce max decimals
        if ($decimals === 0 && fmod($major, 1.0) !== 0.0) {
            return null; // no decimals allowed
        }

        $scale = 10 ** $decimals;
        return (int) round($major * $scale);
    }
}

