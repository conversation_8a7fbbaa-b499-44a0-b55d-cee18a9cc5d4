<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidContactField implements ValidationRule
{
    /**
     * Campos de contacto permitidos
     */
    private array $allowedFields = [
        'email',
        'phone',
        'mobile',
        'address',
        'website',
        'contact_person',
        'whatsapp',
        'linkedin',
        'twitter',
        'facebook',
        'instagram',
        'fax',
        'city',
        'state',
        'country',
        'postal_code',
        'timezone',
        'language',
    ];

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_string($value)) {
            $fail('El campo debe ser una cadena de texto.');

            return;
        }

        // Convertir a minúsculas para comparación
        $fieldName = strtolower(trim($value));

        // Verificar si el campo está en la lista permitida
        if (! in_array($fieldName, $this->allowedFields)) {
            $allowedFieldsList = implode(', ', $this->allowedFields);
            $fail("El campo '{$value}' no está permitido. Campos válidos: {$allowedFieldsList}");
        }

        // Verificar formato básico (solo letras, números y guiones bajos)
        if (! preg_match('/^[a-z_]+$/', $fieldName)) {
            $fail('El nombre del campo solo puede contener letras minúsculas y guiones bajos.');
        }

        // Verificar longitud
        if (strlen($fieldName) < 2) {
            $fail('El nombre del campo debe tener al menos 2 caracteres.');
        }

        if (strlen($fieldName) > 30) {
            $fail('El nombre del campo no puede exceder 30 caracteres.');
        }
    }
}
