<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidContactValue implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_string($value)) {
            $fail('El valor debe ser una cadena de texto.');

            return;
        }

        $value = trim($value);

        // Verificar que no esté vacío
        if (empty($value)) {
            $fail('El valor no puede estar vacío.');

            return;
        }

        // Verificar longitud máxima
        if (strlen($value) > 255) {
            $fail('El valor no puede exceder 255 caracteres.');
        }

        // Obtener el nombre del campo desde el atributo
        $fieldName = $this->extractFieldName($attribute);

        // Validaciones específicas por tipo de campo
        switch ($fieldName) {
            case 'email':
                if (! filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $fail('El email no tiene un formato válido.');
                }
                break;

            case 'phone':
            case 'mobile':
            case 'whatsapp':
                if (! preg_match('/^[\+]?[0-9\s\-\(\)]{7,20}$/', $value)) {
                    $fail('El número de teléfono no tiene un formato válido.');
                }
                break;

            case 'website':
                if (! filter_var($value, FILTER_VALIDATE_URL) && ! preg_match('/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/', $value)) {
                    $fail('El sitio web no tiene un formato válido.');
                }
                break;

            case 'linkedin':
            case 'twitter':
            case 'facebook':
            case 'instagram':
                if (! filter_var($value, FILTER_VALIDATE_URL) && ! preg_match('/^@?[a-zA-Z0-9_]{1,30}$/', $value)) {
                    $fail('El perfil de red social no tiene un formato válido.');
                }
                break;

            case 'postal_code':
                if (! preg_match('/^[0-9]{4,10}$/', $value)) {
                    $fail('El código postal debe contener entre 4 y 10 dígitos.');
                }
                break;
        }
    }

    /**
     * Extraer el nombre del campo desde el atributo
     */
    private function extractFieldName(string $attribute): ?string
    {
        // El atributo viene como "contact.email", "contact.phone", etc.
        $parts = explode('.', $attribute);

        return $parts[1] ?? null;
    }
}
