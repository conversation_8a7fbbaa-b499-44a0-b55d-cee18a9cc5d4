<?php

namespace App\Filament\Pages;

use App\Models\CustomerQuote;
use BackedEnum;
use App\Models\ProductType;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Schemas\Concerns\InteractsWithSchemas;
use Filament\Schemas\Schema;

class QuotePlanMenuPage extends Page
{
    use InteractsWithForms, InteractsWithSchemas;
    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-squares-2x2';
    
    protected string $view = 'filament.pages.quote-plan-menu';
    
    protected static ?string $navigationLabel = 'Plan de Cotización';
    
    protected static ?string $title = 'Plan de Cotización';
    
    protected static ?int $navigationSort = 4;

    public ?int $selectedQuoteId = null;
    public ?CustomerQuote $selectedQuote = null;
    public bool $showDetails = true;
    public bool $showVariantsOnly = false;
    public bool $attentionOnly = false;
    public ?string $filterCurrency = null;
    public array $variantRequiredKeys = [];

    public function mount(): void
    {
        // Cargar la primera cotización por defecto si existe
        $firstQuote = CustomerQuote::first();
        if ($firstQuote) {
            $this->selectedQuoteId = $firstQuote->id;
            $this->selectedQuote = $firstQuote;
        }
        $this->loadVariantRequiredKeys();
    }

    public function loadVariantRequiredKeys(): void
    {
        $this->variantRequiredKeys = ProductType::with('requiredAttributes')
            ->get()
            ->pluck('requiredAttributes', 'id')
            ->map(fn($attrs) => $attrs->pluck('key')->toArray())
            ->toArray();
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Select::make('selectedQuoteId')
                    ->label('Seleccionar Cotización')
                    ->options(CustomerQuote::pluck('quote_number', 'id'))
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        if ($state) {
                            $this->selectedQuote = CustomerQuote::find($state);
                        }
                    })
                    ->required(),
                
                Toggle::make('showDetails')
                    ->label('Mostrar detalles')
                    ->live(),
                
                Toggle::make('showVariantsOnly')
                    ->label('Solo variantes')
                    ->live(),
                
                Toggle::make('attentionOnly')
                    ->label('Solo atención')
                    ->live(),
            ])
            ->statePath('data');
    }

    public function getKpis(): array
    {
        if (!$this->selectedQuote) {
            return [
                'total_groups' => 0,
                'total_products' => 0,
                'total_variants' => 0,
                'total_value' => 0,
                'missing_attributes' => 0,
            ];
        }

        $quote = $this->selectedQuote;
        
        $totalGroups = $quote->groups()->count();
        $totalProducts = $quote->groups()
            ->withCount('products')
            ->get()
            ->sum('products_count');
        $totalVariants = $quote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->count();

        $totalValue = $quote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->sum(fn($variant) => $variant->quantity * $variant->unit_price);

        $missingAttributes = $this->getVariantRows()
            ->filter(fn($variant) => $this->missingRequiredForVariant($variant['id']))
            ->count();

        return [
            'total_groups' => $totalGroups,
            'total_products' => $totalProducts,
            'total_variants' => $totalVariants,
            'total_value' => $totalValue,
            'missing_attributes' => $missingAttributes,
        ];
    }

    public function getCurrencies(): array
    {
        if (!$this->selectedQuote) return [];
        
        return $this->selectedQuote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->pluck('currency')
            ->unique()
            ->mapWithKeys(fn($currency) => [$currency => $currency])
            ->toArray();
    }

    public function missingRequiredForVariant(int $variantId): bool
    {
        if (!$this->selectedQuote) return false;
        
        $variant = $this->selectedQuote->groups()
            ->with('products.variants.productType.requiredAttributes')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->firstWhere('id', $variantId);

        if (!$variant || !$variant->productType) {
            return false;
        }

        $requiredKeys = $this->variantRequiredKeys[$variant->productType->id] ?? [];
        $variantAttributes = $variant->attributes ?? [];

        foreach ($requiredKeys as $key) {
            if (!isset($variantAttributes[$key]) || empty($variantAttributes[$key])) {
                return true;
            }
        }

        return false;
    }

    public function getVariantRows(): \Illuminate\Support\Collection
    {
        if (!$this->selectedQuote) return collect();
        
        $query = $this->selectedQuote->groups()
            ->with(['products.variants.productType']);

        if ($this->filterCurrency) {
            $query->whereHas('products.variants', function ($q) {
                $q->where('currency', $this->filterCurrency);
            });
        }

        $variants = $query->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants->map(fn($variant) => [
                'id' => $variant->id,
                'group_name' => $group->name,
                'product_name' => $product->name,
                'variant_name' => $variant->name,
                'quantity' => $variant->quantity,
                'unit_price' => $variant->unit_price,
                'total_price' => $variant->quantity * $variant->unit_price,
                'currency' => $variant->currency,
                'product_type' => $variant->productType?->name,
                'missing_attributes' => $this->missingRequiredForVariant($variant->id),
                'attributes' => $variant->attributes ?? [],
            ]));

        if ($this->attentionOnly) {
            $variants = $variants->filter(fn($variant) => $variant['missing_attributes']);
        }

        return $variants;
    }

    public function getGroupRows(): \Illuminate\Support\Collection
    {
        if (!$this->selectedQuote) return collect();
        
        return $this->selectedQuote->groups()
            ->with(['products.variants'])
            ->get()
            ->map(function ($group) {
                $subtotal = $group->products
                    ->flatMap(fn($product) => $product->variants)
                    ->sum(fn($variant) => $variant->quantity * $variant->unit_price);

                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'products_count' => $group->products->count(),
                    'variants_count' => $group->products->flatMap(fn($product) => $product->variants)->count(),
                    'subtotal' => $subtotal,
                ];
            });
    }

    public function getRootProductRows(): \Illuminate\Support\Collection
    {
        if (!$this->selectedQuote) return collect();
        
        return $this->selectedQuote->groups()
            ->with(['products.variants'])
            ->get()
            ->flatMap(fn($group) => $group->products->map(fn($product) => [
                'id' => $product->id,
                'group_name' => $group->name,
                'name' => $product->name,
                'variants_count' => $product->variants->count(),
                'subtotal' => $product->variants->sum(fn($variant) => $variant->quantity * $variant->unit_price),
            ]));
    }
}
