<?php

namespace App\Filament\Pages;

use App\Models\Customer;
use App\Models\CustomerQuote;
use App\Models\ProductType;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Pages\Page;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Support\Icons\Heroicon;
use Filament\Schemas\Components\Wizard;
use Filament\Schemas\Components\Wizard\Step;
use Filament\Schemas\Schema;
use Livewire\Attributes\Layout;

#[Layout('filament-panels::components.layout.index')]
class QuoteWizard extends Page implements HasActions
{
    use InteractsWithActions, InteractsWithForms;

    protected string $view = 'filament.pages.quote-wizard';

    protected static ?string $navigationLabel = 'Nueva Cotización (Wizard)';

    protected static ?string $title = 'Crear Cotización';

    // Datos del wizard
    public $customerId = null;

    public $customerName = '';

    public $country = '';

    public $quoteNumber = '';

    public $validUntil = '';

    public $notes = '';

    public $selectedProducts = [];

    public $productVariants = [];

    // Datos para kits y productos individuales
    public $newKits = [];
    public $existingKits = [];
    public $newProducts = [];
    public $existingProducts = [];

    // Datos para crear nuevo producto
    public $newProductName = '';

    public $newProductCode = '';

    public $newProductDescription = '';

    public $newProductCategoryId = null;

    public $newProductSubcategoryId = null;

    public function mount(): void
    {
        $this->quoteNumber = 'COT-'.str_pad(CustomerQuote::count() + 1, 4, '0', STR_PAD_LEFT);
        $this->validUntil = now()->addDays(30)->format('Y-m-d');
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Wizard::make([
                    Step::make('Cliente')
                        ->description('Selecciona o crea un cliente para la cotización')
                        ->icon(Heroicon::User)
                        ->schema([
                            Select::make('customerId')
                                ->label('Cliente')
                                ->options($this->getCustomers())
                                ->searchable()
                                ->live()
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if ($state) {
                                        $customer = Customer::find($state);
                                        if ($customer) {
                                            $set('customerName', $customer->name);
                                            $set('country', $customer->country);
                                        }
                                    }
                                }),

                            TextInput::make('customerName')
                                ->label('Nombre del Cliente')
                                ->disabled(),

                            TextInput::make('country')
                                ->label('País')
                                ->disabled(),
                        ]),

                    Step::make('Configuración')
                        ->description('Define la información básica de la cotización')
                        ->icon(Heroicon::Cog6Tooth)
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('quoteNumber')
                                        ->label('Número de Cotización')
                                        ->required(),

                                    DatePicker::make('validUntil')
                                        ->label('Válida hasta')
                                        ->required(),
                                ]),

                            Textarea::make('notes')
                                ->label('Notas')
                                ->placeholder('Notas adicionales para la cotización...')
                                ->rows(3)
                                ->columnSpanFull(),
                        ]),

                    Step::make('Productos y Variantes')
                        ->description('Agrega kits y productos a la cotización')
                        ->icon(Heroicon::ShoppingBag)
                        ->schema([
                            // Opciones principales simplificadas
                            Section::make('Selecciona una opción')
                                ->description('Elige si quieres agregar un kit o un producto individual a tu cotización')
                                ->schema([
                                    Grid::make([
                                        'lg' => 2,
                                    ])
                                        ->schema([
                                            // Agregar Kit
                                            Section::make('Agregar Kit')
                                                ->description('Agrega un kit existente o crea uno nuevo con productos y variantes')
                                                ->extraAttributes(['class' => 'text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors'])
                                                ->columnSpanFull(),
                                            
                                            // Agregar Producto
                                            Section::make('Agregar Producto')
                                                ->description('Agrega un producto existente o crea uno nuevo con sus variantes')
                                                ->extraAttributes(['class' => 'text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors'])
                                                ->columnSpanFull(),
                                        ]),
                                ])
                                ->columnSpanFull(),
                                
                            // Agregar Kits
                            Repeater::make('kits')
                                ->label('Kits')
                                ->schema([
                                    Select::make('kitId')
                                        ->label('Kit')
                                        ->options($this->getExistingKits())
                                        ->searchable()
                                        ->preload()
                                        ->required()
                                        ->live()
                                        ->afterStateUpdated(function ($state, callable $set) {
                                            if ($state) {
                                                $kit = \App\Models\CustomerQuoteGroup::find($state);
                                                if ($kit) {
                                                    $set('name', $kit->name);
                                                    $set('description', $kit->description);
                                                }
                                            }
                                        })
                                        ->createOptionForm([
                                            TextInput::make('name')
                                                ->label('Nombre del Kit')
                                                ->placeholder('Ej: Kit de Bienvenida Ejecutivo')
                                                ->required()
                                                ->maxLength(255),
                                                
                                            TextInput::make('description')
                                                ->label('Descripción del Kit')
                                                ->placeholder('Descripción opcional del kit...')
                                                ->maxLength(500),
                                        ])
                                        ->createOptionUsing(function (array $data): int {
                                            $kit = \App\Models\CustomerQuoteGroup::create([
                                                'name' => $data['name'],
                                                'description' => $data['description'],
                                            ]);
                                            return $kit->id;
                                        }),
                                    
                                    TextInput::make('name')
                                        ->label('Nombre del Kit')
                                        ->disabled()
                                        ->dehydrated(false),
                                        
                                    TextInput::make('description')
                                        ->label('Descripción del Kit')
                                        ->disabled()
                                        ->dehydrated(false),
                                    
                                    TextInput::make('quantity')
                                        ->label('Cantidad de Kits')
                                        ->numeric()
                                        ->default(1)
                                        ->minValue(1)
                                        ->required()
                                        ->helperText('Cantidad de este kit a agregar a la cotización'),
                                    
                                    Repeater::make('products')
                                        ->label('Productos del Kit')
                                        ->schema([
                                            Select::make('productId')
                                                ->label('Producto')
                                                ->options($this->getProductTypes()->mapWithKeys(fn ($product) => [
                                                    $product->id => $product->name.' ('.$product->code.')',
                                                ]))
                                                ->searchable()
                                                ->preload()
                                                ->required()
                                                ->createOptionForm([
                                                    TextInput::make('name')
                                                        ->label('Nombre del Producto')
                                                        ->placeholder('Ej: Gorra Corporativa')
                                                        ->required()
                                                        ->maxLength(255),
                                                        
                                                    TextInput::make('code')
                                                        ->label('Código del Producto')
                                                        ->placeholder('Ej: GORRA-001')
                                                        ->required()
                                                        ->maxLength(50),
                                                        
                                                    Textarea::make('description')
                                                        ->label('Descripción')
                                                        ->placeholder('Descripción del producto...')
                                                        ->rows(2)
                                                        ->maxLength(1000),
                                                        
                                                    Select::make('categoryId')
                                                        ->label('Categoría')
                                                        ->options($this->getCategories())
                                                        ->required()
                                                        ->live()
                                                        ->afterStateUpdated(function ($state, callable $set) {
                                                            $set('subcategoryId', null);
                                                        }),
                                                        
                                                    Select::make('subcategoryId')
                                                        ->label('Subcategoría')
                                                        ->options(function (callable $get) {
                                                            $categoryId = $get('categoryId');
                                                            if (!$categoryId) {
                                                                return [];
                                                            }
                                                            return $this->getSubcategories($categoryId);
                                                        })
                                                        ->required(),
                                                ])
                                                ->createOptionUsing(function (array $data): int {
                                                    $product = \App\Models\ProductType::create([
                                                        'name' => $data['name'],
                                                        'code' => $data['code'],
                                                        'description' => $data['description'],
                                                        'category_id' => $data['categoryId'],
                                                        'subcategory_id' => $data['subcategoryId'],
                                                    ]);
                                                    return $product->id;
                                                }),
                                            
                                            TextInput::make('unitsPerKit')
                                                ->label('Unidades por Kit')
                                                ->numeric()
                                                ->default(1)
                                                ->minValue(1)
                                                ->required()
                                                ->helperText('Cantidad de este producto que va en cada kit'),
                                            
                                            Repeater::make('variants')
                                                ->label('Variantes del Producto')
                                                ->schema([
                                                    Grid::make(3)
                                                        ->schema([
                                                            TextInput::make('name')
                                                                ->label('Nombre de Variante')
                                                                ->placeholder('Ej: Rojo, Grande, Premium')
                                                                ->required(),
                                                                
                                                            TextInput::make('quantity')
                                                                ->label('Cantidad de Variante')
                                                                ->numeric()
                                                                ->default(1)
                                                                ->minValue(1)
                                                                ->required(),
                                                                
                                                            TextInput::make('specifications')
                                                                ->label('Especificaciones')
                                                                ->placeholder('Color, tamaño, material, etc.'),
                                                        ]),
                                                        
                                                    Textarea::make('notes')
                                                        ->label('Notas')
                                                        ->placeholder('Notas adicionales para esta variante...')
                                                        ->rows(2)
                                                        ->columnSpanFull(),
                                                ])
                                                ->addActionLabel('Agregar Variante')
                                                ->collapsible()
                                                ->cloneable()
                                                ->defaultItems(1)
                                                ->minItems(1)
                                                ->columnSpanFull(),
                                        ])
                                        ->addActionLabel('Agregar Producto al Kit')
                                        ->collapsible()
                                        ->cloneable()
                                        ->defaultItems(1)
                                        ->minItems(1)
                                        ->columnSpanFull(),
                                ])
                                ->addActionLabel('Agregar Kit')
                                ->collapsible()
                                ->cloneable()
                                ->defaultItems(0)
                                ->columnSpanFull(),
                                
                            // Agregar Productos Individuales
                            Repeater::make('products')
                                ->label('Productos Individuales')
                                ->schema([
                                    Select::make('productId')
                                        ->label('Producto')
                                        ->options($this->getProductTypes()->mapWithKeys(fn ($product) => [
                                            $product->id => $product->name.' ('.$product->code.')',
                                        ]))
                                        ->searchable()
                                        ->preload()
                                        ->required()
                                        ->live()
                                        ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                            if ($state) {
                                                $set('variants', []);
                                            }
                                        })
                                        ->createOptionForm([
                                            TextInput::make('name')
                                                ->label('Nombre del Producto')
                                                ->placeholder('Ej: Gorra Corporativa')
                                                ->required()
                                                ->maxLength(255),
                                                
                                            TextInput::make('code')
                                                ->label('Código del Producto')
                                                ->placeholder('Ej: GORRA-001')
                                                ->required()
                                                ->maxLength(50),
                                                
                                            Textarea::make('description')
                                                ->label('Descripción')
                                                ->placeholder('Descripción del producto...')
                                                ->rows(2)
                                                ->maxLength(1000),
                                                
                                            Select::make('categoryId')
                                                ->label('Categoría')
                                                ->options($this->getCategories())
                                                ->required()
                                                ->live()
                                                ->afterStateUpdated(function ($state, callable $set) {
                                                    $set('subcategoryId', null);
                                                }),
                                                
                                            Select::make('subcategoryId')
                                                ->label('Subcategoría')
                                                ->options(function (callable $get) {
                                                    $categoryId = $get('categoryId');
                                                    if (!$categoryId) {
                                                        return [];
                                                    }
                                                    return $this->getSubcategories($categoryId);
                                                })
                                                ->required(),
                                        ])
                                        ->createOptionUsing(function (array $data): int {
                                            $product = \App\Models\ProductType::create([
                                                'name' => $data['name'],
                                                'code' => $data['code'],
                                                'description' => $data['description'],
                                                'category_id' => $data['categoryId'],
                                                'subcategory_id' => $data['subcategoryId'],
                                            ]);
                                            return $product->id;
                                        }),
                                    
                                    Repeater::make('variants')
                                        ->label('Variantes')
                                        ->schema([
                                            Grid::make(3)
                                                ->schema([
                                                    TextInput::make('name')
                                                        ->label('Nombre de Variante')
                                                        ->placeholder('Ej: Rojo, Grande, Premium')
                                                        ->required(),
                                                        
                                                    TextInput::make('quantity')
                                                        ->label('Cantidad')
                                                        ->numeric()
                                                        ->default(1)
                                                        ->minValue(1)
                                                        ->required(),
                                                        
                                                    TextInput::make('specifications')
                                                        ->label('Especificaciones')
                                                        ->placeholder('Color, tamaño, material, etc.'),
                                                ]),
                                                
                                            Textarea::make('notes')
                                                ->label('Notas')
                                                ->placeholder('Notas adicionales para esta variante...')
                                                ->rows(2)
                                                ->columnSpanFull(),
                                        ])
                                        ->addActionLabel('Agregar Variante')
                                        ->collapsible()
                                        ->cloneable()
                                        ->defaultItems(1)
                                        ->minItems(1)
                                        ->columnSpanFull(),
                                ])
                                ->addActionLabel('Agregar Producto')
                                ->collapsible()
                                ->cloneable()
                                ->defaultItems(0)
                                ->columnSpanFull(),
                        ]),
                ])
                    ->persistStepInQueryString()
                    ->skippable()
                    ->submitAction(view('filament.forms.components.wizard-submit-button')),
            ]);
    }

    public function getCustomers()
    {
        return Customer::where('is_active', true)
            ->orderBy('name')
            ->get()
            ->mapWithKeys(fn (Customer $customer) => [
                $customer->id => $customer->name,
            ]);
    }

    public function getProductTypes()
    {
        return ProductType::with(['subcategory.category'])
            ->orderBy('name')
            ->limit(20)
            ->get();
    }

    public function getSelectedCustomer()
    {
        if ($this->customerId) {
            return Customer::find($this->customerId);
        }

        return null;
    }

    public function getProductById($productId)
    {
        return ProductType::find($productId);
    }

    public function createNewProductAction(): Action
    {
        return Action::make('createNewProduct')
            ->label('Crear Nuevo Producto')
            ->icon('heroicon-o-plus')
            ->color('success')
            ->form([
                TextInput::make('newProductName')
                    ->label('Nombre del Producto')
                    ->required()
                    ->maxLength(255),

                TextInput::make('newProductCode')
                    ->label('Código del Producto')
                    ->required()
                    ->maxLength(50)
                    ->unique('product_types', 'code'),

                Textarea::make('newProductDescription')
                    ->label('Descripción')
                    ->rows(3)
                    ->maxLength(1000),

                Select::make('newProductCategoryId')
                    ->label('Categoría')
                    ->options($this->getCategories())
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state, callable $set) {
                        $set('newProductSubcategoryId', null);
                    }),

                Select::make('newProductSubcategoryId')
                    ->label('Subcategoría')
                    ->options(function (callable $get) {
                        $categoryId = $get('newProductCategoryId');
                        if (! $categoryId) {
                            return [];
                        }

                        return $this->getSubcategories($categoryId);
                    })
                    ->required(),
            ])
            ->action(function (array $data) {
                // Crear el nuevo producto
                $product = ProductType::create([
                    'name' => $data['newProductName'],
                    'code' => $data['newProductCode'],
                    'description' => $data['newProductDescription'],
                    'product_subcategory_id' => $data['newProductSubcategoryId'],
                ]);

                // Limpiar el formulario
                $this->newProductName = '';
                $this->newProductCode = '';
                $this->newProductDescription = '';
                $this->newProductCategoryId = null;
                $this->newProductSubcategoryId = null;

                // Mostrar mensaje de éxito
                $this->dispatch('product-created', [
                    'message' => "Producto '{$product->name}' creado exitosamente",
                    'productId' => $product->id,
                ]);
            })
            ->modalWidth('lg')
            ->modalHeading('Crear Nuevo Producto')
            ->modalDescription('Agrega un nuevo producto al catálogo para incluirlo en esta cotización')
            ->modalSubmitActionLabel('Crear Producto')
            ->modalCancelActionLabel('Cancelar');
    }

    public function createNewProduct(): void
    {
        $this->createNewProductAction()->call();
    }

    public function getCategories()
    {
        return \App\Models\ProductCategory::orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    public function getSubcategories($categoryId)
    {
        return \App\Models\ProductSubcategory::where('category_id', $categoryId)
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }
    
    public function getExistingKits()
    {
        return \App\Models\CustomerQuoteGroup::orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }
}
