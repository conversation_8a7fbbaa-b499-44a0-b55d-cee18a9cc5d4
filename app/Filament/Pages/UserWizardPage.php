<?php

namespace App\Filament\Pages;

use App\Models\User;
use BackedEnum;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Pages\Page;
use Filament\Schemas\Components\Wizard;
use Filament\Schemas\Components\Wizard\Step;
use Filament\Schemas\Concerns\InteractsWithSchemas;
use Filament\Schemas\Schema;
use Filament\Support\Exceptions\Halt;
use Filament\Support\Icons\Heroicon;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

class UserWizardPage extends Page
{
    use InteractsWithSchemas;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedUserPlus;

    protected string $view = 'filament.pages.user-wizard';

    protected static ?string $navigationLabel = 'Crear Usuario (Wizard)';

    protected static ?string $title = 'Crear Usuario - Asistente';

    protected static ?int $navigationSort = 2;

    public ?array $data = [];

    public function mount(): void
    {
        $this->schema->fill();
        
        // Inicializar datos por defecto
        $this->data = [
            'name' => '',
            'email' => '',
            'password' => '',
            'password_confirmation' => '',
            'roles' => $this->getDefaultRoles(),
        ];
    }

    protected function getDefaultRoles(): array
    {
        $panelUserRole = \Spatie\Permission\Models\Role::where('name', 'panel_user')->first();
        return $panelUserRole ? [$panelUserRole->id] : [];
    }

    public function schema(Schema $schema): Schema
    {
        return $schema
            ->statePath('data')
            ->components([
                Wizard::make([
                    Step::make('Información Personal')
                        ->description('Datos básicos del usuario')
                        ->icon(Heroicon::OutlinedUser)
                        ->schema([
                            TextInput::make('name')
                                ->label('Nombre completo')
                                ->required()
                                ->regex('/^[a-zA-Z\s]+$/')
                                ->validationMessages([
                                    'regex' => 'El nombre solo puede contener letras y espacios.',
                                ])
                                ->minLength(2)
                                ->maxLength(255),
                            TextInput::make('email')
                                ->label('Correo electrónico')
                                ->email()
                                ->required()
                                ->unique(User::class, 'email')
                                ->validationMessages([
                                    'email' => 'Debe ser una dirección de email válida.',
                                    'unique' => 'Este email ya está registrado.',
                                ]),
                        ])
                        ->columns(2),

                    Step::make('Seguridad')
                        ->description('Configuración de acceso')
                        ->icon(Heroicon::OutlinedLockClosed)
                        ->schema([
                            TextInput::make('password')
                                ->label('Contraseña')
                                ->password()
                                ->required()
                                ->minLength(8)
                                ->validationMessages([
                                    'min' => 'La contraseña debe tener al menos 8 caracteres.',
                                ]),
                            TextInput::make('password_confirmation')
                                ->label('Confirmar contraseña')
                                ->password()
                                ->required()
                                ->same('password')
                                ->validationMessages([
                                    'same' => 'Las contraseñas deben ser idénticas.',
                                ]),
                        ])
                        ->columns(2),

                    Step::make('Roles y Permisos')
                        ->description('Asignar roles al usuario')
                        ->icon(Heroicon::OutlinedShieldCheck)
                        ->schema([
                            Select::make('roles')
                                ->label('Roles')
                                ->multiple()
                                ->options(\Spatie\Permission\Models\Role::all()->pluck('name', 'id'))
                                ->preload()
                                ->searchable()
                                ->required()
                                ->default(function () {
                                    $panelUserRole = \Spatie\Permission\Models\Role::where('name', 'panel_user')->first();
                                    return $panelUserRole ? [$panelUserRole->id] : [];
                                })
                                ->validationMessages([
                                    'required' => 'Debe seleccionar al menos un rol.',
                                ])
                                ->helperText('Selecciona al menos un rol para el usuario. El rol "panel_user" está seleccionado por defecto.'),
                        ])
                        ->columns(2)
                ])
                ->skippable(false)
                ->persistStepInQueryString()
                ->submitAction(new HtmlString(Blade::render(<<<BLADE
                    <x-filament::button
                        type="button"
                        wire:click="submit"
                        size="md"
                        color="primary"
                        icon="heroicon-o-user-plus"
                    >
                        Crear Usuario
                    </x-filament::button>
                BLADE)))
                ->nextAction(
                    fn (\Filament\Actions\Action $action) => $action
                        ->label('Siguiente')
                        ->icon('heroicon-o-arrow-right')
                        ->color('primary')
                )
                ->previousAction(
                    fn (\Filament\Actions\Action $action) => $action
                        ->label('Anterior')
                        ->icon('heroicon-o-arrow-left')
                        ->color('gray')
                ),
            ]);
    }

    public function submit(): void
    {
        try {
            $data = $this->schema->getState();

            // Crear el usuario
            $user = User::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => bcrypt($data['password']),
            ]);

            // Asignar roles al usuario
            $user->roles()->sync($data['roles']);

            // Mostrar notificación de éxito
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Usuario creado exitosamente.',
            ]);

            // Limpiar datos después de éxito
            $this->data = [];
            $this->schema->fill();

            // Redirigir al listado de usuarios
            $this->redirect(route('filament.admin.resources.users.index'));

        } catch (\Exception $e) {
            // En caso de error, mostrar notificación
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Ocurrió un error al crear el usuario. Por favor, inténtalo de nuevo.',
            ]);
        }
    }
}
