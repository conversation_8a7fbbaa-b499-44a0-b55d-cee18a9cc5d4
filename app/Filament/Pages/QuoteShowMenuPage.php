<?php

namespace App\Filament\Pages;

use App\Models\CustomerQuote;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\TextSize;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Text;
use Filament\Schemas\Schema;

class QuoteShowMenuPage extends Page
{
    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-document-text';
    
    protected string $view = 'filament.pages.quote-show-menu';
    
    protected static ?string $navigationLabel = 'Vista de Cotizaciones';
    
    protected static ?string $title = 'Vista de Cotizaciones';
    
    
    protected static ?int $navigationSort = 2;

    public ?int $selectedQuoteId = null;
    public ?CustomerQuote $selectedQuote = null;

    public function mount(): void
    {
        // Cargar la primera cotización por defecto si existe
        $firstQuote = CustomerQuote::first();
        if ($firstQuote) {
            $this->selectedQuoteId = $firstQuote->id;
            $this->selectedQuote = $firstQuote;
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedQuoteId')
                    ->label('Seleccionar Cotización')
                    ->options(CustomerQuote::pluck('quote_number', 'id'))
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        if ($state) {
                            $this->selectedQuote = CustomerQuote::find($state);
                        }
                    })
                    ->required(),
            ])
            ->statePath('data');
    }

    public function infolist(Schema $schema): Schema
    {
        if (!$this->selectedQuote) {
            return $schema->schema([]);
        }

        return $schema
            ->schema([
                Section::make('Información de la Cotización')
                    ->description('Datos principales y configuración')
                    ->schema([
                        Text::make('ID Interno')
                            ->content(fn () => "#{$this->selectedQuote->id}")
                            ->fontFamily('mono')
                            ->weight(FontWeight::Bold),

                        Text::make('Cliente')
                            ->content(fn () => $this->selectedQuote->customer_name)
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),

                        Text::make('Número de Cotización')
                            ->content(fn () => $this->selectedQuote->quote_number)
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),

                        Text::make('Moneda')
                            ->content(fn () => $this->selectedQuote->currency)
                            ->badge(),

                        Text::make('Estado')
                            ->content(fn () => $this->selectedQuote->status)
                            ->badge(),

                        Text::make('Válida hasta')
                            ->content(fn () => $this->selectedQuote->valid_until?->format('d/m/Y')),

                        Text::make('Notas Adicionales')
                            ->content(fn () => $this->selectedQuote->notes)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Resumen Financiero')
                    ->description('Cálculos automáticos de la cotización')
                    ->schema([
                        Text::make('Subtotal')
                            ->content(fn () => '$' . number_format($this->getSubtotal(), 2))
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),

                        Text::make('Impuestos')
                            ->content(fn () => '$' . number_format($this->getTaxAmount(), 2))
                            ->weight(FontWeight::Bold),

                        Text::make('Total')
                            ->content(fn () => '$' . number_format($this->getTotal(), 2))
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large)
                            ->color('success'),
                    ])
                    ->columns(3),

                Section::make('Detalles de Productos')
                    ->description('Grupos, productos y variantes incluidos')
                    ->schema([
                        Text::make('Grupos')
                            ->content(fn () => $this->getGroupsCount()),

                        Text::make('Productos')
                            ->content(fn () => $this->getProductsCount()),

                        Text::make('Variantes')
                            ->content(fn () => $this->getVariantsCount()),
                    ])
                    ->columns(3),
            ]);
    }

    public function getSubtotal(): float
    {
        if (!$this->selectedQuote) return 0;
        
        return $this->selectedQuote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->sum(fn($variant) => $variant->quantity * $variant->unit_price);
    }

    public function getTaxAmount(): float
    {
        if (!$this->selectedQuote) return 0;
        
        $subtotal = $this->getSubtotal();
        $taxRate = $this->selectedQuote->tax_rate ?? 0;
        return $subtotal * ($taxRate / 100);
    }

    public function getTotal(): float
    {
        return $this->getSubtotal() + $this->getTaxAmount();
    }

    public function getGroupsCount(): int
    {
        if (!$this->selectedQuote) return 0;
        
        return $this->selectedQuote->groups()->count();
    }

    public function getProductsCount(): int
    {
        if (!$this->selectedQuote) return 0;
        
        return $this->selectedQuote->groups()
            ->withCount('products')
            ->get()
            ->sum('products_count');
    }

    public function getVariantsCount(): int
    {
        if (!$this->selectedQuote) return 0;
        
        return $this->selectedQuote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->count();
    }
}
