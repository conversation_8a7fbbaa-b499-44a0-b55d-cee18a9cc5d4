<?php

namespace App\Filament\Pages;

use App\Models\CustomerQuote;
use BackedEnum;
use Filament\Forms\Components\Select;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Pages\Page;

class QuoteShowMenuPage extends Page
{
    use InteractsWithForms;
    
    protected static string|BackedEnum|null $navigationIcon = 'heroicon-o-document-text';
    protected string $view = 'filament.pages.quote-show-menu';
    protected static ?string $navigationLabel = 'Vista de Cotizaciones';
    protected static ?string $title = 'Vista de Cotizaciones';
    protected static ?int $navigationSort = 2;

    public ?int $selectedQuoteId = null;
    public ?CustomerQuote $selectedQuote = null;

    public function mount(): void
    {
        // Cargar la primera cotización por defecto si existe
        $firstQuote = CustomerQuote::first();
        if ($firstQuote) {
            $this->selectedQuoteId = $firstQuote->id;
            $this->selectedQuote = $firstQuote;
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedQuoteId')
                    ->label('Seleccionar Cotización')
                    ->options(CustomerQuote::pluck('quote_number', 'id'))
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        if ($state) {
                            $this->selectedQuote = CustomerQuote::find($state);
                        }
                    })
                    ->required(),
            ])
            ->statePath('data');
    }

    public function getSubtotal(): float
    {
        if (!$this->selectedQuote) return 0;
        
        return $this->selectedQuote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->sum(fn($variant) => $variant->quantity * $variant->unit_price);
    }

    public function getTaxAmount(): float
    {
        if (!$this->selectedQuote) return 0;
        
        $subtotal = $this->getSubtotal();
        $taxRate = $this->selectedQuote->tax_rate ?? 0;
        return $subtotal * ($taxRate / 100);
    }

    public function getTotal(): float
    {
        return $this->getSubtotal() + $this->getTaxAmount();
    }

    public function getGroupsCount(): int
    {
        if (!$this->selectedQuote) return 0;
        
        return $this->selectedQuote->groups()->count();
    }

    public function getProductsCount(): int
    {
        if (!$this->selectedQuote) return 0;
        
        return $this->selectedQuote->groups()
            ->withCount('products')
            ->get()
            ->sum('products_count');
    }

    public function getVariantsCount(): int
    {
        if (!$this->selectedQuote) return 0;
        
        return $this->selectedQuote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->count();
    }
}