<?php

namespace App\Filament\Pages;

use App\Models\CustomerQuote;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Text;
use Filament\Schemas\Schema;

class SourcingPlanMenuPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    
    protected static string $view = 'filament.pages.sourcing-plan-menu';
    
    protected static ?string $navigationLabel = 'Plan de Sourcing';
    
    protected static ?string $title = 'Plan de Sourcing';
    
    protected static ?string $navigationGroup = 'Gestión Comercial';
    
    protected static ?int $navigationSort = 3;

    public ?int $selectedQuoteId = null;
    public ?CustomerQuote $selectedQuote = null;
    public bool $showDetails = true;
    public bool $showBatchesOnly = false;
    public ?string $filterSupplier = null;
    public ?string $filterStatus = null;

    public function mount(): void
    {
        // Cargar la primera cotización por defecto si existe
        $firstQuote = CustomerQuote::first();
        if ($firstQuote) {
            $this->selectedQuoteId = $firstQuote->id;
            $this->selectedQuote = $firstQuote;
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('selectedQuoteId')
                    ->label('Seleccionar Cotización')
                    ->options(CustomerQuote::pluck('quote_number', 'id'))
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function ($state) {
                        if ($state) {
                            $this->selectedQuote = CustomerQuote::find($state);
                        }
                    })
                    ->required(),
                
                Toggle::make('showDetails')
                    ->label('Mostrar detalles')
                    ->live(),
                
                Toggle::make('showBatchesOnly')
                    ->label('Solo lotes')
                    ->live(),
            ])
            ->statePath('data');
    }

    public function getKpis(): array
    {
        if (!$this->selectedQuote) {
            return [
                'total_purchase_orders' => 0,
                'total_items' => 0,
                'total_batches' => 0,
                'total_value' => 0,
                'confirmed_value' => 0,
            ];
        }

        $quote = $this->selectedQuote;
        
        $totalPurchaseOrders = $quote->purchaseOrders()->count();
        $totalItems = $quote->purchaseOrders()->withCount('items')->get()->sum('items_count');
        $totalBatches = $quote->purchaseOrders()
            ->with('items.batches')
            ->get()
            ->flatMap(fn($po) => $po->items)
            ->flatMap(fn($item) => $item->batches)
            ->count();

        $totalValue = $quote->purchaseOrders()->sum('total_amount');
        $confirmedValue = $quote->purchaseOrders()
            ->where('status', 'confirmed')
            ->sum('total_amount');

        return [
            'total_purchase_orders' => $totalPurchaseOrders,
            'total_items' => $totalItems,
            'total_batches' => $totalBatches,
            'total_value' => $totalValue,
            'confirmed_value' => $confirmedValue,
        ];
    }

    public function getSuppliers(): array
    {
        if (!$this->selectedQuote) return [];
        
        return $this->selectedQuote->purchaseOrders()
            ->with('supplier')
            ->get()
            ->pluck('supplier.name', 'supplier.id')
            ->unique()
            ->toArray();
    }

    public function getStatuses(): array
    {
        return [
            'draft' => 'Borrador',
            'sent' => 'Enviada',
            'confirmed' => 'Confirmada',
            'closed' => 'Cerrada',
        ];
    }

    public function getPurchaseOrderRows(): \Illuminate\Support\Collection
    {
        if (!$this->selectedQuote) return collect();
        
        $query = $this->selectedQuote->purchaseOrders()->with(['supplier', 'items.batches']);

        if ($this->filterSupplier) {
            $query->where('supplier_id', $this->filterSupplier);
        }

        if ($this->filterStatus) {
            $query->where('status', $this->filterStatus);
        }

        return $query->get()->map(function ($po) {
            $itemsCount = $po->items->count();
            $batchesCount = $po->items->flatMap(fn($item) => $item->batches)->count();
            
            return [
                'id' => $po->id,
                'supplier_name' => $po->supplier->name,
                'status' => $po->status,
                'items_count' => $itemsCount,
                'batches_count' => $batchesCount,
                'total_amount' => $po->total_amount,
                'created_at' => $po->created_at,
            ];
        });
    }

    public function getItemRows(): \Illuminate\Support\Collection
    {
        if (!$this->selectedQuote) return collect();
        
        $query = $this->selectedQuote->purchaseOrders()
            ->with(['supplier', 'items.batches']);

        if ($this->filterSupplier) {
            $query->where('supplier_id', $this->filterSupplier);
        }

        if ($this->filterStatus) {
            $query->where('status', $this->filterStatus);
        }

        return $query->get()
            ->flatMap(fn($po) => $po->items->map(fn($item) => [
                'id' => $item->id,
                'purchase_order_id' => $po->id,
                'supplier_name' => $po->supplier->name,
                'product_name' => $item->product_name,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'total_price' => $item->total_price,
                'batches_count' => $item->batches->count(),
                'status' => $po->status,
            ]));
    }

    public function getBatchRows(): \Illuminate\Support\Collection
    {
        if (!$this->selectedQuote) return collect();
        
        $query = $this->selectedQuote->purchaseOrders()
            ->with(['supplier', 'items.batches']);

        if ($this->filterSupplier) {
            $query->where('supplier_id', $this->filterSupplier);
        }

        if ($this->filterStatus) {
            $query->where('status', $this->filterStatus);
        }

        return $query->get()
            ->flatMap(fn($po) => $po->items)
            ->flatMap(fn($item) => $item->batches->map(fn($batch) => [
                'id' => $batch->id,
                'purchase_order_id' => $po->id,
                'supplier_name' => $po->supplier->name,
                'product_name' => $item->product_name,
                'batch_number' => $batch->batch_number,
                'quantity' => $batch->quantity,
                'status' => $batch->status,
                'pool_state' => $batch->pool_state,
                'created_at' => $batch->created_at,
            ]));
    }

    public function getStatusColor(string $status): string
    {
        return match ($status) {
            'draft' => 'gray',
            'sent' => 'warning',
            'confirmed' => 'success',
            'closed' => 'info',
            default => 'gray',
        };
    }

    public function getPoolStateColor(string $poolState): string
    {
        return match ($poolState) {
            'pending' => 'gray',
            'in_progress' => 'warning',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'gray',
        };
    }
}
