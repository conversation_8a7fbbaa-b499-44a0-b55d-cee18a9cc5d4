<?php

namespace App\Filament\Schemas;

use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class ClientInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Información Básica')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Nombre')
                            ->weight('bold')
                            ->size('lg'),

                        TextEntry::make('company')
                            ->label('Empresa')
                            ->placeholder('—'),

                        TextEntry::make('email')
                            ->label('Email')
                            ->copyable()
                            ->placeholder('—'),

                        TextEntry::make('phone')
                            ->label('Teléfono')
                            ->copyable()
                            ->placeholder('—'),

                        TextEntry::make('tax_id')
                            ->label('RUT/CUIT/ID Fiscal')
                            ->placeholder('—'),

                        TextEntry::make('contact_person')
                            ->label('Persona de Contacto')
                            ->placeholder('—'),
                    ])
                    ->columns(2),

                Section::make('Dirección')
                    ->schema([
                        TextEntry::make('address')
                            ->label('Dirección')
                            ->placeholder('—'),

                        TextEntry::make('city')
                            ->label('Ciudad')
                            ->placeholder('—'),

                        TextEntry::make('state')
                            ->label('Estado/Región')
                            ->placeholder('—'),

                        TextEntry::make('postal_code')
                            ->label('Código Postal')
                            ->placeholder('—'),

                        TextEntry::make('country')
                            ->label('País')
                            ->badge()
                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                'CL' => 'Chile',
                                'AR' => 'Argentina',
                                'PE' => 'Perú',
                                'CO' => 'Colombia',
                                'MX' => 'México',
                                'US' => 'Estados Unidos',
                                'BR' => 'Brasil',
                                'UY' => 'Uruguay',
                                'PY' => 'Paraguay',
                                'BO' => 'Bolivia',
                                'EC' => 'Ecuador',
                                'VE' => 'Venezuela',
                                default => $state,
                            })
                            ->color(fn (string $state): string => match ($state) {
                                'CL' => 'success',
                                'AR' => 'warning',
                                'PE' => 'info',
                                'CO' => 'primary',
                                'MX' => 'secondary',
                                default => 'gray',
                            }),
                    ])
                    ->columns(2),

                Section::make('Información Adicional')
                    ->schema([
                        TextEntry::make('notes')
                            ->label('Notas')
                            ->placeholder('Sin notas')
                            ->columnSpanFull(),

                        IconEntry::make('is_active')
                            ->label('Estado')
                            ->boolean()
                            ->trueIcon('heroicon-o-check-circle')
                            ->falseIcon('heroicon-o-x-circle')
                            ->trueColor('success')
                            ->falseColor('danger'),

                        TextEntry::make('quotes_count')
                            ->label('Total de Cotizaciones')
                            ->badge()
                            ->color('info'),

                        TextEntry::make('created_at')
                            ->label('Fecha de Creación')
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('updated_at')
                            ->label('Última Actualización')
                            ->dateTime('d/m/Y H:i'),
                    ])
                    ->columns(2),
            ]);
    }
}
