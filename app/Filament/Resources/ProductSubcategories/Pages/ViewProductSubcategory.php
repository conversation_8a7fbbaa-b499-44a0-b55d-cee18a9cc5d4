<?php

namespace App\Filament\Resources\ProductSubcategories\Pages;

use App\Filament\Resources\ProductSubcategories\ProductSubcategoryResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewProductSubcategory extends ViewRecord
{
    protected static string $resource = ProductSubcategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
