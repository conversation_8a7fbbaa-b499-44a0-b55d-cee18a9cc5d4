<?php

namespace App\Filament\Resources\ProductSubcategories;

use App\Filament\Resources\ProductSubcategories\Pages\CreateProductSubcategory;
use App\Filament\Resources\ProductSubcategories\Pages\EditProductSubcategory;
use App\Filament\Resources\ProductSubcategories\Pages\ListProductSubcategories;
use App\Filament\Resources\ProductSubcategories\Pages\ViewProductSubcategory;
use App\Filament\Resources\ProductSubcategories\Schemas\ProductSubcategoryForm;
use App\Filament\Resources\ProductSubcategories\Schemas\ProductSubcategoryInfolist;
use App\Filament\Resources\ProductSubcategories\Tables\ProductSubcategoriesTable;
use App\Models\ProductSubcategory;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class ProductSubcategoryResource extends Resource
{
    protected static ?string $model = ProductSubcategory::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedFolderOpen;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationLabel = 'Subcategorías de Productos';

    protected static ?string $modelLabel = 'Subcategoría de Producto';

    protected static ?string $pluralModelLabel = 'Subcategorías de Productos';

    protected static UnitEnum|string|null $navigationGroup = 'Gestión Comercial';

    protected static ?int $navigationSort = 7;

    public static function form(Schema $schema): Schema
    {
        return ProductSubcategoryForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return ProductSubcategoryInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ProductSubcategoriesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProductSubcategories::route('/'),
            'create' => CreateProductSubcategory::route('/create'),
            'view' => ViewProductSubcategory::route('/{record}'),
            'edit' => EditProductSubcategory::route('/{record}/edit'),
        ];
    }
}
