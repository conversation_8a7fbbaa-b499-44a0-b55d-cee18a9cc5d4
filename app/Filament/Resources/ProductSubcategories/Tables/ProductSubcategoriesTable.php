<?php

namespace App\Filament\Resources\ProductSubcategories\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ProductSubcategoriesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('Código')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary')
                    ->weight('bold'),

                TextColumn::make('name')
                    ->label('Nombre')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('category.name')
                    ->label('Categoría')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('info'),

                TextColumn::make('types_count')
                    ->label('Tipos')
                    ->numeric()
                    ->getStateUsing(fn ($record) => $record->types()->count())
                    ->sortable()
                    ->alignCenter()
                    ->badge()
                    ->color('warning'),

                BadgeColumn::make('hasMetadata')
                    ->label('Metadatos')
                    ->getStateUsing(fn ($record) => ! empty($record->metadata) ? 'Con Metadatos' : 'Sin Metadatos')
                    ->color(fn ($record) => ! empty($record->metadata) ? 'success' : 'gray'),

                TextColumn::make('created_at')
                    ->label('Creado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Actualizado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('category')
                    ->label('Categoría')
                    ->relationship('category', 'name')
                    ->searchable()
                    ->preload(),

                Filter::make('has_metadata')
                    ->label('Con Metadatos')
                    ->query(fn ($query) => $query->whereNotNull('metadata')->where('metadata', '!=', '[]')),

                Filter::make('without_metadata')
                    ->label('Sin Metadatos')
                    ->query(fn ($query) => $query->where(function ($q) {
                        $q->whereNull('metadata')->orWhere('metadata', '[]');
                    })),

                Filter::make('with_types')
                    ->label('Con Tipos')
                    ->query(fn ($query) => $query->has('types')),

                Filter::make('without_types')
                    ->label('Sin Tipos')
                    ->query(fn ($query) => $query->doesntHave('types')),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }
}
