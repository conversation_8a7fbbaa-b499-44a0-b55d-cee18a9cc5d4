<?php

namespace App\Filament\Resources\ProductSubcategories\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ProductSubcategoryInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        TextEntry::make('code')
                            ->label('Código')
                            ->badge()
                            ->color('primary'),

                        TextEntry::make('name')
                            ->label('Nombre')
                            ->weight('bold'),

                        TextEntry::make('category.name')
                            ->label('Categoría')
                            ->badge()
                            ->color('info'),

                        TextEntry::make('types_count')
                            ->label('Número de Tipos')
                            ->numeric()
                            ->getStateUsing(fn ($record) => $record->types()->count())
                            ->badge()
                            ->color('warning'),
                    ])
                    ->columns(2),

                Section::make('Configuración Avanzada')
                    ->schema([
                        TextEntry::make('metadata')
                            ->label('Metadatos')
                            ->getStateUsing(function ($record) {
                                if (empty($record->metadata)) {
                                    return 'No configurado';
                                }
                                
                                return is_string($record->metadata) 
                                    ? $record->metadata 
                                    : json_encode($record->metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                            })
                            ->columnSpanFull()
                            ->markdown()
                            ->placeholder('No hay metadatos configurados'),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Section::make('Tipos Relacionados')
                    ->schema([
                        TextEntry::make('types_list')
                            ->label('Tipos de Productos')
                            ->getStateUsing(function ($record) {
                                $types = $record->types()->get();
                                if ($types->isEmpty()) {
                                    return 'No hay tipos asociados';
                                }
                                
                                return $types->map(function ($type) {
                                    return "• {$type->name} ({$type->code})";
                                })->join("\n");
                            })
                            ->columnSpanFull()
                            ->markdown()
                            ->placeholder('No hay tipos asociados'),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Section::make('Información del Sistema')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Fecha de Creación')
                            ->dateTime(),

                        TextEntry::make('updated_at')
                            ->label('Última Actualización')
                            ->dateTime(),

                        TextEntry::make('hasMetadata')
                            ->label('Tiene Metadatos')
                            ->getStateUsing(fn ($record) => ! empty($record->metadata) ? 'Sí' : 'No')
                            ->badge()
                            ->color(fn ($record) => ! empty($record->metadata) ? 'success' : 'gray'),

                        TextEntry::make('hasTypes')
                            ->label('Tiene Tipos')
                            ->getStateUsing(fn ($record) => $record->types()->exists() ? 'Sí' : 'No')
                            ->badge()
                            ->color(fn ($record) => $record->types()->exists() ? 'warning' : 'gray'),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->collapsed(),
            ]);
    }
}
