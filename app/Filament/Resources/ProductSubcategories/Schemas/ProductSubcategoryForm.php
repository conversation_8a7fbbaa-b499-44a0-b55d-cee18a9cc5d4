<?php

namespace App\Filament\Resources\ProductSubcategories\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ProductSubcategoryForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        Select::make('category_id')
                            ->label('Categoría')
                            ->relationship('category', 'name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->helperText('Selecciona la categoría a la que pertenece esta subcategoría'),

                        TextInput::make('code')
                            ->label('Código')
                            ->required()
                            ->maxLength(50)
                            ->unique(ignoreRecord: true)
                            ->helperText('Código único para identificar la subcategoría de producto'),

                        TextInput::make('name')
                            ->label('Nombre')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Nombre descriptivo de la subcategoría de producto'),
                    ])
                    ->columns(2),

                Section::make('Configuración Avanzada')
                    ->schema([
                        Textarea::make('metadata')
                            ->label('Metadatos')
                            ->rows(4)
                            ->helperText('Configuración JSON con metadatos adicionales para esta subcategoría')
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }
}
