<?php

namespace App\Filament\Resources\ProductionBatches\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ProductionBatchesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TextColumn::make('purchaseOrderItem.quoteProductVariant.quoteProduct.name')
                    ->searchable()
                    ->sortable()
                    ->label('Producto')
                    ->description(fn ($record) => $record->purchaseOrderItem->quoteProductVariant->label ?? ''),
                TextColumn::make('quantity')
                    ->numeric()
                    ->sortable()
                    ->label('Cantidad'),
                TextColumn::make('purchaseOrderItem.purchaseOrder.supplier.name')
                    ->searchable()
                    ->sortable()
                    ->label('Proveedor'),
                TextColumn::make('planned_start')
                    ->dateTime()
                    ->sortable()
                    ->label('Inicio Planificado'),
                TextColumn::make('planned_finish')
                    ->dateTime()
                    ->sortable()
                    ->label('Fin Planificado'),
                BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'borrador',
                        'warning' => 'planificado',
                        'primary' => 'en_produccion',
                        'success' => 'completado',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'borrador' => 'Borrador',
                        'planificado' => 'Planificado',
                        'en_produccion' => 'En Producción',
                        'completado' => 'Completado',
                        default => $state,
                    })
                    ->label('Estado'),
                BadgeColumn::make('pool_state')
                    ->colors([
                        'success' => 'available',
                        'danger' => 'consumed',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'available' => 'Disponible',
                        'consumed' => 'Consumido',
                        default => $state,
                    })
                    ->label('Estado del Pool'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Creado'),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Actualizado'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'borrador' => 'Borrador',
                        'planificado' => 'Planificado',
                        'en_produccion' => 'En Producción',
                        'completado' => 'Completado',
                    ])
                    ->label('Estado'),
                SelectFilter::make('pool_state')
                    ->options([
                        'available' => 'Disponible',
                        'consumed' => 'Consumido',
                    ])
                    ->label('Estado del Pool'),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
