<?php

namespace App\Filament\Resources\ProductionBatches\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class ProductionBatchInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('id')
                    ->label('ID'),
                TextEntry::make('purchaseOrderItem.quoteProductVariant.product.name')
                    ->label('Producto'),
                TextEntry::make('quantity')
                    ->label('Cantidad'),
                TextEntry::make('planned_start')
                    ->dateTime()
                    ->label('Inicio Planificado'),
                TextEntry::make('planned_finish')
                    ->dateTime()
                    ->label('Fin Planificado'),
                TextEntry::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'borrador' => 'secondary',
                        'planificado' => 'warning',
                        'en_produccion' => 'primary',
                        'completado' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'borrador' => 'Borrador',
                        'planificado' => 'Planificado',
                        'en_produccion' => 'En Producción',
                        'completado' => 'Completado',
                        default => $state,
                    })
                    ->label('Estado'),
                TextEntry::make('pool_state')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'available' => 'success',
                        'consumed' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'available' => 'Disponible',
                        'consumed' => 'Consumido',
                        default => $state,
                    })
                    ->label('Estado del Pool'),
                TextEntry::make('correlation_id')
                    ->label('ID de Correlación'),
                TextEntry::make('notes')
                    ->label('Notas')
                    ->columnSpanFull(),
                TextEntry::make('created_at')
                    ->dateTime()
                    ->label('Creado'),
                TextEntry::make('updated_at')
                    ->dateTime()
                    ->label('Actualizado'),
            ]);
    }
}
