<?php

namespace App\Filament\Resources\ProductionBatches\Schemas;

use App\Models\PurchaseOrderItem;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class ProductionBatchForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('purchase_order_item_id')
                    ->relationship('purchaseOrderItem', 'id')
                    ->getOptionLabelFromRecordUsing(fn (PurchaseOrderItem $record): string => "Ítem #{$record->id}" . ($record->quoteProductVariant && $record->quoteProductVariant->quoteProduct ? " - {$record->quoteProductVariant->quoteProduct->name}" : ""))
                    ->searchable()
                    ->preload()
                    ->required()
                    ->label('Ítem de Orden de Compra'),
                TextInput::make('quantity')
                    ->required()
                    ->numeric()
                    ->minValue(1)
                    ->label('Cantidad'),
                DateTimePicker::make('planned_start')
                    ->label('Inicio Planificado'),
                DateTimePicker::make('planned_finish')
                    ->label('Fin Planificado'),
                Select::make('status')
                    ->options([
                        'borrador' => 'Borrador',
                        'planificado' => 'Planificado',
                        'en_produccion' => 'En Producción',
                        'completado' => 'Completado',
                    ])
                    ->required()
                    ->default('borrador')
                    ->label('Estado'),
                Select::make('pool_state')
                    ->options([
                        'available' => 'Disponible',
                        'consumed' => 'Consumido',
                    ])
                    ->required()
                    ->default('available')
                    ->label('Estado del Pool'),
                TextInput::make('correlation_id')
                    ->required()
                    ->label('ID de Correlación'),
                Textarea::make('notes')
                    ->rows(3)
                    ->columnSpanFull()
                    ->label('Notas'),
            ]);
    }
}
