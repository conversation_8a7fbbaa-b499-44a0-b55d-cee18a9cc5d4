<?php

namespace App\Filament\Resources\ProductTypes\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ProductTypeInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        TextEntry::make('code')
                            ->label('Código')
                            ->badge()
                            ->color('primary'),

                        TextEntry::make('name')
                            ->label('Nombre')
                            ->weight('bold'),

                        TextEntry::make('subcategory.name')
                            ->label('Subcategoría')
                            ->badge()
                            ->color('info'),

                        TextEntry::make('subcategory.category.name')
                            ->label('Categoría')
                            ->badge()
                            ->color('secondary'),

                        TextEntry::make('hs_code_sugerido')
                            ->label('Código HS Sugerido')
                            ->placeholder('No configurado')
                            ->badge()
                            ->color('warning'),
                    ])
                    ->columns(2),

                Section::make('Configuración Avanzada')
                    ->schema([
                        TextEntry::make('defaults')
                            ->label('Valores por Defecto')
                            ->getStateUsing(function ($record) {
                                if (empty($record->defaults)) {
                                    return 'No configurado';
                                }

                                return is_string($record->defaults)
                                    ? $record->defaults
                                    : json_encode($record->defaults, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                            })
                            ->columnSpanFull()
                            ->markdown()
                            ->placeholder('No hay valores por defecto configurados'),

                        TextEntry::make('reglas')
                            ->label('Reglas de Validación')
                            ->getStateUsing(function ($record) {
                                if (empty($record->reglas)) {
                                    return 'No configurado';
                                }

                                return is_string($record->reglas)
                                    ? $record->reglas
                                    : json_encode($record->reglas, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                            })
                            ->columnSpanFull()
                            ->markdown()
                            ->placeholder('No hay reglas de validación configuradas'),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Section::make('Información del Sistema')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Fecha de Creación')
                            ->dateTime(),

                        TextEntry::make('updated_at')
                            ->label('Última Actualización')
                            ->dateTime(),

                        TextEntry::make('hasDefaults')
                            ->label('Tiene Configuración')
                            ->getStateUsing(fn ($record) => ! empty($record->defaults) ? 'Sí' : 'No')
                            ->badge()
                            ->color(fn ($record) => ! empty($record->defaults) ? 'success' : 'gray'),

                        TextEntry::make('hasRules')
                            ->label('Tiene Reglas')
                            ->getStateUsing(fn ($record) => ! empty($record->reglas) ? 'Sí' : 'No')
                            ->badge()
                            ->color(fn ($record) => ! empty($record->reglas) ? 'warning' : 'gray'),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->collapsed(),
            ]);
    }
}
