<?php

namespace App\Filament\Resources\ProductTypes\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ProductTypeForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        Select::make('subcategory_id')
                            ->label('Subcategoría')
                            ->relationship('subcategory', 'name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->helperText('Selecciona la subcategoría a la que pertenece este tipo de producto'),

                        TextInput::make('code')
                            ->label('Código')
                            ->required()
                            ->maxLength(50)
                            ->unique(ignoreRecord: true)
                            ->helperText('Código único para identificar el tipo de producto'),

                        TextInput::make('name')
                            ->label('Nombre')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Nombre descriptivo del tipo de producto'),

                        TextInput::make('hs_code_sugerido')
                            ->label('Código HS Sugerido')
                            ->maxLength(20)
                            ->helperText('Código arancelario sugerido para este tipo de producto'),
                    ])
                    ->columns(2),

                Section::make('Configuración Avanzada')
                    ->schema([
                        Textarea::make('defaults')
                            ->label('Valores por Defecto')
                            ->rows(4)
                            ->helperText('Configuración JSON con valores por defecto para este tipo de producto')
                            ->columnSpanFull(),

                        Textarea::make('reglas')
                            ->label('Reglas de Validación')
                            ->rows(4)
                            ->helperText('Configuración JSON con reglas de validación específicas para este tipo de producto')
                            ->columnSpanFull(),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }
}
