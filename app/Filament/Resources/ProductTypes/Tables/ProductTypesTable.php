<?php

namespace App\Filament\Resources\ProductTypes\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ProductTypesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('Código')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary')
                    ->weight('bold'),

                TextColumn::make('name')
                    ->label('Nombre')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('subcategory.name')
                    ->label('Subcategoría')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('info'),

                TextColumn::make('subcategory.category.name')
                    ->label('Categoría')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('secondary')
                    ->toggleable(),

                TextColumn::make('hs_code_sugerido')
                    ->label('Código HS')
                    ->searchable()
                    ->sortable()
                    ->placeholder('No configurado')
                    ->toggleable(),

                BadgeColumn::make('hasDefaults')
                    ->label('Configuración')
                    ->getStateUsing(fn ($record) => ! empty($record->defaults) ? 'Con Defaults' : 'Sin Defaults')
                    ->color(fn ($record) => ! empty($record->defaults) ? 'success' : 'gray'),

                BadgeColumn::make('hasRules')
                    ->label('Reglas')
                    ->getStateUsing(fn ($record) => ! empty($record->reglas) ? 'Con Reglas' : 'Sin Reglas')
                    ->color(fn ($record) => ! empty($record->reglas) ? 'warning' : 'gray'),

                TextColumn::make('created_at')
                    ->label('Creado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Actualizado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('subcategory')
                    ->label('Subcategoría')
                    ->relationship('subcategory', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('category')
                    ->label('Categoría')
                    ->relationship('subcategory.category', 'name')
                    ->searchable()
                    ->preload(),

                Filter::make('hs_code_sugerido')
                    ->label('Código HS')
                    ->form([
                        TextInput::make('hs_code')
                            ->label('Código HS')
                            ->placeholder('Buscar por código HS'),
                    ])
                    ->query(function ($query, array $data) {
                        if (filled($data['hs_code'])) {
                            return $query->where('hs_code_sugerido', 'like', "%{$data['hs_code']}%");
                        }

                        return $query;
                    }),

                SelectFilter::make('has_configuration')
                    ->label('Configuración')
                    ->options([
                        'with_defaults' => 'Con Valores por Defecto',
                        'without_defaults' => 'Sin Valores por Defecto',
                        'with_rules' => 'Con Reglas',
                        'without_rules' => 'Sin Reglas',
                    ])
                    ->query(function ($query, array $data) {
                        switch ($data['value']) {
                            case 'with_defaults':
                                return $query->whereNotNull('defaults')->where('defaults', '!=', '[]');
                            case 'without_defaults':
                                return $query->where(function ($q) {
                                    $q->whereNull('defaults')->orWhere('defaults', '[]');
                                });
                            case 'with_rules':
                                return $query->whereNotNull('reglas')->where('reglas', '!=', '[]');
                            case 'without_rules':
                                return $query->where(function ($q) {
                                    $q->whereNull('reglas')->orWhere('reglas', '[]');
                                });
                        }

                        return $query;
                    }),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }
}
