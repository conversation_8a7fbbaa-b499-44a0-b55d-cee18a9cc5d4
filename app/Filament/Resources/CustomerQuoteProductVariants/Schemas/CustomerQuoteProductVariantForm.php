<?php

namespace App\Filament\Resources\CustomerQuoteProductVariants\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CustomerQuoteProductVariantForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        Select::make('quote_product_id')
                            ->label('Producto')
                            ->relationship('quoteProduct', 'name')
                            ->required()
                            ->searchable()
                            ->preload(),

                        TextInput::make('label')
                            ->label('Etiqueta de la Variante')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Ej: "Rojo - Talla M", "USB 32GB - Azul"'),

                        TextInput::make('position')
                            ->label('Posición')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),

                        Textarea::make('notes')
                            ->label('Notas')
                            ->columnSpanFull()
                            ->rows(3),
                    ])
                    ->columns(2),

                Section::make('Especificaciones')
                    ->schema([
                        TextInput::make('hs_code')
                            ->label('Código HS')
                            ->maxLength(20),

                        TextInput::make('weight')
                            ->label('Peso (kg)')
                            ->numeric()
                            ->step(0.001),

                        TextInput::make('volume')
                            ->label('Volumen (m³)')
                            ->numeric()
                            ->step(0.001),
                    ])
                    ->columns(2),

                Section::make('Precios y Cantidades')
                    ->schema([
                        TextInput::make('quantity')
                            ->label('Cantidad')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->default(1),

                        TextInput::make('unit_price_minor')
                            ->label('Precio Unitario (en centavos)')
                            ->numeric()
                            ->minValue(0)
                            ->helperText('Precio en la moneda más pequeña (ej: centavos para USD)'),

                        TextInput::make('price_currency')
                            ->label('Moneda del Precio')
                            ->maxLength(3)
                            ->default('USD')
                            ->helperText('Código de moneda ISO (USD, EUR, CLP)'),

                        TextInput::make('fx_rate_to_quote')
                            ->label('Tipo de Cambio a Cotización')
                            ->numeric()
                            ->default(1)
                            ->minValue(0)
                            ->helperText('Factor de conversión a la moneda de la cotización'),
                    ])
                    ->columns(2),
            ]);
    }
}
