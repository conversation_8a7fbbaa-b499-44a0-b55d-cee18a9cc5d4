<?php

namespace App\Filament\Resources\CustomerQuoteProductVariants\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CustomerQuoteProductVariantInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información de la Variante')
                    ->schema([
                        TextEntry::make('label')
                            ->label('Etiqueta de la Variante'),

                        TextEntry::make('quoteProduct.name')
                            ->label('Producto'),

                        TextEntry::make('quoteProduct.quote.quote_number')
                            ->label('Cotización'),

                        TextEntry::make('notes')
                            ->label('Notas')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Especificaciones')
                    ->schema([
                        TextEntry::make('hs_code')
                            ->label('Código HS'),

                        TextEntry::make('weight')
                            ->label('Peso (kg)')
                            ->numeric(),

                        TextEntry::make('volume')
                            ->label('Volumen (m³)')
                            ->numeric(),

                        TextEntry::make('position')
                            ->label('Posición')
                            ->numeric(),
                    ])
                    ->columns(2),

                Section::make('Precios y Cantidades')
                    ->schema([
                        TextEntry::make('quantity')
                            ->label('Cantidad')
                            ->numeric(),

                        TextEntry::make('unit_price_minor')
                            ->label('Precio Unitario')
                            ->money('USD'),

                        TextEntry::make('price_currency')
                            ->label('Moneda del Precio'),

                        TextEntry::make('fx_rate_to_quote')
                            ->label('Tipo de Cambio')
                            ->numeric(),

                        TextEntry::make('totalAmount')
                            ->label('Total de la Variante')
                            ->getStateUsing(fn ($record) => $record->totalAmount()),
                    ])
                    ->columns(2),

                Section::make('Atributos y Especificaciones')
                    ->schema([
                        TextEntry::make('attributes')
                            ->label('Atributos')
                            ->formatStateUsing(fn ($state) => is_array($state) ? json_encode($state, JSON_PRETTY_PRINT) : $state)
                            ->columnSpanFull(),

                        TextEntry::make('specs')
                            ->label('Especificaciones')
                            ->formatStateUsing(fn ($state) => is_array($state) ? json_encode($state, JSON_PRETTY_PRINT) : $state)
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
