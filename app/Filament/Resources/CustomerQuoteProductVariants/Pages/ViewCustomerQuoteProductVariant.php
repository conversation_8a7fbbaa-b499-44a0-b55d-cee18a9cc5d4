<?php

namespace App\Filament\Resources\CustomerQuoteProductVariants\Pages;

use App\Filament\Resources\CustomerQuoteProductVariants\CustomerQuoteProductVariantResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewCustomerQuoteProductVariant extends ViewRecord
{
    protected static string $resource = CustomerQuoteProductVariantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
