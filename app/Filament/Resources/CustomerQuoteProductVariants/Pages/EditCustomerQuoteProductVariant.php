<?php

namespace App\Filament\Resources\CustomerQuoteProductVariants\Pages;

use App\Filament\Resources\CustomerQuoteProductVariants\CustomerQuoteProductVariantResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditCustomerQuoteProductVariant extends EditRecord
{
    protected static string $resource = CustomerQuoteProductVariantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
