<?php

namespace App\Filament\Resources\CustomerQuoteProductVariants\Pages;

use App\Filament\Resources\CustomerQuoteProductVariants\CustomerQuoteProductVariantResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListCustomerQuoteProductVariants extends ListRecords
{
    protected static string $resource = CustomerQuoteProductVariantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
