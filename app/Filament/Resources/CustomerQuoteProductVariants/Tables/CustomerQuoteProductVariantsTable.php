<?php

namespace App\Filament\Resources\CustomerQuoteProductVariants\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class CustomerQuoteProductVariantsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('quoteProduct.quote.id')
                    ->label('Cotización')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary')
                    ->formatStateUsing(fn ($state) => "#{$state}"),

                TextColumn::make('quoteProduct.name')
                    ->label('Producto')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('label')
                    ->label('Variante')
                    ->searchable()
                    ->sortable()
                    ->color('info'),

                TextColumn::make('quantity')
                    ->label('Cantidad')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('unit_price_minor')
                    ->label('Precio Unitario')
                    ->money('USD')
                    ->sortable()
                    ->placeholder('No configurado'),

                TextColumn::make('price_currency')
                    ->label('Moneda')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('secondary'),

                TextColumn::make('totalAmount')
                    ->label('Total')
                    ->getStateUsing(fn ($record) => $record->totalAmount())
                    ->sortable(),

                TextColumn::make('weight')
                    ->label('Peso (kg)')
                    ->numeric()
                    ->sortable()
                    ->alignCenter()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('volume')
                    ->label('Volumen (m³)')
                    ->numeric()
                    ->sortable()
                    ->alignCenter()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('hs_code')
                    ->label('Código HS')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('position')
                    ->label('Posición')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Creado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Actualizado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('quoteProduct')
                    ->label('Producto')
                    ->relationship('quoteProduct', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('price_currency')
                    ->label('Moneda')
                    ->options(fn () => \App\Models\CustomerQuoteProductVariant::distinct()
                        ->pluck('price_currency', 'price_currency')
                        ->filter()
                        ->toArray())
                    ->searchable(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('position', 'asc');
    }
}
