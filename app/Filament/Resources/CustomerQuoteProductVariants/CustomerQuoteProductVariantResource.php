<?php

namespace App\Filament\Resources\CustomerQuoteProductVariants;

use App\Filament\Resources\CustomerQuoteProductVariants\Pages\CreateCustomerQuoteProductVariant;
use App\Filament\Resources\CustomerQuoteProductVariants\Pages\EditCustomerQuoteProductVariant;
use App\Filament\Resources\CustomerQuoteProductVariants\Pages\ListCustomerQuoteProductVariants;
use App\Filament\Resources\CustomerQuoteProductVariants\Pages\ViewCustomerQuoteProductVariant;
use App\Filament\Resources\CustomerQuoteProductVariants\Schemas\CustomerQuoteProductVariantForm;
use App\Filament\Resources\CustomerQuoteProductVariants\Schemas\CustomerQuoteProductVariantInfolist;
use App\Filament\Resources\CustomerQuoteProductVariants\Tables\CustomerQuoteProductVariantsTable;
use App\Models\CustomerQuoteProductVariant;
use BackedEnum;
use Filament\Panel;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class CustomerQuoteProductVariantResource extends Resource
{
    protected static ?string $model = CustomerQuoteProductVariant::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedTag;

    protected static ?string $recordTitleAttribute = 'label';

    protected static ?string $navigationLabel = 'Variantes de Productos';

    protected static ?string $modelLabel = 'Variante de Producto';

    protected static ?string $pluralModelLabel = 'Variantes de Productos';

    protected static UnitEnum|string|null $navigationGroup = 'Gestión Comercial';

    protected static ?int $navigationSort = 4;

    public static function form(Schema $schema): Schema
    {
        return CustomerQuoteProductVariantForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return CustomerQuoteProductVariantInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return CustomerQuoteProductVariantsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCustomerQuoteProductVariants::route('/'),
            'create' => CreateCustomerQuoteProductVariant::route('/create'),
            'view' => ViewCustomerQuoteProductVariant::route('/{record}'),
            'edit' => EditCustomerQuoteProductVariant::route('/{record}/edit'),
        ];
    }

    public static function getSlug(?Panel $panel = null): string
    {
        return 'customer-quote-product-variants';
    }
}
