<?php

namespace App\Filament\Resources\EventLogs\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class EventLogForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('entity')
                    ->required(),
                TextInput::make('entity_id')
                    ->required()
                    ->numeric(),
                TextInput::make('action')
                    ->required(),
                Select::make('actor_id')
                    ->relationship('actor', 'name'),
                TextInput::make('payload'),
            ]);
    }
}
