<?php

namespace App\Filament\Resources\EventLogs;

use App\Filament\Resources\EventLogs\Pages\ListEventLogs;
use App\Filament\Resources\EventLogs\Pages\ViewEventLog;
use App\Filament\Resources\EventLogs\Tables\EventLogsTable;
use App\Models\EventLog;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class EventLogResource extends Resource
{
    protected static ?string $model = EventLog::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedDocumentText;

    protected static bool $shouldRegisterNavigation = true;

    public static function table(Table $table): Table
    {
        return EventLogsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListEventLogs::route('/'),
            'view' => ViewEventLog::route('/{record}'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }
}
