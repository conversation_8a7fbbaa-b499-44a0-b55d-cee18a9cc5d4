<?php

namespace App\Filament\Resources\EventLogs\Tables;

use Filament\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class EventLogsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                BadgeColumn::make('entity')
                    ->colors([
                        'primary' => 'supplier',
                        'success' => 'po',
                        'warning' => 'po_item',
                        'info' => 'batch',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'supplier' => 'Proveedor',
                        'po' => 'Orden de Compra',
                        'po_item' => 'Ítem de OC',
                        'batch' => 'Lote de Producción',
                        default => $state,
                    })
                    ->label('Entidad'),
                TextColumn::make('entity_id')
                    ->numeric()
                    ->sortable()
                    ->label('ID de Entidad'),
                TextColumn::make('action')
                    ->searchable()
                    ->label('Acción'),
                TextColumn::make('actor.name')
                    ->searchable()
                    ->placeholder('Sistema')
                    ->label('Actor'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->label('Fecha'),
            ])
            ->filters([
                SelectFilter::make('entity')
                    ->options([
                        'supplier' => 'Proveedor',
                        'po' => 'Orden de Compra',
                        'po_item' => 'Ítem de OC',
                        'batch' => 'Lote de Producción',
                    ])
                    ->label('Entidad'),
                SelectFilter::make('action')
                    ->options([
                        'created' => 'Creado',
                        'updated' => 'Actualizado',
                        'deleted' => 'Eliminado',
                        'status_changed' => 'Estado Cambiado',
                    ])
                    ->label('Acción'),
            ])
            ->recordActions([
                ViewAction::make(),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
