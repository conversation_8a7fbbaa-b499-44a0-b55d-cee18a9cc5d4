<?php

namespace App\Filament\Resources\EventLogs\Pages;

use App\Filament\Resources\EventLogs\EventLogResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Schemas\Schema;

class ViewEventLog extends ViewRecord
{
    protected static string $resource = EventLogResource::class;

    public function infolist(Schema $schema): Schema
    {
        return $schema
            ->components([
                \Filament\Infolists\Components\TextEntry::make('id')
                    ->label('ID'),
                \Filament\Infolists\Components\TextEntry::make('entity')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'supplier' => 'primary',
                        'po' => 'success',
                        'po_item' => 'warning',
                        'batch' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'supplier' => 'Proveedor',
                        'po' => 'Orden de Compra',
                        'po_item' => 'Ítem de OC',
                        'batch' => 'Lote de Producción',
                        default => $state,
                    })
                    ->label('Entidad'),
                \Filament\Infolists\Components\TextEntry::make('entity_id')
                    ->label('ID de Entidad'),
                \Filament\Infolists\Components\TextEntry::make('action')
                    ->label('Acción'),
                \Filament\Infolists\Components\TextEntry::make('actor.name')
                    ->placeholder('Sistema')
                    ->label('Actor'),
                \Filament\Infolists\Components\KeyValueEntry::make('payload')
                    ->keyLabel('Campo')
                    ->valueLabel('Valor')
                    ->label('Datos'),
                \Filament\Infolists\Components\TextEntry::make('created_at')
                    ->dateTime()
                    ->label('Fecha'),
            ]);
    }
}
