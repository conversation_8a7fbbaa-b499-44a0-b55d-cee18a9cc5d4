<?php

namespace App\Filament\Resources\ProductCategories\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;

class ProductCategoriesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->label('Código')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary')
                    ->weight('bold'),

                TextColumn::make('name')
                    ->label('Nombre')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('subcategories_count')
                    ->label('Subcategorías')
                    ->numeric()
                    ->getStateUsing(fn ($record) => $record->subcategories()->count())
                    ->sortable()
                    ->alignCenter()
                    ->badge()
                    ->color('info'),

                BadgeColumn::make('hasMetadata')
                    ->label('Metadatos')
                    ->getStateUsing(fn ($record) => ! empty($record->metadata) ? 'Con Metadatos' : 'Sin Metadatos')
                    ->color(fn ($record) => ! empty($record->metadata) ? 'success' : 'gray'),

                TextColumn::make('created_at')
                    ->label('Creado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Actualizado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Filter::make('has_metadata')
                    ->label('Con Metadatos')
                    ->query(fn ($query) => $query->whereNotNull('metadata')->where('metadata', '!=', '[]')),

                Filter::make('without_metadata')
                    ->label('Sin Metadatos')
                    ->query(fn ($query) => $query->where(function ($q) {
                        $q->whereNull('metadata')->orWhere('metadata', '[]');
                    })),

                Filter::make('with_subcategories')
                    ->label('Con Subcategorías')
                    ->query(fn ($query) => $query->has('subcategories')),

                Filter::make('without_subcategories')
                    ->label('Sin Subcategorías')
                    ->query(fn ($query) => $query->doesntHave('subcategories')),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('name', 'asc');
    }
}
