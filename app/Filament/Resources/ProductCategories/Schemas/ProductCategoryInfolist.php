<?php

namespace App\Filament\Resources\ProductCategories\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class ProductCategoryInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        TextEntry::make('code')
                            ->label('Código')
                            ->badge()
                            ->color('primary'),

                        TextEntry::make('name')
                            ->label('Nombre')
                            ->weight('bold'),

                        TextEntry::make('subcategories_count')
                            ->label('Número de Subcategorías')
                            ->numeric()
                            ->getStateUsing(fn ($record) => $record->subcategories()->count())
                            ->badge()
                            ->color('info'),
                    ])
                    ->columns(2),

                Section::make('Configuración Avanzada')
                    ->schema([
                        TextEntry::make('metadata')
                            ->label('Metadatos')
                            ->getStateUsing(function ($record) {
                                if (empty($record->metadata)) {
                                    return 'No configurado';
                                }

                                return is_string($record->metadata)
                                    ? $record->metadata
                                    : json_encode($record->metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                            })
                            ->columnSpanFull()
                            ->markdown()
                            ->placeholder('No hay metadatos configurados'),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Section::make('Subcategorías Relacionadas')
                    ->schema([
                        TextEntry::make('subcategories_list')
                            ->label('Subcategorías')
                            ->getStateUsing(function ($record) {
                                $subcategories = $record->subcategories()->get();
                                if ($subcategories->isEmpty()) {
                                    return 'No hay subcategorías asociadas';
                                }

                                return $subcategories->map(function ($subcategory) {
                                    return "• {$subcategory->name} ({$subcategory->code})";
                                })->join("\n");
                            })
                            ->columnSpanFull()
                            ->markdown()
                            ->placeholder('No hay subcategorías asociadas'),
                    ])
                    ->collapsible()
                    ->collapsed(),

                Section::make('Información del Sistema')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Fecha de Creación')
                            ->dateTime(),

                        TextEntry::make('updated_at')
                            ->label('Última Actualización')
                            ->dateTime(),

                        TextEntry::make('hasMetadata')
                            ->label('Tiene Metadatos')
                            ->getStateUsing(fn ($record) => ! empty($record->metadata) ? 'Sí' : 'No')
                            ->badge()
                            ->color(fn ($record) => ! empty($record->metadata) ? 'success' : 'gray'),

                        TextEntry::make('hasSubcategories')
                            ->label('Tiene Subcategorías')
                            ->getStateUsing(fn ($record) => $record->subcategories()->exists() ? 'Sí' : 'No')
                            ->badge()
                            ->color(fn ($record) => $record->subcategories()->exists() ? 'info' : 'gray'),
                    ])
                    ->columns(2)
                    ->collapsible()
                    ->collapsed(),
            ]);
    }
}
