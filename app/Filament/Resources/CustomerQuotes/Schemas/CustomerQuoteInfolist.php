<?php

namespace App\Filament\Resources\CustomerQuotes\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\TextSize;

class CustomerQuoteInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Información Básica')
                    ->description('Datos principales de la cotización')
                    ->schema([
                        TextEntry::make('quote_number')
                            ->label('Número de Cotización')
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large)
                            ->copyable(),

                        TextEntry::make('client.name')
                            ->label('Cliente')
                            ->placeholder('Sin cliente asignado')
                            ->url(fn ($record) => $record->client ? route('filament.admin.resources.customers.view', $record->client) : null)
                            ->openUrlInNewTab(),

                        TextEntry::make('project_id')
                            ->label('ID del Proyecto')
                            ->placeholder('No especificado'),

                        TextEntry::make('status')
                            ->label('Estado')
                            ->badge()
                            ->colors([
                                'secondary' => 'borrador',
                                'warning' => 'enviada',
                                'success' => 'aceptada',
                                'danger' => 'rechazada',
                                'gray' => 'expirada',
                            ]),
                    ])
                    ->columns(2),

                Section::make('Configuración')
                    ->description('Parámetros de la cotización')
                    ->schema([
                        TextEntry::make('currency')
                            ->label('Moneda')
                            ->badge()
                            ->colors([
                                'primary' => 'CLP',
                                'success' => 'USD',
                                'warning' => 'EUR',
                            ]),

                        TextEntry::make('country')
                            ->label('País')
                            ->badge()
                            ->colors([
                                'primary' => 'CL',
                                'success' => 'US',
                                'warning' => 'ES',
                                'info' => 'AR',
                                'secondary' => 'MX',
                            ]),

                        TextEntry::make('valid_until')
                            ->label('Válida hasta')
                            ->date('d/m/Y')
                            ->color(fn ($record) => $record->valid_until < now() ? 'danger' : 'success'),

                        TextEntry::make('total')
                            ->label('Total')
                            ->getStateUsing(fn ($record) => $record->money($record->totalAmount()))
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),
                    ])
                    ->columns(2),

                Section::make('Información Adicional')
                    ->schema([
                        TextEntry::make('notes')
                            ->label('Notas Adicionales')
                            ->placeholder('Sin notas adicionales')
                            ->columnSpanFull(),

                        TextEntry::make('created_at')
                            ->label('Fecha de Creación')
                            ->dateTime('d/m/Y H:i:s'),

                        TextEntry::make('updated_at')
                            ->label('Última Actualización')
                            ->dateTime('d/m/Y H:i:s'),
                    ])
                    ->columns(2),
            ]);
    }
}
