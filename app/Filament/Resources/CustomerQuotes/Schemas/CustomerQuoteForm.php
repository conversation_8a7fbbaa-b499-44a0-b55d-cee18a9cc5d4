<?php

namespace App\Filament\Resources\CustomerQuotes\Schemas;

use App\Models\Customer;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CustomerQuoteForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Información Básica')
                    ->description('Datos principales de la cotización')
                    ->schema([
                        TextInput::make('quote_number')
                            ->label('Número de Cotización')
                            ->placeholder('Ej: COT-2024-001')
                            ->unique(ignoreRecord: true)
                            ->required(),

                        Select::make('customer_id')
                            ->label('Cliente')
                            ->relationship('customer', 'name')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                TextInput::make('name')->required()->maxLength(255),
                                TextInput::make('email')->email()->maxLength(255),
                                TextInput::make('phone')->tel()->maxLength(255),
                            ])
                            ->createOptionUsing(function (array $data): int {
                                return Customer::create($data)->getKey();
                            })
                            ->required()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $client = Customer::find($state);
                                    if ($client) {
                                        $set('customer_name', $client->name);
                                        $set('country', $client->country);
                                    }
                                }
                            }),

                        TextInput::make('customer_name')
                            ->label('Nombre del Cliente (fallback)')
                            ->maxLength(255)
                            ->helperText('Se llena automáticamente al seleccionar un cliente'),

                        TextInput::make('project_id')
                            ->label('ID del Proyecto')
                            ->numeric()
                            ->placeholder('Opcional'),
                    ])
                    ->columns(2),

                Section::make('Configuración')
                    ->description('Parámetros de la cotización')
                    ->schema([
                        Select::make('currency')
                            ->label('Moneda')
                            ->options([
                                'CLP' => 'Peso Chileno (CLP)',
                                'USD' => 'Dólar Americano (USD)',
                                'EUR' => 'Euro (EUR)',
                            ])
                            ->default('CLP')
                            ->required(),

                        Select::make('country')
                            ->label('País')
                            ->options([
                                'CL' => 'Chile',
                                'AR' => 'Argentina',
                                'PE' => 'Perú',
                                'CO' => 'Colombia',
                                'MX' => 'México',
                                'US' => 'Estados Unidos',
                                'ES' => 'España',
                                'BR' => 'Brasil',
                            ])
                            ->default('CL')
                            ->required(),

                        DatePicker::make('valid_until')
                            ->label('Válida hasta')
                            ->default(now()->addDays(30))
                            ->required(),

                        Select::make('status')
                            ->label('Estado')
                            ->options([
                                'borrador' => 'Borrador',
                                'enviada' => 'Enviada',
                                'aceptada' => 'Aceptada',
                                'rechazada' => 'Rechazada',
                                'expirada' => 'Expirada',
                            ])
                            ->default('borrador')
                            ->required(),
                    ])
                    ->columns(2),

                Section::make('Información Adicional')
                    ->schema([
                        Textarea::make('notes')
                            ->label('Notas')
                            ->placeholder('Información adicional sobre la cotización...')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
