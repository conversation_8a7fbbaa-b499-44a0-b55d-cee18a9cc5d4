<?php

namespace App\Filament\Resources\CustomerQuotes\Tables;

use App\Filament\Resources\CustomerQuotes\Actions\DuplicateQuoteAction;
use App\Filament\Resources\CustomerQuotes\Actions\ExportPdfAction;
use App\Filament\Resources\CustomerQuotes\Actions\ViewCompositionAction;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CustomerQuotesTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('quote_number')
                    ->label('Número')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->copyable(),

                TextColumn::make('customer.name')
                    ->label('Cliente')
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->placeholder('Sin cliente asignado')
                    ->default(fn ($record) => $record->customer_name),

                BadgeColumn::make('status')
                    ->label('Estado')
                    ->colors([
                        'secondary' => 'borrador',
                        'warning' => 'enviada',
                        'success' => 'aceptada',
                        'danger' => 'rechazada',
                        'gray' => 'expirada',
                    ])
                    ->icons([
                        'heroicon-o-document' => 'borrador',
                        'heroicon-o-paper-airplane' => 'enviada',
                        'heroicon-o-check-circle' => 'aceptada',
                        'heroicon-o-x-circle' => 'rechazada',
                        'heroicon-o-clock' => 'expirada',
                    ]),

                TextColumn::make('currency')
                    ->label('Moneda')
                    ->badge()
                    ->colors([
                        'primary' => 'CLP',
                        'success' => 'USD',
                        'warning' => 'EUR',
                    ]),

                TextColumn::make('country')
                    ->label('País')
                    ->badge()
                    ->colors([
                        'primary' => 'CL',
                        'success' => 'US',
                        'warning' => 'ES',
                    ]),

                TextColumn::make('valid_until')
                    ->label('Válida hasta')
                    ->date('d/m/Y')
                    ->sortable()
                    ->color(fn ($record) => $record->valid_until < now() ? 'danger' : 'success'),

                TextColumn::make('total')
                    ->label('Total')
                    ->getStateUsing(fn ($record) => $record->money($record->totalAmount()))
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('products_count')
                    ->label('Número de Items')
                    ->counts('products')
                    ->badge()
                    ->color('info'),

                TextColumn::make('variants_count')
                    ->label('Número de Variantes')
                    ->getStateUsing(function ($record) {
                        return $record->products()->withCount('variants')->get()->sum('variants_count');
                    })
                    ->badge()
                    ->color('success'),

                TextColumn::make('created_at')
                    ->label('Creada')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Actualizada')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('customer_id')
                    ->label('Cliente')
                    ->relationship('customer', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('status')
                    ->label('Estado')
                    ->options([
                        'borrador' => 'Borrador',
                        'enviada' => 'Enviada',
                        'aceptada' => 'Aceptada',
                        'rechazada' => 'Rechazada',
                        'expirada' => 'Expirada',
                    ]),

                SelectFilter::make('currency')
                    ->label('Moneda')
                    ->options([
                        'CLP' => 'Peso Chileno (CLP)',
                        'USD' => 'Dólar Americano (USD)',
                        'EUR' => 'Euro (EUR)',
                    ]),

                SelectFilter::make('country')
                    ->label('País')
                    ->options([
                        'CL' => 'Chile',
                        'AR' => 'Argentina',
                        'PE' => 'Perú',
                        'CO' => 'Colombia',
                        'MX' => 'México',
                        'US' => 'Estados Unidos',
                        'ES' => 'España',
                        'BR' => 'Brasil',
                    ]),

                Filter::make('expired')
                    ->label('Expiradas')
                    ->query(fn (Builder $query): Builder => $query->where('valid_until', '<', now()))
                    ->toggle(),

                Filter::make('recent')
                    ->label('Recientes (últimos 7 días)')
                    ->query(fn (Builder $query): Builder => $query->where('created_at', '>=', now()->subDays(7)))
                    ->toggle(),

                Filter::make('with_client')
                    ->label('Con cliente asignado')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('customer_id'))
                    ->toggle(),

                Filter::make('without_client')
                    ->label('Sin cliente asignado')
                    ->query(fn (Builder $query): Builder => $query->whereNull('customer_id'))
                    ->toggle(),
            ])
            ->recordActions([
                ViewCompositionAction::make(),
                ViewAction::make()->label('Ver Detalles'),
                EditAction::make()->label('Editar'),
                DuplicateQuoteAction::make(),
                ExportPdfAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }
}
