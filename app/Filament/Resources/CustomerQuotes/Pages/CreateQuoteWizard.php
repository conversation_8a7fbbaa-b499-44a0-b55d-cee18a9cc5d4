<?php

namespace App\Filament\Resources\CustomerQuotes\Pages;

use App\Filament\Resources\CustomerQuotes\CustomerQuoteResource;
use App\Models\Customer;
use App\Models\CustomerQuote;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;
use App\Models\ProductType;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ViewField;
use Filament\Resources\Pages\CreateRecord;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Wizard\Step;
use Filament\Support\Icons\Heroicon;

class CreateQuoteWizard extends CreateRecord
{
    use CreateRecord\Concerns\HasWizard;

    protected static string $resource = CustomerQuoteResource::class;

    protected function getSteps(): array
    {
        return [
            Step::make('Cliente')
                ->description('Selecciona o crea un cliente para la cotización')
                ->icon(Heroicon::User)
                ->schema([
                    $this->getCustomerSelectionStep(),
                ]),

            Step::make('Configuración')
                ->description('Define la información básica de la cotización')
                ->icon(Heroicon::Cog6Tooth)
                ->schema([
                    $this->getQuoteConfigurationStep(),
                ]),

            Step::make('Productos')
                ->description('Selecciona los productos para incluir en la cotización')
                ->icon(Heroicon::ShoppingBag)
                ->schema([
                    $this->getProductSelectionStep(),
                ]),

            Step::make('Variantes')
                ->description('Configura las variantes y cantidades de cada producto')
                ->icon(Heroicon::AdjustmentsHorizontal)
                ->schema([
                    $this->getVariantConfigurationStep(),
                ]),
        ];
    }

    protected function getCustomerSelectionStep(): Grid
    {
        return Grid::make(2)
            ->schema([
                Select::make('customer_id')
                    ->label('Cliente')
                    ->searchable()
                    ->preload()
                    ->options(function () {
                        return Customer::active()
                            ->get()
                            ->mapWithKeys(fn (Customer $customer) => [
                                $customer->id => $customer->full_name,
                            ]);
                    })
                    ->getSearchResultsUsing(function (string $search) {
                        return Customer::active()
                            ->search($search)
                            ->limit(50)
                            ->get()
                            ->mapWithKeys(fn (Customer $customer) => [
                                $customer->id => $customer->full_name,
                            ]);
                    })
                    ->required()
                    ->live()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            $customer = Customer::find($state);
                            if ($customer) {
                                $set('customer_name', $customer->name);
                                $set('country', $customer->country);
                            }
                        }
                    }),

                TextInput::make('customer_name')
                    ->label('Nombre del Cliente')
                    ->disabled()
                    ->dehydrated(false),
            ]);
    }

    protected function getQuoteConfigurationStep(): Grid
    {
        return Grid::make(2)
            ->schema([
                TextInput::make('quote_number')
                    ->label('Número de Cotización')
                    ->default(fn () => 'COT-'.str_pad(CustomerQuote::count() + 1, 6, '0', STR_PAD_LEFT))
                    ->required()
                    ->unique(CustomerQuote::class, 'quote_number', ignoreRecord: true),

                Select::make('currency')
                    ->label('Moneda')
                    ->options([
                        'USD' => 'USD - Dólar Americano',
                        'EUR' => 'EUR - Euro',
                        'CLP' => 'CLP - Peso Chileno',
                        'MXN' => 'MXN - Peso Mexicano',
                    ])
                    ->default('USD')
                    ->required(),

                DatePicker::make('valid_until')
                    ->label('Válida hasta')
                    ->default(now()->addDays(30))
                    ->minDate(now())
                    ->required(),

                Select::make('country')
                    ->label('País')
                    ->options([
                        'CL' => 'Chile',
                        'US' => 'Estados Unidos',
                        'MX' => 'México',
                        'ES' => 'España',
                        'AR' => 'Argentina',
                    ])
                    ->default('CL')
                    ->required(),

                Textarea::make('notes')
                    ->label('Notas Internas')
                    ->rows(3)
                    ->columnSpanFull(),

                Toggle::make('is_draft')
                    ->label('Guardar como borrador')
                    ->default(true)
                    ->columnSpanFull(),
            ]);
    }

    protected function getProductSelectionStep(): Grid
    {
        return Grid::make(3)
            ->schema([
                Select::make('category_filter')
                    ->label('Categoría')
                    ->options(ProductCategory::all()->pluck('name', 'id'))
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(fn (callable $set) => $set('subcategory_filter', null)),

                Select::make('subcategory_filter')
                    ->label('Subcategoría')
                    ->options(function (callable $get) {
                        $categoryId = $get('category_filter');
                        if (! $categoryId) {
                            return [];
                        }

                        return ProductSubcategory::where('category_id', $categoryId)
                            ->get()
                            ->pluck('name', 'id');
                    })
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(fn (callable $set) => $set('type_filter', null)),

                Select::make('type_filter')
                    ->label('Tipo de Producto')
                    ->options(function (callable $get) {
                        $subcategoryId = $get('subcategory_filter');
                        if (! $subcategoryId) {
                            return [];
                        }

                        return ProductType::where('subcategory_id', $subcategoryId)
                            ->get()
                            ->pluck('name', 'id');
                    })
                    ->searchable()
                    ->live(),

                ViewField::make('product_selection')
                    ->label('Productos Disponibles')
                    ->view('filament.forms.components.product-selection-simple')
                    ->columnSpanFull(),
            ]);
    }

    protected function getVariantConfigurationStep(): ViewField
    {
        return ViewField::make('variant_configuration')
            ->label('Configuración de Variantes')
            ->view('filament.forms.components.variant-configuration')
            ->columnSpanFull();
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['status'] = $data['is_draft'] ? 'borrador' : 'enviada';
        unset($data['is_draft']);

        return $data;
    }
}
