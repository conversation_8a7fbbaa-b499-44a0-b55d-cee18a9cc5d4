<?php

namespace App\Filament\Resources\CustomerQuotes\Pages;

use App\Filament\Resources\CustomerQuotes\CustomerQuoteResource;
use App\Models\CustomerQuote;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\TextEntry;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\TextSize;

class QuoteShowPage extends ViewRecord
{
    protected static string $resource = CustomerQuoteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('refresh')
                ->label('🔄 Refrescar')
                ->icon('heroicon-o-arrow-path')
                ->action('refreshQuote')
                ->loadingState('refreshing')
                ->color('primary'),
            EditAction::make(),
        ];
    }

    public function refreshQuote(): void
    {
        try {
            $this->record->refresh();
            $this->record->loadMissing([
                'groups.products',
                'groups.products.variants',
                'products',
                'products.variants',
            ]);
            
            $this->dispatch('quote-refreshed');
        } catch (\Exception $e) {
            \Log::error('Error refreshing quote: ' . $e->getMessage());
        }
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Información de la Cotización')
                    ->description('Datos principales y configuración')
                    ->schema([
                        TextEntry::make('id')
                            ->label('ID Interno')
                            ->formatStateUsing(fn ($state) => "#{$state}")
                            ->fontFamily('mono')
                            ->weight(FontWeight::Bold),

                        TextEntry::make('customer_name')
                            ->label('Cliente')
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),

                        TextEntry::make('quote_number')
                            ->label('Número de Cotización')
                            ->placeholder('—')
                            ->badge(),

                        TextEntry::make('currency')
                            ->label('Moneda')
                            ->badge()
                            ->colors([
                                'primary' => 'CLP',
                                'success' => 'USD',
                                'warning' => 'EUR',
                            ]),

                        TextEntry::make('status')
                            ->label('Estatus')
                            ->badge()
                            ->colors([
                                'secondary' => 'borrador',
                                'warning' => 'enviada',
                                'success' => 'aceptada',
                                'danger' => 'rechazada',
                                'gray' => 'expirada',
                            ]),

                        TextEntry::make('valid_until')
                            ->label('Válida hasta')
                            ->date('d/m/Y')
                            ->placeholder('—')
                            ->color(fn ($record) => $record->valid_until && $record->valid_until < now() ? 'danger' : 'success'),
                    ])
                    ->columns(3),

                Section::make('Notas')
                    ->schema([
                        TextEntry::make('notes')
                            ->label('Notas Adicionales')
                            ->placeholder('Sin notas adicionales')
                            ->columnSpanFull(),
                    ])
                    ->visible(fn ($record) => !empty($record->notes)),

                Section::make('Artículos Cotizados')
                    ->description('Lista detallada de productos y grupos')
                    ->schema([
                        TextEntry::make('items_summary')
                            ->label('')
                            ->formatStateUsing(function ($record) {
                                return $this->renderItemsTable($record);
                            })
                            ->html()
                            ->columnSpanFull(),
                    ]),

                Section::make('Resumen Financiero')
                    ->description('Cálculos de totales y impuestos')
                    ->schema([
                        TextEntry::make('subtotal')
                            ->label('Subtotal')
                            ->formatStateUsing(fn ($record) => $record->money($record->subtotalAmount()))
                            ->weight(FontWeight::Bold),

                        TextEntry::make('tax')
                            ->label('IVA ' . ($this->record->country ? '(' . strtoupper($this->record->country) . ')' : '') . ' (' . number_format($this->record->taxRate() * 100, 0) . '%)')
                            ->formatStateUsing(fn ($record) => $record->money($record->taxAmount()))
                            ->weight(FontWeight::Bold),

                        TextEntry::make('total')
                            ->label('Total a Pagar')
                            ->formatStateUsing(fn ($record) => $record->money($record->totalAmount()))
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large)
                            ->color('success'),
                    ])
                    ->columns(3),
            ]);
    }

    private function renderItemsTable(CustomerQuote $quote): string
    {
        $rootProducts = $quote->products->whereNull('group_id')->sortBy('position');
        $groups = $quote->groups->sortBy('position');
        
        if ($groups->count() === 0 && $rootProducts->count() === 0) {
            return '<div class="text-sm text-gray-600 dark:text-gray-300">No hay artículos para mostrar.</div>';
        }

        $html = '<div class="overflow-x-auto">';
        $html .= '<table class="min-w-[640px] text-sm w-full">';
        $html .= '<thead>';
        $html .= '<tr class="text-left border-b border-gray-200 dark:border-gray-700">';
        $html .= '<th class="py-2 pr-4 w-10">#</th>';
        $html .= '<th class="py-2 pr-4">Descripción</th>';
        $html .= '<th class="py-2 pr-4 text-right">Cantidad</th>';
        $html .= '<th class="py-2 pr-4 text-right">Precio Unitario</th>';
        $html .= '<th class="py-2 text-right">Total</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody class="divide-y divide-gray-200 dark:divide-gray-700">';

        $i = 1;

        // Render groups
        foreach ($groups as $group) {
            $units = $group->totalUnitsUniform();
            $gTotal = $group->totalAmount();
            $gUnit = $group->avgUnitPrice();

            $html .= '<tr>';
            $html .= '<td class="py-2 pr-4 align-top">' . $i++ . '</td>';
            $html .= '<td class="py-2 pr-4">';
            $html .= '<div class="font-medium">' . e($group->name) . ' <span class="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">[ID: <span class="font-mono">' . $group->id . '</span>]</span></div>';
            
            if ($group->notes) {
                $html .= '<div class="text-gray-600 dark:text-gray-300">' . e($group->notes) . '</div>';
            }

            if ($group->isKitWithFixedPrice()) {
                $kitErrors = $group->validateKitComposition();
                
                if (!empty($kitErrors)) {
                    $html .= '<div class="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">';
                    $html .= '<div class="text-sm font-medium text-red-800 dark:text-red-200 mb-1">⚠️ Error en composición del kit:</div>';
                    foreach ($kitErrors as $error) {
                        $html .= '<div class="text-xs text-red-700 dark:text-red-300">' . e($error) . '</div>';
                    }
                    $html .= '</div>';
                }
                
                $html .= '<div class="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">';
                $html .= '<div class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">';
                $html .= '📦 Kit: ' . $group->quantity . ' kits × ' . $group->unit_price->format() . ' = ' . $quote->currencyDto()->format($group->totalAmount());
                $html .= '</div>';
                $html .= '<div class="text-xs text-blue-700 dark:text-blue-300">';
                $html .= '<div class="font-medium mb-1">Composición del kit:</div>';
                
                foreach ($group->products->sortBy('position') as $gp) {
                    if ($gp->units_per_kit) {
                        $html .= '<div class="ml-2">• ' . e($gp->name) . ': ' . $gp->units_per_kit . ' por kit';
                        if ($gp->variants->count()) {
                            $html .= '<div class="ml-4 text-gray-600 dark:text-gray-400">';
                            foreach ($gp->variants->sortBy('position') as $v) {
                                $html .= '<div>◦ ' . e($v->label) . ' <span class="text-blue-600 dark:text-blue-400 font-medium">(' . $v->quantity . ' unidades)</span></div>';
                            }
                            $html .= '</div>';
                        }
                        $html .= '</div>';
                    }
                }
                $html .= '</div>';
                $html .= '</div>';
            } else {
                if ($group->products->count()) {
                    $html .= '<div class="mt-2 text-xs text-gray-600 dark:text-gray-400 space-y-1">';
                    foreach ($group->products->sortBy('position') as $gp) {
                        $html .= '<div>' . e($gp->name) . ' — ' . $gp->totalUnits() . ' uds <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">[item: <span class="font-mono">' . $gp->id . '</span> · tipo: <span class="font-mono">' . $gp->type_name . '</span>]</span></div>';
                        if ($gp->variants->count()) {
                            $html .= '<div class="ms-4">';
                            foreach ($gp->variants->sortBy('position') as $v) {
                                $html .= '<div>◦ Variante: ' . e($v->label) . ' — ' . $v->quantity . ' uds <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">[varItem: <span class="font-mono">' . $v->id . '</span>]</span></div>';
                            }
                            $html .= '</div>';
                        }
                    }
                    $html .= '</div>';
                }
            }

            $html .= '</td>';
            $html .= '<td class="py-2 pr-4 text-right align-top">' . ($units ?? '—') . '</td>';
            $html .= '<td class="py-2 pr-4 text-right align-top">' . ($gUnit !== null ? $quote->currencyDto()->format($gUnit) : '—') . '</td>';
            $html .= '<td class="py-2 text-right align-top">' . $quote->currencyDto()->format($gTotal) . '</td>';
            $html .= '</tr>';
        }

        // Render individual products
        foreach ($rootProducts as $rp) {
            $pUnits = $rp->totalUnits();
            $pTotal = $rp->totalAmount();
            $pUnit = $rp->avgUnitPrice();

            $html .= '<tr>';
            $html .= '<td class="py-2 pr-4 align-top">' . $i++ . '</td>';
            $html .= '<td class="py-2 pr-4">';
            $html .= '<div class="font-medium">' . e($rp->name) . ' <span class="ml-2 text-xs font-normal text-gray-500 dark:text-gray-400">[item: <span class="font-mono">' . $rp->id . '</span> · tipo: <span class="font-mono">' . $rp->type_name . '</span>]</span></div>';
            
            if ($rp->notes) {
                $html .= '<div class="text-gray-600 dark:text-gray-300">' . e($rp->notes) . '</div>';
            }
            
            if ($rp->variants->count()) {
                $html .= '<div class="mt-2 text-xs text-gray-600 dark:text-gray-400 space-y-1">';
                foreach ($rp->variants->sortBy('position') as $v) {
                    $html .= '<div>◦ Variante: ' . e($v->label) . ' — ' . $v->quantity . ' × ' . $v->money('unit_price') . ' = ' . $quote->currencyDto()->format($v->totalAmount()) . ' <span class="ml-2 text-[11px] text-gray-500 dark:text-gray-400">[varItem: <span class="font-mono">' . $v->id . '</span>]</span></div>';
                }
                $html .= '</div>';
            }

            $html .= '</td>';
            $html .= '<td class="py-2 pr-4 text-right align-top">' . $pUnits . '</td>';
            $html .= '<td class="py-2 pr-4 text-right align-top">' . ($pUnit !== null ? $quote->currencyDto()->format($pUnit) : '—') . '</td>';
            $html .= '<td class="py-2 text-right align-top">' . $quote->currencyDto()->format($pTotal) . '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';

        return $html;
    }
}
