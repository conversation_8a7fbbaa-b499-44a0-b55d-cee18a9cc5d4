<?php

namespace App\Filament\Resources\CustomerQuotes\Pages;

use App\Filament\Resources\CustomerQuotes\CustomerQuoteResource;
use App\Models\CustomerQuote;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Components\Text;
use Filament\Schemas\Schema;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\TextSize;

class QuoteShowPage extends ViewRecord
{
    protected static string $resource = CustomerQuoteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('refresh')
                ->label('Actualizar Cotización')
                ->icon('heroicon-o-arrow-path')
                ->action('refreshQuote')
                ->color('primary'),
            EditAction::make(),
        ];
    }

    public function refreshQuote(): void
    {
        try {
            $this->record->refresh();
            $this->dispatch('quote-refreshed');
        } catch (\Exception $e) {
            \Log::error('Error refreshing quote: ' . $e->getMessage());
        }
    }

    public function infolist(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información de la Cotización')
                    ->description('Datos principales y configuración')
                    ->schema([
                        Text::make('ID Interno')
                            ->content(fn () => "#{$this->record->id}")
                            ->fontFamily('mono')
                            ->weight(FontWeight::Bold),

                        Text::make('Cliente')
                            ->content(fn () => $this->record->customer_name)
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),

                        Text::make('Número de Cotización')
                            ->content(fn () => $this->record->quote_number)
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),

                        Text::make('Moneda')
                            ->content(fn () => $this->record->currency)
                            ->badge(),

                        Text::make('Estado')
                            ->content(fn () => $this->record->status)
                            ->badge(),

                        Text::make('Válida hasta')
                            ->content(fn () => $this->record->valid_until?->format('d/m/Y'))
                            ->date('d/m/Y'),

                        Text::make('Notas Adicionales')
                            ->content(fn () => $this->record->notes)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Resumen Financiero')
                    ->description('Cálculos automáticos de la cotización')
                    ->schema([
                        Text::make('Subtotal')
                            ->content(fn () => '$' . number_format($this->getSubtotal(), 2))
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large),

                        Text::make('Impuestos')
                            ->content(fn () => '$' . number_format($this->getTaxAmount(), 2))
                            ->weight(FontWeight::Bold),

                        Text::make('Total')
                            ->content(fn () => '$' . number_format($this->getTotal(), 2))
                            ->weight(FontWeight::Bold)
                            ->size(TextSize::Large)
                            ->color('success'),
                    ])
                    ->columns(3),

                Section::make('Detalles de Productos')
                    ->description('Grupos, productos y variantes incluidos')
                    ->schema([
                        Text::make('Grupos')
                            ->content(fn () => $this->getGroupsCount())
                            ->numeric(),

                        Text::make('Productos')
                            ->content(fn () => $this->getProductsCount())
                            ->numeric(),

                        Text::make('Variantes')
                            ->content(fn () => $this->getVariantsCount())
                            ->numeric(),
                    ])
                    ->columns(3),
            ]);
    }

    public function getSubtotal(): float
    {
        return $this->record->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->sum(fn($variant) => $variant->quantity * $variant->unit_price);
    }

    public function getTaxAmount(): float
    {
        $subtotal = $this->getSubtotal();
        $taxRate = $this->record->tax_rate ?? 0;
        return $subtotal * ($taxRate / 100);
    }

    public function getTotal(): float
    {
        return $this->getSubtotal() + $this->getTaxAmount();
    }

    public function getGroupsCount(): int
    {
        return $this->record->groups()->count();
    }

    public function getProductsCount(): int
    {
        return $this->record->groups()
            ->withCount('products')
            ->get()
            ->sum('products_count');
    }

    public function getVariantsCount(): int
    {
        return $this->record->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->count();
    }
}