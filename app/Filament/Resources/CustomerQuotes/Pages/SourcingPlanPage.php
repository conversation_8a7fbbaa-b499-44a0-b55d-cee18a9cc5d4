<?php

namespace App\Filament\Resources\CustomerQuotes\Pages;

use App\Filament\Resources\CustomerQuotes\CustomerQuoteResource;
use App\Models\CustomerQuote;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\TextSize;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Builder;

class SourcingPlanPage extends ViewRecord implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = CustomerQuoteResource::class;


    public \Illuminate\Database\Eloquent\Model|string|int|null $record;

    public bool $showDetails = true;
    public bool $showBatchesOnly = false;
    public ?string $filterSupplier = null;
    public ?string $filterStatus = null;

    public function mount(int|string $record): void
    {
        $this->record = $this->resolveRecord($record);
    }

    public function getView(): string
    {
        return 'filament.resources.customer-quotes.pages.sourcing-plan';
    }

    public function getSubheading(): string|Htmlable|null
    {
        $quoteNumber = $this->record->quote_number ?? '—';
        return "Cotización: {$quoteNumber} · Cliente: {$this->record->customer_name} · Moneda: {$this->record->currency}";
    }

    public function getKpis(): array
    {
        $quote = $this->record;
        
        $totalPurchaseOrders = $quote->purchaseOrders()->count();
        $totalItems = $quote->purchaseOrders()->withCount('items')->get()->sum('items_count');
        $totalBatches = $quote->purchaseOrders()
            ->with('items.batches')
            ->get()
            ->flatMap(fn($po) => $po->items)
            ->flatMap(fn($item) => $item->batches)
            ->count();

        $totalValue = $quote->purchaseOrders()->sum('total_amount');
        $confirmedValue = $quote->purchaseOrders()
            ->where('status', 'confirmed')
            ->sum('total_amount');

        return [
            'total_purchase_orders' => $totalPurchaseOrders,
            'total_items' => $totalItems,
            'total_batches' => $totalBatches,
            'total_value' => $totalValue,
            'confirmed_value' => $confirmedValue,
        ];
    }

    public function getSuppliers(): array
    {
        return $this->record->purchaseOrders()
            ->with('supplier')
            ->get()
            ->pluck('supplier.name', 'supplier.id')
            ->unique()
            ->toArray();
    }

    public function getStatuses(): array
    {
        return [
            'draft' => 'Borrador',
            'sent' => 'Enviada',
            'confirmed' => 'Confirmada',
            'closed' => 'Cerrada',
        ];
    }

    public function getPurchaseOrderRows(): \Illuminate\Support\Collection
    {
        $query = $this->record->purchaseOrders()->with(['supplier', 'items.batches']);

        if ($this->filterSupplier) {
            $query->where('supplier_id', $this->filterSupplier);
        }

        if ($this->filterStatus) {
            $query->where('status', $this->filterStatus);
        }

        return $query->get()->map(function ($po) {
            $itemsCount = $po->items->count();
            $batchesCount = $po->items->flatMap(fn($item) => $item->batches)->count();
            
            return [
                'id' => $po->id,
                'supplier_name' => $po->supplier->name,
                'status' => $po->status,
                'items_count' => $itemsCount,
                'batches_count' => $batchesCount,
                'total_amount' => $po->total_amount,
                'created_at' => $po->created_at,
            ];
        });
    }

    public function getItemRows(): \Illuminate\Support\Collection
    {
        $query = $this->record->purchaseOrders()
            ->with(['supplier', 'items.batches']);

        if ($this->filterSupplier) {
            $query->where('supplier_id', $this->filterSupplier);
        }

        if ($this->filterStatus) {
            $query->where('status', $this->filterStatus);
        }

        return $query->get()
            ->flatMap(fn($po) => $po->items->map(fn($item) => [
                'id' => $item->id,
                'purchase_order_id' => $po->id,
                'supplier_name' => $po->supplier->name,
                'product_name' => $item->product_name,
                'quantity' => $item->quantity,
                'unit_price' => $item->unit_price,
                'total_price' => $item->total_price,
                'batches_count' => $item->batches->count(),
                'status' => $po->status,
            ]));
    }

    public function getBatchRows(): \Illuminate\Support\Collection
    {
        $query = $this->record->purchaseOrders()
            ->with(['supplier', 'items.batches']);

        if ($this->filterSupplier) {
            $query->where('supplier_id', $this->filterSupplier);
        }

        if ($this->filterStatus) {
            $query->where('status', $this->filterStatus);
        }

        return $query->get()
            ->flatMap(fn($po) => $po->items)
            ->flatMap(fn($item) => $item->batches->map(fn($batch) => [
                'id' => $batch->id,
                'purchase_order_id' => $po->id,
                'supplier_name' => $po->supplier->name,
                'product_name' => $item->product_name,
                'batch_number' => $batch->batch_number,
                'quantity' => $batch->quantity,
                'status' => $batch->status,
                'pool_state' => $batch->pool_state,
                'created_at' => $batch->created_at,
            ]));
    }

    public function getStatusColor(string $status): string
    {
        return match ($status) {
            'draft' => 'gray',
            'sent' => 'warning',
            'confirmed' => 'success',
            'closed' => 'info',
            default => 'gray',
        };
    }

    public function getPoolStateColor(string $poolState): string
    {
        return match ($poolState) {
            'pending' => 'gray',
            'in_progress' => 'warning',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'gray',
        };
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getPurchaseOrderRows()->toQuery())
            ->columns([
                TextColumn::make('supplier_name')
                    ->label('Proveedor')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('status')
                    ->label('Estado')
                    ->badge()
                    ->color(fn($state) => $this->getStatusColor($state)),
                TextColumn::make('items_count')
                    ->label('Items')
                    ->numeric(),
                TextColumn::make('batches_count')
                    ->label('Lotes')
                    ->numeric(),
                TextColumn::make('total_amount')
                    ->label('Total')
                    ->money('USD'),
                TextColumn::make('created_at')
                    ->label('Creado')
                    ->dateTime(),
            ])
            ->filters([
                Select::make('supplier')
                    ->label('Proveedor')
                    ->options($this->getSuppliers())
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['supplier'],
                            fn (Builder $query, $supplier): Builder => $query->where('supplier_id', $supplier),
                        );
                    }),
                Select::make('status')
                    ->label('Estado')
                    ->options($this->getStatuses())
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['status'],
                            fn (Builder $query, $status): Builder => $query->where('status', $status),
                        );
                    }),
            ])
            ->actions([
                TableAction::make('view')
                    ->label('Ver')
                    ->url(fn($record) => route('filament.admin.resources.purchase-orders.view', $record['id'])),
            ]);
    }
}
