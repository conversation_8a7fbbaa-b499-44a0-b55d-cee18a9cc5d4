<?php

namespace App\Filament\Resources\CustomerQuotes\Pages;

use App\Filament\Resources\CustomerQuotes\CustomerQuoteResource;
use Filament\Actions\Action;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Support\Icons\Heroicon;

class ListCustomerQuotes extends ListRecords
{
    protected static string $resource = CustomerQuoteResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Action::make('wizard')
            //     ->label('Nueva Cotización (Wizard)')
            //     ->icon(Heroicon::Sparkles)
            //     ->color('primary')
            //     ->url(fn () => CustomerQuoteResource::getUrl('wizard'))
            //     ->openUrlInNewTab(false),

            CreateAction::make()
                ->label('Nueva Cotización (Formulario)'),
        ];
    }
}
