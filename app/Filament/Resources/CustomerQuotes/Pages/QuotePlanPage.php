<?php

namespace App\Filament\Resources\CustomerQuotes\Pages;

use App\Filament\Resources\CustomerQuotes\CustomerQuoteResource;
use App\Models\CustomerQuote;
use App\Models\CustomerQuoteGroup;
use App\Models\ProductType;
use App\Services\ProductTypeRules;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\FontWeight;
use Filament\Support\Enums\TextSize;
use Filament\Tables\Actions\Action as TableAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Builder;

class QuotePlanPage extends ViewRecord implements HasTable
{
    use InteractsWithTable;

    protected static string $resource = CustomerQuoteResource::class;


    public \Illuminate\Database\Eloquent\Model|string|int|null $record;

    public bool $showDetails = true;
    public bool $showVariantsOnly = false;
    public bool $attentionOnly = false;
    public ?string $filterCurrency = null;
    public array $variantRequiredKeys = [];

    public function mount(int|string $record): void
    {
        $this->record = $this->resolveRecord($record);
        $this->loadVariantRequiredKeys();
    }

    public function getView(): string
    {
        return 'filament.resources.customer-quotes.pages.quote-plan';
    }

    public function loadVariantRequiredKeys(): void
    {
        $this->variantRequiredKeys = ProductType::with('requiredAttributes')
            ->get()
            ->pluck('requiredAttributes', 'id')
            ->map(fn($attrs) => $attrs->pluck('key')->toArray())
            ->toArray();
    }

    public function getSubheading(): string|Htmlable|null
    {
        $quoteNumber = $this->record->quote_number ?? '—';
        return "Cotización: {$quoteNumber} · Cliente: {$this->record->customer_name} · Moneda: {$this->record->currency}";
    }

    public function getKpis(): array
    {
        $quote = $this->record;
        
        $totalGroups = $quote->groups()->count();
        $totalProducts = $quote->groups()
            ->withCount('products')
            ->get()
            ->sum('products_count');
        $totalVariants = $quote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->count();

        $totalValue = $quote->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->sum(fn($variant) => $variant->quantity * $variant->unit_price);

        $missingAttributes = $this->getVariantRows()
            ->filter(fn($variant) => $this->missingRequiredForVariant($variant['id']))
            ->count();

        return [
            'total_groups' => $totalGroups,
            'total_products' => $totalProducts,
            'total_variants' => $totalVariants,
            'total_value' => $totalValue,
            'missing_attributes' => $missingAttributes,
        ];
    }

    public function getCurrencies(): array
    {
        return $this->record->groups()
            ->with('products.variants')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->pluck('currency')
            ->unique()
            ->mapWithKeys(fn($currency) => [$currency => $currency])
            ->toArray();
    }

    public function missingRequiredForVariant(int $variantId): bool
    {
        $variant = $this->record->groups()
            ->with('products.variants.productType.requiredAttributes')
            ->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants)
            ->firstWhere('id', $variantId);

        if (!$variant || !$variant->productType) {
            return false;
        }

        $requiredKeys = $this->variantRequiredKeys[$variant->productType->id] ?? [];
        $variantAttributes = $variant->attributes ?? [];

        foreach ($requiredKeys as $key) {
            if (!isset($variantAttributes[$key]) || empty($variantAttributes[$key])) {
                return true;
            }
        }

        return false;
    }

    public function getVariantRows(): \Illuminate\Support\Collection
    {
        $query = $this->record->groups()
            ->with(['products.variants.productType']);

        if ($this->filterCurrency) {
            $query->whereHas('products.variants', function ($q) {
                $q->where('currency', $this->filterCurrency);
            });
        }

        $variants = $query->get()
            ->flatMap(fn($group) => $group->products)
            ->flatMap(fn($product) => $product->variants->map(fn($variant) => [
                'id' => $variant->id,
                'group_name' => $group->name,
                'product_name' => $product->name,
                'variant_name' => $variant->name,
                'quantity' => $variant->quantity,
                'unit_price' => $variant->unit_price,
                'total_price' => $variant->quantity * $variant->unit_price,
                'currency' => $variant->currency,
                'product_type' => $variant->productType?->name,
                'missing_attributes' => $this->missingRequiredForVariant($variant->id),
                'attributes' => $variant->attributes ?? [],
            ]));

        if ($this->attentionOnly) {
            $variants = $variants->filter(fn($variant) => $variant['missing_attributes']);
        }

        return $variants;
    }

    public function getGroupRows(): \Illuminate\Support\Collection
    {
        return $this->record->groups()
            ->with(['products.variants'])
            ->get()
            ->map(function ($group) {
                $subtotal = $group->products
                    ->flatMap(fn($product) => $product->variants)
                    ->sum(fn($variant) => $variant->quantity * $variant->unit_price);

                return [
                    'id' => $group->id,
                    'name' => $group->name,
                    'products_count' => $group->products->count(),
                    'variants_count' => $group->products->flatMap(fn($product) => $product->variants)->count(),
                    'subtotal' => $subtotal,
                ];
            });
    }

    public function getRootProductRows(): \Illuminate\Support\Collection
    {
        return $this->record->groups()
            ->with(['products.variants'])
            ->get()
            ->flatMap(fn($group) => $group->products->map(fn($product) => [
                'id' => $product->id,
                'group_name' => $group->name,
                'name' => $product->name,
                'variants_count' => $product->variants->count(),
                'subtotal' => $product->variants->sum(fn($variant) => $variant->quantity * $variant->unit_price),
            ]));
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getVariantRows()->toQuery())
            ->columns([
                TextColumn::make('group_name')
                    ->label('Grupo')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('product_name')
                    ->label('Producto')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('variant_name')
                    ->label('Variante')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('quantity')
                    ->label('Cantidad')
                    ->numeric(),
                TextColumn::make('unit_price')
                    ->label('Precio Unit.')
                    ->money('USD'),
                TextColumn::make('total_price')
                    ->label('Total')
                    ->money('USD'),
                TextColumn::make('currency')
                    ->label('Moneda')
                    ->badge(),
                TextColumn::make('missing_attributes')
                    ->label('Faltan Atributos')
                    ->badge()
                    ->color(fn($state) => $state ? 'danger' : 'success')
                    ->formatStateUsing(fn($state) => $state ? 'Sí' : 'No'),
            ])
            ->filters([
                Select::make('currency')
                    ->label('Moneda')
                    ->options($this->getCurrencies())
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['currency'],
                            fn (Builder $query, $currency): Builder => $query->where('currency', $currency),
                        );
                    }),
                Toggle::make('attention_only')
                    ->label('Solo atención')
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['attention_only'],
                            fn (Builder $query): Builder => $query->where('missing_attributes', true),
                        );
                    }),
            ])
            ->actions([
                TableAction::make('view')
                    ->label('Ver')
                    ->url(fn($record) => route('filament.admin.resources.customer-quote-product-variants.view', $record['id'])),
            ]);
    }
}
