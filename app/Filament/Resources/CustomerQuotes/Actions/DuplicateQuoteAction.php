<?php

namespace App\Filament\Resources\CustomerQuotes\Actions;

use App\Models\CustomerQuote;
use App\Models\EventLog;
use Filament\Actions\Action;
use Filament\Notifications\Notification;

class DuplicateQuoteAction
{
    public static function make(): Action
    {
        return Action::make('duplicateQuote')
            ->label('Duplicar')
            ->icon('heroicon-o-document-duplicate')
            ->color('warning')
            ->requiresConfirmation()
            ->modalHeading('Duplicar Cotización')
            ->modalDescription('¿Estás seguro de que quieres duplicar esta cotización? Se creará una nueva cotización con el mismo contenido.')
            ->action(function (CustomerQuote $record) {
                self::duplicateQuote($record);
            });
    }

    protected static function duplicateQuote(CustomerQuote $originalQuote): void
    {
        try {
            $newQuote = CustomerQuote::create([
                'quote_number' => self::generateQuoteNumber(),
                'client_name' => $originalQuote->client_name.' (Copia)',
                'project_id' => $originalQuote->project_id,
                'currency' => $originalQuote->currency,
                'country' => $originalQuote->country,
                'valid_until' => now()->addDays(30),
                'status' => 'borrador',
                'notes' => $originalQuote->notes,
            ]);

            foreach ($originalQuote->groups as $group) {
                $newGroup = $newQuote->groups()->create([
                    'name' => $group->name,
                    'notes' => $group->notes,
                    'position' => $group->position,
                    'quantity' => $group->quantity,
                    'unit_price_minor' => $group->unit_price_minor,
                    'price_currency' => $group->price_currency,
                    'fx_rate_to_quote' => $group->fx_rate_to_quote,
                ]);

                foreach ($group->products as $product) {
                    $newProduct = $newGroup->products()->create([
                        'type_id' => $product->type_id,
                        'name' => $product->name,
                        'attributes' => $product->attributes,
                        'specs' => $product->specs,
                        'hs_code' => $product->hs_code,
                        'weight' => $product->weight,
                        'volume' => $product->volume,
                        'type_code' => $product->type_code,
                        'type_name' => $product->type_name,
                        'subcategory_code' => $product->subcategory_code,
                        'category_code' => $product->category_code,
                        'notes' => $product->notes,
                        'position' => $product->position,
                        'units_per_kit' => $product->units_per_kit,
                    ]);

                    foreach ($product->variants as $variant) {
                        $newProduct->variants()->create([
                            'label' => $variant->label,
                            'attributes' => $variant->attributes,
                            'specs' => $variant->specs,
                            'hs_code' => $variant->hs_code,
                            'weight' => $variant->weight,
                            'volume' => $variant->volume,
                            'quantity' => $variant->quantity,
                            'unit_price_minor' => $variant->unit_price_minor,
                            'price_currency' => $variant->price_currency,
                            'fx_rate_to_quote' => $variant->fx_rate_to_quote,
                            'notes' => $variant->notes,
                            'position' => $variant->position,
                        ]);
                    }
                }
            }

            foreach ($originalQuote->products()->whereNull('group_id')->get() as $product) {
                $newProduct = $newQuote->products()->create([
                    'type_id' => $product->type_id,
                    'name' => $product->name,
                    'attributes' => $product->attributes,
                    'specs' => $product->specs,
                    'hs_code' => $product->hs_code,
                    'weight' => $product->weight,
                    'volume' => $product->volume,
                    'type_code' => $product->type_code,
                    'type_name' => $product->type_name,
                    'subcategory_code' => $product->subcategory_code,
                    'category_code' => $product->category_code,
                    'notes' => $product->notes,
                    'position' => $product->position,
                    'units_per_kit' => $product->units_per_kit,
                ]);

                foreach ($product->variants as $variant) {
                    $newProduct->variants()->create([
                        'label' => $variant->label,
                        'attributes' => $variant->attributes,
                        'specs' => $variant->specs,
                        'hs_code' => $variant->hs_code,
                        'weight' => $variant->weight,
                        'volume' => $variant->volume,
                        'quantity' => $variant->quantity,
                        'unit_price_minor' => $variant->unit_price_minor,
                        'price_currency' => $variant->price_currency,
                        'fx_rate_to_quote' => $variant->fx_rate_to_quote,
                        'notes' => $variant->notes,
                        'position' => $variant->position,
                    ]);
                }
            }

            // Registrar evento de duplicación
            EventLog::create([
                'entity' => 'quote',
                'entity_id' => $newQuote->id,
                'action' => 'duplicated',
                'actor_id' => auth()->id(),
                'payload' => [
                    'original_quote_id' => $originalQuote->id,
                    'original_quote_number' => $originalQuote->quote_number,
                    'new_quote_number' => $newQuote->quote_number,
                    'duplicated_groups_count' => $newQuote->groups()->count(),
                    'duplicated_products_count' => $newQuote->products()->count(),
                    'duplicated_variants_count' => $newQuote->products()->withCount('variants')->get()->sum('variants_count'),
                ],
                'created_at' => now(),
            ]);

            Notification::make()->title('Cotización duplicada exitosamente')
                ->body("Se ha creado la cotización {$newQuote->quote_number}")
                ->success()->send();

        } catch (\Exception $e) {
            Notification::make()->title('Error al duplicar cotización')
                ->body('Ocurrió un error inesperado: '.$e->getMessage())
                ->danger()->send();
        }
    }

    protected static function generateQuoteNumber(): string
    {
        $year = now()->year;
        $lastQuote = CustomerQuote::where('quote_number', 'like', "COT-{$year}-%")
            ->orderBy('quote_number', 'desc')
            ->first();

        $newNumber = $lastQuote ? ((int) substr($lastQuote->quote_number, -3)) + 1 : 1;

        return sprintf('COT-%d-%03d', $year, $newNumber);
    }
}
