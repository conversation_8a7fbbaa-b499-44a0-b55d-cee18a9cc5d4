<?php

namespace App\Filament\Resources\CustomerQuotes\Actions;

use Filament\Actions\Action;

class ViewCompositionAction
{
    public static function make(): Action
    {
        return Action::make('viewComposition')
            ->label('Ver Composición')
            ->icon('heroicon-o-eye')
            ->color('info')
            ->url(fn ($record) => route('quotes.show', $record))
            ->openUrlInNewTab();
    }
}
