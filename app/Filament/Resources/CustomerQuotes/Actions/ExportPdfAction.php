<?php

namespace App\Filament\Resources\CustomerQuotes\Actions;

use App\Models\CustomerQuote;
use App\Models\EventLog;
use Filament\Actions\Action;
use Filament\Notifications\Notification;

class ExportPdfAction
{
    public static function make(): Action
    {
        return Action::make('exportPdf')
            ->label('Exportar PDF')
            ->icon('heroicon-o-document-arrow-down')
            ->color('success')
            ->action(function (CustomerQuote $record) {
                self::exportToPdf($record);
            });
    }

    protected static function exportToPdf(CustomerQuote $record): void
    {
        try {
            // Registrar evento de exportación
            EventLog::create([
                'entity' => 'quote',
                'entity_id' => $record->id,
                'action' => 'exported_pdf',
                'actor_id' => auth()->id(),
                'payload' => [
                    'quote_number' => $record->quote_number,
                    'export_format' => 'pdf',
                    'export_timestamp' => now()->toISOString(),
                ],
                'created_at' => now(),
            ]);

            redirect(route('quotes.show', $record));
            Notification::make()->title('Exportación iniciada')
                ->body('La cotización se está preparando para exportar...')->info()->send();
        } catch (\Exception $e) {
            Notification::make()->title('Error al exportar PDF')
                ->body('Ocurrió un error inesperado: '.$e->getMessage())
                ->danger()->send();
        }
    }
}
