<?php

namespace App\Filament\Resources\CustomerQuotes;

use App\Filament\Resources\CustomerQuotes\Pages\CreateCustomerQuote;
use App\Filament\Resources\CustomerQuotes\Pages\CreateQuoteWizard;
use App\Filament\Resources\CustomerQuotes\Pages\EditCustomerQuote;
use App\Filament\Resources\CustomerQuotes\Pages\ListCustomerQuotes;
use App\Filament\Resources\CustomerQuotes\Pages\QuotePlanPage;
use App\Filament\Resources\CustomerQuotes\Pages\QuoteShowPage;
use App\Filament\Resources\CustomerQuotes\Pages\SourcingPlanPage;
use App\Filament\Resources\CustomerQuotes\Pages\ViewCustomerQuote;
use App\Filament\Resources\CustomerQuotes\Schemas\CustomerQuoteForm;
use App\Filament\Resources\CustomerQuotes\Schemas\CustomerQuoteInfolist;
use App\Filament\Resources\CustomerQuotes\Tables\CustomerQuotesTable;
use App\Models\CustomerQuote;
use BackedEnum;
use Filament\Panel;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class CustomerQuoteResource extends Resource
{
    protected static ?string $model = CustomerQuote::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedDocumentText;

    protected static ?string $recordTitleAttribute = 'quote_number';

    protected static ?string $navigationLabel = 'Cotizaciones';

    protected static ?string $modelLabel = 'Cotización';

    protected static ?string $pluralModelLabel = 'Cotizaciones';

    protected static UnitEnum|string|null $navigationGroup = 'Gestión Comercial';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationBadge = null;

    protected static ?string $navigationBadgeColor = null;

    public static function form(Schema $schema): Schema
    {
        return CustomerQuoteForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return CustomerQuoteInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return CustomerQuotesTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCustomerQuotes::route('/'),
            'create' => CreateCustomerQuote::route('/create'),
            // 'wizard' => CreateQuoteWizard::route('/wizard'), // Deshabilitado temporalmente
            'view' => QuoteShowPage::route('/{record}'),
            'quote-plan' => QuotePlanPage::route('/{record}/quote-plan'),
            'sourcing-plan' => SourcingPlanPage::route('/{record}/sourcing-plan'),
            'edit' => EditCustomerQuote::route('/{record}/edit'),
        ];
    }

    public static function getSlug(?Panel $panel = null): string
    {
        return 'customer-quotes';
    }
}
