<?php

namespace App\Filament\Resources\PurchaseOrders\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class PurchaseOrdersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TextColumn::make('quote.id')
                    ->searchable()
                    ->sortable()
                    ->label('Cotización')
                    ->formatStateUsing(fn ($state) => "#{$state}"),
                TextColumn::make('supplier.name')
                    ->searchable()
                    ->sortable()
                    ->label('Proveedor'),
                TextColumn::make('currency')
                    ->searchable()
                    ->sortable()
                    ->label('Moneda'),
                TextColumn::make('incoterm')
                    ->searchable()
                    ->sortable()
                    ->label('Incoterm'),
                BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'borrador',
                        'warning' => 'enviada',
                        'success' => 'confirmada',
                        'danger' => 'cerrada',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'borrador' => 'Borrador',
                        'enviada' => 'Enviada',
                        'confirmada' => 'Confirmada',
                        'cerrada' => 'Cerrada',
                        default => $state,
                    })
                    ->label('Estado'),
                TextColumn::make('items_count')
                    ->counts('items')
                    ->label('Ítems'),
                TextColumn::make('total_amount')
                    ->label('Total')
                    ->getStateUsing(function ($record) {
                        $total = $record->items->sum(function ($item) {
                            return $item->quantity * $item->unit_price;
                        });

                        return $total > 0 ? number_format($total, 2).' '.$record->currency : '-';
                    })
                    ->sortable(false),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Creado'),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Actualizado'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'borrador' => 'Borrador',
                        'enviada' => 'Enviada',
                        'confirmada' => 'Confirmada',
                        'cerrada' => 'Cerrada',
                    ])
                    ->label('Estado'),
                SelectFilter::make('currency')
                    ->options([
                        'USD' => 'USD',
                        'EUR' => 'EUR',
                        'CLP' => 'CLP',
                    ])
                    ->label('Moneda'),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
