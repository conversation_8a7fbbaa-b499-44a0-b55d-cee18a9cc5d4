<?php

namespace App\Filament\Resources\PurchaseOrders\Schemas;

use Filament\Infolists\Components\KeyValueEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class PurchaseOrderInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('id')
                    ->label('ID'),
                TextEntry::make('quote.client.name')
                    ->label('Cliente'),
                TextEntry::make('supplier.name')
                    ->label('Proveedor'),
                TextEntry::make('currency')
                    ->label('Moneda'),
                TextEntry::make('incoterm')
                    ->label('Incoterm'),
                KeyValueEntry::make('terms')
                    ->keyLabel('Término')
                    ->valueLabel('Valor')
                    ->label('Términos comerciales'),
                TextEntry::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'borrador' => 'secondary',
                        'enviada' => 'warning',
                        'confirmada' => 'success',
                        'cerrada' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'borrador' => 'Borrador',
                        'enviada' => 'Enviada',
                        'confirmada' => 'Confirmada',
                        'cerrada' => 'Cerrada',
                        default => $state,
                    })
                    ->label('Estado'),
                TextEntry::make('correlation_id')
                    ->label('ID de Correlación'),
                TextEntry::make('notes')
                    ->label('Notas')
                    ->columnSpanFull(),
                TextEntry::make('created_at')
                    ->dateTime()
                    ->label('Creado'),
                TextEntry::make('updated_at')
                    ->dateTime()
                    ->label('Actualizado'),
            ]);
    }
}
