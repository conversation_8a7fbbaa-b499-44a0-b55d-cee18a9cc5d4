<?php

namespace App\Filament\Resources\PurchaseOrders\Schemas;

use App\Models\CustomerQuote;
use App\Models\Supplier;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class PurchaseOrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('quote_id')
                    ->relationship('quote', 'id')
                    ->getOptionLabelFromRecordUsing(fn (CustomerQuote $record): string => "Cotización #{$record->id}".($record->client ? " - {$record->client->name}" : ''))
                    ->searchable()
                    ->preload()
                    ->required()
                    ->label('Cotización'),
                Select::make('supplier_id')
                    ->relationship('supplier', 'name')
                    ->getOptionLabelFromRecordUsing(fn (Supplier $record): string => ($record->code ? "{$record->code} - " : '').$record->name)
                    ->searchable()
                    ->preload()
                    ->required()
                    ->label('Proveedor'),
                TextInput::make('currency')
                    ->required()
                    ->maxLength(3)
                    ->default('USD')
                    ->label('Moneda'),
                TextInput::make('incoterm')
                    ->required()
                    ->maxLength(50)
                    ->label('Incoterm'),
                KeyValue::make('terms')
                    ->keyLabel('Término')
                    ->valueLabel('Valor')
                    ->addActionLabel('Agregar término')
                    ->label('Términos comerciales'),
                Select::make('status')
                    ->options([
                        'borrador' => 'Borrador',
                        'enviada' => 'Enviada',
                        'confirmada' => 'Confirmada',
                        'cerrada' => 'Cerrada',
                    ])
                    ->required()
                    ->default('borrador')
                    ->label('Estado'),
                TextInput::make('correlation_id')
                    ->required()
                    ->label('ID de Correlación'),
                Textarea::make('notes')
                    ->rows(3)
                    ->columnSpanFull()
                    ->label('Notas'),
            ]);
    }
}
