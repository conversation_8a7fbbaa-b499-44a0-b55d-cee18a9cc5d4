<?php

namespace App\Filament\Resources\PurchaseOrders\RelationManagers;

use App\Filament\Resources\PurchaseOrderItems\Schemas\PurchaseOrderItemForm;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'items';

    protected static ?string $title = 'Ítems de la Orden';

    public function form(Schema $schema): Schema
    {
        return PurchaseOrderItemForm::configure($schema);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TextColumn::make('quoteProductVariant.quoteProduct.name')
                    ->searchable()
                    ->sortable()
                    ->label('Producto')
                    ->description(fn ($record) => $record->quoteProductVariant->label ?? ''),
                TextColumn::make('quantity')
                    ->numeric()
                    ->sortable()
                    ->label('Cantidad'),
                TextColumn::make('unit_price')
                    ->money(fn ($record) => $record->purchaseOrder->currency ?? 'USD')
                    ->sortable()
                    ->label('Precio Unitario'),
                TextColumn::make('lead_time_days')
                    ->numeric()
                    ->sortable()
                    ->suffix(' días')
                    ->label('Tiempo de Entrega'),
                TextColumn::make('production_batches_count')
                    ->counts('productionBatches')
                    ->label('Lotes'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Creado'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['purchase_order_id'] = $this->ownerRecord->id;

                        return $data;
                    }),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
