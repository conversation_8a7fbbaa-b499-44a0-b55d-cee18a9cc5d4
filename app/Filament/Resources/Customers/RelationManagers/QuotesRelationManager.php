<?php

namespace App\Filament\Resources\Customers\RelationManagers;

use Filament\Forms;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables;
use Filament\Tables\Table;

class QuotesRelationManager extends RelationManager
{
    protected static string $relationship = 'quotes';

    protected static ?string $title = 'Cotizaciones';

    protected static ?string $modelLabel = 'Cotización';

    protected static ?string $pluralModelLabel = 'Cotizaciones';

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Forms\Components\TextInput::make('quote_number')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('quote_number')
            ->columns([
                Tables\Columns\TextColumn::make('quote_number')
                    ->label('Número')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->copyable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Estado')
                    ->badge()
                    ->colors([
                        'secondary' => 'borrador',
                        'warning' => 'enviada',
                        'success' => 'aceptada',
                        'danger' => 'rechazada',
                        'gray' => 'expirada',
                    ]),

                Tables\Columns\TextColumn::make('currency')
                    ->label('Moneda')
                    ->badge()
                    ->colors([
                        'primary' => 'CLP',
                        'success' => 'USD',
                        'warning' => 'EUR',
                    ]),

                Tables\Columns\TextColumn::make('country')
                    ->label('País')
                    ->badge()
                    ->colors([
                        'primary' => 'CL',
                        'success' => 'US',
                        'warning' => 'ES',
                    ]),

                Tables\Columns\TextColumn::make('valid_until')
                    ->label('Válida hasta')
                    ->date('d/m/Y')
                    ->sortable()
                    ->color(fn ($record) => $record->valid_until < now() ? 'danger' : 'success'),

                Tables\Columns\TextColumn::make('total')
                    ->label('Total')
                    ->getStateUsing(fn ($record) => $record->money($record->totalAmount()))
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Creada')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Estado')
                    ->options([
                        'borrador' => 'Borrador',
                        'enviada' => 'Enviada',
                        'aceptada' => 'Aceptada',
                        'rechazada' => 'Rechazada',
                        'expirada' => 'Expirada',
                    ]),

                Tables\Filters\SelectFilter::make('currency')
                    ->label('Moneda')
                    ->options([
                        'CLP' => 'Peso Chileno (CLP)',
                        'USD' => 'Dólar Americano (USD)',
                        'EUR' => 'Euro (EUR)',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->url(fn () => route('filament.admin.resources.customer-quotes.create', ['customer_id' => $this->ownerRecord->id])),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => route('filament.admin.resources.customer-quotes.view', $record)),
                Tables\Actions\EditAction::make()
                    ->url(fn ($record) => route('filament.admin.resources.customer-quotes.edit', $record)),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([10, 25, 50]);
    }
}
