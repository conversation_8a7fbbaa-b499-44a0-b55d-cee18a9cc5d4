<?php

namespace App\Filament\Resources\Customers\Tables;

use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class CustomersTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->label('Nombre')->searchable()->sortable(),
                TextColumn::make('company')->label('Empresa')->searchable()->sortable(),
                TextColumn::make('email')->label('Email')->searchable(),
                TextColumn::make('phone')->label('Teléfono'),
                TextColumn::make('is_active')->label('Activo')->badge()->colors([
                    'success' => true,
                    'danger' => false,
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
