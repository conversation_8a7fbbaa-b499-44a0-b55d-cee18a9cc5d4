<?php

namespace App\Filament\Resources\Customers\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CustomerForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Información Personal')
                    ->description('Datos básicos del cliente')
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Nombre')
                            ->helperText('Nombre del contacto principal')
                            ->columnSpan(1),
                        TextInput::make('email')
                            ->email()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->label('Email')
                            ->columnSpan(1),
                        TextInput::make('phone')
                            ->tel()
                            ->maxLength(255)
                            ->label('Teléfono')
                            ->placeholder('+56 9 1234 5678')
                            ->columnSpan(1),
                        TextInput::make('company')
                            ->maxLength(255)
                            ->label('Empresa')
                            ->helperText('Nombre de la empresa (opcional)')
                            ->columnSpan(1),
                    ])->columns(2),

                Section::make('Información Fiscal')
                    ->description('Datos tributarios y de facturación')
                    ->schema([
                        TextInput::make('tax_id')
                            ->maxLength(255)
                            ->label('RUT/CUIT/Tax ID')
                            ->helperText('Identificación tributaria')
                            ->columnSpan(1),
                        TextInput::make('contact_person')
                            ->maxLength(255)
                            ->label('Persona de Contacto')
                            ->helperText('Contacto alternativo')
                            ->columnSpan(1),
                    ])->columns(2),

                Section::make('Dirección')
                    ->description('Información de ubicación')
                    ->schema([
                        TextInput::make('address')
                            ->maxLength(255)
                            ->label('Dirección')
                            ->columnSpan(2),
                        TextInput::make('city')
                            ->maxLength(255)
                            ->label('Ciudad')
                            ->columnSpan(1),
                        TextInput::make('state')
                            ->maxLength(255)
                            ->label('Estado/Región')
                            ->columnSpan(1),
                        Select::make('country')
                            ->options([
                                'CL' => 'Chile',
                                'AR' => 'Argentina',
                                'PE' => 'Perú',
                                'CO' => 'Colombia',
                                'MX' => 'México',
                                'US' => 'Estados Unidos',
                                'ES' => 'España',
                            ])
                            ->searchable()
                            ->label('País')
                            ->columnSpan(1),
                        TextInput::make('postal_code')
                            ->maxLength(255)
                            ->label('Código Postal')
                            ->columnSpan(1),
                    ])->columns(2),

                Section::make('Configuración')
                    ->description('Estado y notas adicionales')
                    ->schema([
                        Toggle::make('is_active')
                            ->default(true)
                            ->label('Cliente Activo')
                            ->helperText('¿Está el cliente activo?')
                            ->columnSpan(1),
                        Textarea::make('notes')
                            ->rows(3)
                            ->label('Notas')
                            ->helperText('Observaciones adicionales sobre el cliente')
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
