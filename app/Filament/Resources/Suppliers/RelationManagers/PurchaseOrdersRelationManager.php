<?php

namespace App\Filament\Resources\Suppliers\RelationManagers;

use App\Filament\Resources\PurchaseOrders\Schemas\PurchaseOrderForm;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class PurchaseOrdersRelationManager extends RelationManager
{
    protected static string $relationship = 'purchaseOrders';

    protected static ?string $title = 'Órdenes de Compra';

    public function form(Schema $schema): Schema
    {
        return PurchaseOrderForm::configure($schema);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TextColumn::make('quote.id')
                    ->searchable()
                    ->sortable()
                    ->label('Cotización')
                    ->formatStateUsing(fn ($state) => "#{$state}"),
                TextColumn::make('currency')
                    ->searchable()
                    ->sortable()
                    ->label('Moneda'),
                TextColumn::make('incoterm')
                    ->searchable()
                    ->sortable()
                    ->label('Incoterm'),
                BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'borrador',
                        'warning' => 'enviada',
                        'success' => 'confirmada',
                        'danger' => 'cerrada',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'borrador' => 'Borrador',
                        'enviada' => 'Enviada',
                        'confirmada' => 'Confirmada',
                        'cerrada' => 'Cerrada',
                        default => $state,
                    })
                    ->label('Estado'),
                TextColumn::make('items_count')
                    ->counts('items')
                    ->label('Ítems'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Creado'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'borrador' => 'Borrador',
                        'enviada' => 'Enviada',
                        'confirmada' => 'Confirmada',
                        'cerrada' => 'Cerrada',
                    ])
                    ->label('Estado'),
            ])
            ->headerActions([
                CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['supplier_id'] = $this->ownerRecord->id;

                        return $data;
                    }),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
