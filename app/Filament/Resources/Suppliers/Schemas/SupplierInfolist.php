<?php

namespace App\Filament\Resources\Suppliers\Schemas;

use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\KeyValueEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class SupplierInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('code')
                    ->label('Código'),
                TextEntry::make('name')
                    ->label('Nombre'),
                KeyValueEntry::make('contact')
                    ->keyLabel('Campo')
                    ->valueLabel('Valor')
                    ->label('Información de contacto'),
                IconEntry::make('active')
                    ->boolean()
                    ->label('Estado'),
                TextEntry::make('notes')
                    ->label('Notas')
                    ->columnSpanFull(),
                TextEntry::make('created_at')
                    ->dateTime()
                    ->label('Creado'),
                TextEntry::make('updated_at')
                    ->dateTime()
                    ->label('Actualizado'),
            ]);
    }
}
