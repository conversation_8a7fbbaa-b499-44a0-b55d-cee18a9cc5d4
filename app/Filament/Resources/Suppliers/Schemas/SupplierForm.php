<?php

namespace App\Filament\Resources\Suppliers\Schemas;

use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class SupplierForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make('Información Básica')
                    ->description('Datos principales del proveedor')
                    ->schema([
                        TextInput::make('code')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(50)
                            ->label('Código')
                            ->helperText('Código único del proveedor')
                            ->columnSpan(1),
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Nombre')
                            ->columnSpan(1),
                        Toggle::make('active')
                            ->default(true)
                            ->label('Activo')
                            ->helperText('¿Está el proveedor activo?')
                            ->columnSpan(1),
                    ])->columns(2),

                Section::make('Información de Contacto')
                    ->description('Datos de contacto y comunicación')
                    ->schema([
                        KeyValue::make('contact')
                            ->keyLabel('Campo')
                            ->valueLabel('Valor')
                            ->addActionLabel('Agregar contacto')
                            ->label('Contactos')
                            ->keyPlaceholder('email')
                            ->valuePlaceholder('<EMAIL>')
                            ->addable(true)
                            ->deletable(true)
                            ->reorderable(false)
                            ->helperText('Campos comunes: email, phone, mobile, address, website, contact_person')
                            ->columnSpanFull(),
                    ]),

                Section::make('Información Adicional')
                    ->description('Notas y observaciones')
                    ->schema([
                        Textarea::make('notes')
                            ->rows(3)
                            ->label('Notas')
                            ->helperText('Observaciones adicionales sobre el proveedor')
                            ->columnSpanFull(),
                    ]),
            ]);
    }
}
