<?php

namespace App\Filament\Resources\Suppliers\Schemas;

use App\Rules\ValidContactValue;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

/**
 * Versión avanzada del formulario de Supplier con diferentes enfoques
 * para validar y restringir campos de contacto
 */
class SupplierFormAdvanced
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('code')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(50)
                    ->label('Código'),

                TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->label('Nombre'),

                // ENFOQUE 1: KeyValue con validaciones personalizadas (ya implementado)
                KeyValue::make('contact')
                    ->keyLabel('Campo')
                    ->valueLabel('Valor')
                    ->addActionLabel('Agregar contacto')
                    ->label('Información de contacto (Flexible)')
                    ->keyPlaceholder('ej: email, phone, address')
                    ->valuePlaceholder('ej: <EMAIL>')
                    ->addable(true)
                    ->deletable(true)
                    ->reorderable(false)
                    ->keyValidationRules([
                        'required',
                        'string',
                        new \App\Rules\ValidContactField,
                    ])
                    ->valueValidationRules([
                        'required',
                        'string',
                        new ValidContactValue,
                    ])
                    ->helperText('Campos permitidos: email, phone, mobile, address, website, contact_person, whatsapp, linkedin, twitter, facebook, instagram, fax, city, state, country, postal_code, timezone, language'),

                // ENFOQUE 2: Repeater con campos predefinidos (más estructurado)
                Repeater::make('contact_structured')
                    ->label('Información de contacto (Estructurada)')
                    ->schema([
                        Select::make('type')
                            ->label('Tipo de contacto')
                            ->options([
                                'email' => 'Email',
                                'phone' => 'Teléfono',
                                'mobile' => 'Móvil',
                                'whatsapp' => 'WhatsApp',
                                'address' => 'Dirección',
                                'website' => 'Sitio Web',
                                'contact_person' => 'Persona de Contacto',
                                'linkedin' => 'LinkedIn',
                                'twitter' => 'Twitter',
                                'facebook' => 'Facebook',
                                'instagram' => 'Instagram',
                                'fax' => 'Fax',
                                'city' => 'Ciudad',
                                'state' => 'Estado/Provincia',
                                'country' => 'País',
                                'postal_code' => 'Código Postal',
                                'timezone' => 'Zona Horaria',
                                'language' => 'Idioma',
                            ])
                            ->required()
                            ->searchable(),

                        TextInput::make('value')
                            ->label('Valor')
                            ->required()
                            ->maxLength(255)
                            ->rules([new ValidContactValue]),
                    ])
                    ->addActionLabel('Agregar contacto')
                    ->collapsible()
                    ->itemLabel(fn (array $state): ?string => $state['type'] ?? null)
                    ->helperText('Selecciona el tipo de contacto y proporciona el valor correspondiente'),

                // ENFOQUE 3: Campos individuales para contactos principales
                TextInput::make('primary_email')
                    ->label('Email Principal')
                    ->email()
                    ->maxLength(255)
                    ->helperText('Email principal de contacto'),

                TextInput::make('primary_phone')
                    ->label('Teléfono Principal')
                    ->tel()
                    ->maxLength(20)
                    ->helperText('Número de teléfono principal'),

                TextInput::make('website')
                    ->label('Sitio Web')
                    ->url()
                    ->maxLength(255)
                    ->helperText('URL del sitio web de la empresa'),

                TextInput::make('contact_person')
                    ->label('Persona de Contacto')
                    ->maxLength(255)
                    ->helperText('Nombre de la persona de contacto principal'),

                Toggle::make('active')
                    ->default(true)
                    ->label('Activo'),

                Textarea::make('notes')
                    ->rows(3)
                    ->columnSpanFull()
                    ->label('Notas'),
            ]);
    }
}
