<?php

namespace App\Filament\Resources\CustomerQuoteProducts;

use App\Filament\Resources\CustomerQuoteProducts\Pages\CreateCustomerQuoteProduct;
use App\Filament\Resources\CustomerQuoteProducts\Pages\EditCustomerQuoteProduct;
use App\Filament\Resources\CustomerQuoteProducts\Pages\ListCustomerQuoteProducts;
use App\Filament\Resources\CustomerQuoteProducts\Pages\ViewCustomerQuoteProduct;
use App\Filament\Resources\CustomerQuoteProducts\Schemas\CustomerQuoteProductForm;
use App\Filament\Resources\CustomerQuoteProducts\Schemas\CustomerQuoteProductInfolist;
use App\Filament\Resources\CustomerQuoteProducts\Tables\CustomerQuoteProductsTable;
use App\Models\CustomerQuoteProduct;
use BackedEnum;
use Filament\Panel;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class CustomerQuoteProductResource extends Resource
{
    protected static ?string $model = CustomerQuoteProduct::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedCube;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationLabel = 'Productos de Cotización';

    protected static ?string $modelLabel = 'Producto de Cotización';

    protected static ?string $pluralModelLabel = 'Productos de Cotización';

    protected static UnitEnum|string|null $navigationGroup = 'Gestión Comercial';

    protected static ?int $navigationSort = 3;

    public static function form(Schema $schema): Schema
    {
        return CustomerQuoteProductForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return CustomerQuoteProductInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return CustomerQuoteProductsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCustomerQuoteProducts::route('/'),
            'create' => CreateCustomerQuoteProduct::route('/create'),
            'view' => ViewCustomerQuoteProduct::route('/{record}'),
            'edit' => EditCustomerQuoteProduct::route('/{record}/edit'),
        ];
    }

    public static function getSlug(?Panel $panel = null): string
    {
        return 'customer-quote-products';
    }
}
