<?php

namespace App\Filament\Resources\CustomerQuoteProducts\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class CustomerQuoteProductsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('quote.quote_number')
                    ->label('Cotización')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                TextColumn::make('name')
                    ->label('Nombre del Producto')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('group.name')
                    ->label('Grupo')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Sin grupo'),

                TextColumn::make('type_name')
                    ->label('Tipo')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('category_code')
                    ->label('Categoría')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('hs_code')
                    ->label('Código HS')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('weight')
                    ->label('Peso (kg)')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('volume')
                    ->label('Volumen (m³)')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('units_per_kit')
                    ->label('Unidades/Kit')
                    ->numeric()
                    ->sortable()
                    ->alignCenter()
                    ->placeholder('N/A'),

                TextColumn::make('totalUnits')
                    ->label('Total Unidades')
                    ->numeric()
                    ->getStateUsing(fn ($record) => $record->totalUnits())
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('totalAmount')
                    ->label('Total')
                    ->getStateUsing(fn ($record) => $record->totalAmount())
                    ->sortable(),

                TextColumn::make('position')
                    ->label('Posición')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Creado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Actualizado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('quote')
                    ->label('Cotización')
                    ->relationship('quote', 'quote_number')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('group')
                    ->label('Grupo')
                    ->relationship('group', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('category_code')
                    ->label('Categoría')
                    ->options(fn () => \App\Models\CustomerQuoteProduct::distinct()
                        ->pluck('category_code', 'category_code')
                        ->filter()
                        ->toArray())
                    ->searchable(),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('position', 'asc');
    }
}
