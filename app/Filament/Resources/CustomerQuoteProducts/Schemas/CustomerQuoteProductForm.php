<?php

namespace App\Filament\Resources\CustomerQuoteProducts\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CustomerQuoteProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        Select::make('customer_quote_id')
                            ->label('Cotización')
                            ->relationship('quote', 'quote_number')
                            ->required()
                            ->searchable()
                            ->preload(),

                        Select::make('group_id')
                            ->label('Grupo')
                            ->relationship('group', 'name')
                            ->searchable()
                            ->preload()
                            ->placeholder('Sin grupo'),

                        TextInput::make('name')
                            ->label('Nombre del Producto')
                            ->required()
                            ->maxLength(255),

                        TextInput::make('position')
                            ->label('Posición')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),

                        Textarea::make('notes')
                            ->label('Notas')
                            ->columnSpanFull()
                            ->rows(3),
                    ])
                    ->columns(2),

                Section::make('Clasificación')
                    ->schema([
                        TextInput::make('type_id')
                            ->label('ID del Tipo')
                            ->numeric(),

                        TextInput::make('type_code')
                            ->label('Código del Tipo')
                            ->maxLength(50),

                        TextInput::make('type_name')
                            ->label('Nombre del Tipo')
                            ->maxLength(255),

                        TextInput::make('category_code')
                            ->label('Código de Categoría')
                            ->maxLength(50),

                        TextInput::make('subcategory_code')
                            ->label('Código de Subcategoría')
                            ->maxLength(50),

                        TextInput::make('hs_code')
                            ->label('Código HS')
                            ->maxLength(20),
                    ])
                    ->columns(2),

                Section::make('Especificaciones')
                    ->schema([
                        TextInput::make('weight')
                            ->label('Peso (kg)')
                            ->numeric()
                            ->step(0.001),

                        TextInput::make('volume')
                            ->label('Volumen (m³)')
                            ->numeric()
                            ->step(0.001),

                        TextInput::make('units_per_kit')
                            ->label('Unidades por Kit')
                            ->numeric()
                            ->minValue(0)
                            ->helperText('Solo aplica para productos en kits con precio fijo'),
                    ])
                    ->columns(2),
            ]);
    }
}
