<?php

namespace App\Filament\Resources\CustomerQuoteProducts\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CustomerQuoteProductInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información del Producto')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Nombre del Producto'),

                        TextEntry::make('quote.quote_number')
                            ->label('Cotización'),

                        TextEntry::make('group.name')
                            ->label('Grupo')
                            ->placeholder('Sin grupo'),

                        TextEntry::make('notes')
                            ->label('Notas')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Clasificación')
                    ->schema([
                        TextEntry::make('type_name')
                            ->label('Tipo de Producto'),

                        TextEntry::make('category_code')
                            ->label('Categoría'),

                        TextEntry::make('subcategory_code')
                            ->label('Subcategoría'),

                        TextEntry::make('hs_code')
                            ->label('Código HS'),
                    ])
                    ->columns(2),

                Section::make('Especificaciones')
                    ->schema([
                        TextEntry::make('weight')
                            ->label('Peso (kg)')
                            ->numeric(),

                        TextEntry::make('volume')
                            ->label('Volumen (m³)')
                            ->numeric(),

                        TextEntry::make('units_per_kit')
                            ->label('Unidades por Kit')
                            ->numeric()
                            ->placeholder('No configurado'),

                        TextEntry::make('position')
                            ->label('Posición')
                            ->numeric(),
                    ])
                    ->columns(2),

                Section::make('Cálculos')
                    ->schema([
                        TextEntry::make('totalUnits')
                            ->label('Total de Unidades')
                            ->numeric()
                            ->getStateUsing(fn ($record) => $record->totalUnits()),

                        TextEntry::make('totalAmount')
                            ->label('Total del Producto')
                            ->getStateUsing(fn ($record) => $record->totalAmount()),

                        TextEntry::make('avgUnitPrice')
                            ->label('Precio Promedio por Unidad')
                            ->getStateUsing(fn ($record) => $record->avgUnitPrice()),

                        TextEntry::make('isPartOfKitWithFixedPrice')
                            ->label('Parte de Kit con Precio Fijo')
                            ->getStateUsing(fn ($record) => $record->isPartOfKitWithFixedPrice() ? 'Sí' : 'No'),
                    ])
                    ->columns(2),
            ]);
    }
}
