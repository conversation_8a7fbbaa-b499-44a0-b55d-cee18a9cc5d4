<?php

namespace App\Filament\Resources\CustomerQuoteProducts\Pages;

use App\Filament\Resources\CustomerQuoteProducts\CustomerQuoteProductResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListCustomerQuoteProducts extends ListRecords
{
    protected static string $resource = CustomerQuoteProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
