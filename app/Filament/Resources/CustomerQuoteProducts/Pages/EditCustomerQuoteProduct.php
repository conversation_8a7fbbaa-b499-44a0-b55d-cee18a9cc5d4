<?php

namespace App\Filament\Resources\CustomerQuoteProducts\Pages;

use App\Filament\Resources\CustomerQuoteProducts\CustomerQuoteProductResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditCustomerQuoteProduct extends EditRecord
{
    protected static string $resource = CustomerQuoteProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
