<?php

namespace App\Filament\Resources\CustomerQuoteGroups\Pages;

use App\Filament\Resources\CustomerQuoteGroups\CustomerQuoteGroupResource;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;

class EditCustomerQuoteGroup extends EditRecord
{
    protected static string $resource = CustomerQuoteGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
