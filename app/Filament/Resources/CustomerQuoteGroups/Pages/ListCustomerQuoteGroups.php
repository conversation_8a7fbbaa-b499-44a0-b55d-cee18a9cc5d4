<?php

namespace App\Filament\Resources\CustomerQuoteGroups\Pages;

use App\Filament\Resources\CustomerQuoteGroups\CustomerQuoteGroupResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListCustomerQuoteGroups extends ListRecords
{
    protected static string $resource = CustomerQuoteGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
