<?php

namespace App\Filament\Resources\CustomerQuoteGroups\Pages;

use App\Filament\Resources\CustomerQuoteGroups\CustomerQuoteGroupResource;
use Filament\Actions\EditAction;
use Filament\Resources\Pages\ViewRecord;

class ViewCustomerQuoteGroup extends ViewRecord
{
    protected static string $resource = CustomerQuoteGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            EditAction::make(),
        ];
    }
}
