<?php

namespace App\Filament\Resources\CustomerQuoteGroups\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CustomerQuoteGroupForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información Básica')
                    ->schema([
                        Select::make('customer_quote_id')
                            ->label('Cotización')
                            ->relationship('quote', 'quote_number')
                            ->required()
                            ->searchable()
                            ->preload(),

                        TextInput::make('name')
                            ->label('Nombre del Grupo')
                            ->required()
                            ->maxLength(255),

                        TextInput::make('position')
                            ->label('Posición')
                            ->numeric()
                            ->default(0)
                            ->minValue(0),

                        Textarea::make('notes')
                            ->label('Notas')
                            ->columnSpanFull()
                            ->rows(3),
                    ])
                    ->columns(2),

                Section::make('Configuración del Kit')
                    ->schema([
                        TextInput::make('quantity')
                            ->label('Cantidad de Kits')
                            ->required()
                            ->numeric()
                            ->default(1)
                            ->minValue(1),

                        TextInput::make('unit_price_minor')
                            ->label('Precio Unitario (centavos)')
                            ->numeric()
                            ->minValue(0)
                            ->helperText('Precio en centavos de la moneda especificada'),

                        TextInput::make('price_currency')
                            ->label('Moneda del Precio')
                            ->maxLength(3)
                            ->helperText('Código de moneda (ej: USD, EUR)'),

                        TextInput::make('fx_rate_to_quote')
                            ->label('Tipo de Cambio a Cotización')
                            ->numeric()
                            ->default(1)
                            ->minValue(0)
                            ->helperText('Factor de conversión a la moneda de la cotización'),
                    ])
                    ->columns(2),
            ]);
    }
}
