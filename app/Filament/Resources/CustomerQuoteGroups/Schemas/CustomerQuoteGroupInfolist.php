<?php

namespace App\Filament\Resources\CustomerQuoteGroups\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;

class CustomerQuoteGroupInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->schema([
                Section::make('Información del Grupo')
                    ->schema([
                        TextEntry::make('name')
                            ->label('Nombre del Grupo'),

                        TextEntry::make('quote.quote_number')
                            ->label('Cotización'),

                        TextEntry::make('notes')
                            ->label('Notas')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('Configuración del Kit')
                    ->schema([
                        TextEntry::make('quantity')
                            ->label('Cantidad de Kits')
                            ->numeric(),

                        TextEntry::make('unit_price_minor')
                            ->label('Precio Unitario')
                            ->placeholder('No configurado'),

                        TextEntry::make('price_currency')
                            ->label('Moneda del Precio')
                            ->placeholder('No configurado'),

                        TextEntry::make('fx_rate_to_quote')
                            ->label('Tipo de Cambio')
                            ->numeric()
                            ->placeholder('No configurado'),
                    ])
                    ->columns(2),

                Section::make('Cálculos')
                    ->schema([
                        TextEntry::make('totalAmount')
                            ->label('Total del Grupo')
                            ->getStateUsing(fn ($record) => $record->totalAmount()),

                        TextEntry::make('avgUnitPrice')
                            ->label('Precio Promedio por Unidad')
                            ->getStateUsing(fn ($record) => $record->avgUnitPrice()),

                        TextEntry::make('totalUnits')
                            ->label('Total de Unidades')
                            ->numeric()
                            ->getStateUsing(fn ($record) => $record->totalUnits()),

                        TextEntry::make('isKitWithFixedPrice')
                            ->label('Tipo de Kit')
                            ->getStateUsing(fn ($record) => $record->isKitWithFixedPrice() ? 'Precio Fijo' : 'Precio por Componentes'),
                    ])
                    ->columns(2),
            ]);
    }
}
