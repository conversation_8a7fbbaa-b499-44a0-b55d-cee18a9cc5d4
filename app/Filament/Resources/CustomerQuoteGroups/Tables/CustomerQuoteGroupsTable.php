<?php

namespace App\Filament\Resources\CustomerQuoteGroups\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class CustomerQuoteGroupsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('quote.quote_number')
                    ->label('Cotización')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                TextColumn::make('name')
                    ->label('Nombre del Grupo')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                TextColumn::make('quantity')
                    ->label('Cantidad')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                BadgeColumn::make('isKitWithFixedPrice')
                    ->label('Tipo')
                    ->getStateUsing(fn ($record) => $record->isKitWithFixedPrice() ? 'Precio Fijo' : 'Por Componentes')
                    ->color(fn ($record) => $record->isKitWithFixedPrice() ? 'success' : 'info'),

                TextColumn::make('unit_price_minor')
                    ->label('Precio Unitario')
                    ->money('USD')
                    ->sortable()
                    ->placeholder('No configurado'),

                TextColumn::make('totalAmount')
                    ->label('Total')
                    ->money('USD')
                    ->getStateUsing(fn ($record) => $record->totalAmount())
                    ->sortable(),

                TextColumn::make('totalUnits')
                    ->label('Total Unidades')
                    ->numeric()
                    ->getStateUsing(fn ($record) => $record->totalUnits())
                    ->sortable()
                    ->alignCenter(),

                BadgeColumn::make('isValidKitComposition')
                    ->label('Composición')
                    ->getStateUsing(fn ($record) => $record->isValidKitComposition() ? 'Válida' : 'Inválida')
                    ->color(fn ($record) => $record->isValidKitComposition() ? 'success' : 'danger'),

                TextColumn::make('position')
                    ->label('Posición')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('Creado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('Actualizado')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('quote')
                    ->label('Cotización')
                    ->relationship('quote', 'quote_number')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('kit_type')
                    ->label('Tipo de Kit')
                    ->options([
                        'fixed' => 'Precio Fijo',
                        'components' => 'Por Componentes',
                    ])
                    ->query(function ($query, array $data) {
                        if ($data['value'] === 'fixed') {
                            return $query->whereNotNull('unit_price_minor');
                        }
                        if ($data['value'] === 'components') {
                            return $query->whereNull('unit_price_minor');
                        }

                        return $query;
                    }),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('position', 'asc');
    }
}
