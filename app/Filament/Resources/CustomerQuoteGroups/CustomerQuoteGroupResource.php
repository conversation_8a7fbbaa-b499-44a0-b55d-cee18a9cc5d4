<?php

namespace App\Filament\Resources\CustomerQuoteGroups;

use App\Filament\Resources\CustomerQuoteGroups\Pages\CreateCustomerQuoteGroup;
use App\Filament\Resources\CustomerQuoteGroups\Pages\EditCustomerQuoteGroup;
use App\Filament\Resources\CustomerQuoteGroups\Pages\ListCustomerQuoteGroups;
use App\Filament\Resources\CustomerQuoteGroups\Pages\ViewCustomerQuoteGroup;
use App\Filament\Resources\CustomerQuoteGroups\Schemas\CustomerQuoteGroupForm;
use App\Filament\Resources\CustomerQuoteGroups\Schemas\CustomerQuoteGroupInfolist;
use App\Filament\Resources\CustomerQuoteGroups\Tables\CustomerQuoteGroupsTable;
use App\Models\CustomerQuoteGroup;
use BackedEnum;
use Filament\Panel;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use UnitEnum;

class CustomerQuoteGroupResource extends Resource
{
    protected static ?string $model = CustomerQuoteGroup::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationLabel = 'Grupos de Cotización';

    protected static ?string $modelLabel = 'Grupo de Cotización';

    protected static ?string $pluralModelLabel = 'Grupos de Cotización';

    protected static UnitEnum|string|null $navigationGroup = 'Gestión Comercial';

    protected static ?int $navigationSort = 2;

    public static function form(Schema $schema): Schema
    {
        return CustomerQuoteGroupForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return CustomerQuoteGroupInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return CustomerQuoteGroupsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCustomerQuoteGroups::route('/'),
            'create' => CreateCustomerQuoteGroup::route('/create'),
            'view' => ViewCustomerQuoteGroup::route('/{record}'),
            'edit' => EditCustomerQuoteGroup::route('/{record}/edit'),
        ];
    }

    public static function getSlug(?Panel $panel = null): string
    {
        return 'customer-quote-groups';
    }
}
