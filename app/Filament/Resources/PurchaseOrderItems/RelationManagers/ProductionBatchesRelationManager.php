<?php

namespace App\Filament\Resources\PurchaseOrderItems\RelationManagers;

use App\Filament\Resources\ProductionBatches\Schemas\ProductionBatchForm;
use Filament\Actions\BulkActionGroup;
use Filament\Actions\CreateAction;
use Filament\Actions\DeleteAction;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class ProductionBatchesRelationManager extends RelationManager
{
    protected static string $relationship = 'productionBatches';

    protected static ?string $title = 'Lotes de Producción';

    public function form(Schema $schema): Schema
    {
        return ProductionBatchForm::configure($schema);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TextColumn::make('quantity')
                    ->numeric()
                    ->sortable()
                    ->label('Cantidad'),
                TextColumn::make('planned_start')
                    ->dateTime()
                    ->sortable()
                    ->label('Inicio Planificado'),
                TextColumn::make('planned_finish')
                    ->dateTime()
                    ->sortable()
                    ->label('Fin Planificado'),
                BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'borrador',
                        'warning' => 'planificado',
                        'primary' => 'en_produccion',
                        'success' => 'completado',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'borrador' => 'Borrador',
                        'planificado' => 'Planificado',
                        'en_produccion' => 'En Producción',
                        'completado' => 'Completado',
                        default => $state,
                    })
                    ->label('Estado'),
                BadgeColumn::make('pool_state')
                    ->colors([
                        'success' => 'available',
                        'danger' => 'consumed',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'available' => 'Disponible',
                        'consumed' => 'Consumido',
                        default => $state,
                    })
                    ->label('Estado del Pool'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Creado'),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'borrador' => 'Borrador',
                        'planificado' => 'Planificado',
                        'en_produccion' => 'En Producción',
                        'completado' => 'Completado',
                    ])
                    ->label('Estado'),
                SelectFilter::make('pool_state')
                    ->options([
                        'available' => 'Disponible',
                        'consumed' => 'Consumido',
                    ])
                    ->label('Estado del Pool'),
            ])
            ->headerActions([
                CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['purchase_order_item_id'] = $this->ownerRecord->id;

                        return $data;
                    }),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
