<?php

namespace App\Filament\Resources\PurchaseOrderItems\Pages;

use App\Filament\Resources\PurchaseOrderItems\PurchaseOrderItemResource;
use App\Filament\Resources\PurchaseOrderItems\Schemas\PurchaseOrderItemInfolist;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Schemas\Schema;

class ViewPurchaseOrderItem extends ViewRecord
{
    protected static string $resource = PurchaseOrderItemResource::class;

    public function infolist(Schema $schema): Schema
    {
        return PurchaseOrderItemInfolist::configure($schema);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
}
