<?php

namespace App\Filament\Resources\PurchaseOrderItems\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class PurchaseOrderItemsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                TextColumn::make('purchaseOrder.supplier.name')
                    ->searchable()
                    ->sortable()
                    ->label('Proveedor'),
                TextColumn::make('quoteProductVariant.quoteProduct.name')
                    ->searchable()
                    ->sortable()
                    ->label('Producto')
                    ->description(fn ($record) => $record->quoteProductVariant->label ?? ''),
                TextColumn::make('quantity')
                    ->numeric()
                    ->sortable()
                    ->label('Cantidad'),
                TextColumn::make('unit_price')
                    ->money(fn ($record) => $record->purchaseOrder->currency ?? 'USD')
                    ->sortable()
                    ->label('Precio Unitario'),
                TextColumn::make('lead_time_days')
                    ->numeric()
                    ->sortable()
                    ->suffix(' días')
                    ->label('Tiempo de Entrega'),
                TextColumn::make('production_batches_count')
                    ->counts('productionBatches')
                    ->label('Lotes'),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Creado'),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Actualizado'),
            ])
            ->filters([
                SelectFilter::make('purchase_order_id')
                    ->relationship('purchaseOrder', 'id')
                    ->label('Orden de Compra'),
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
