<?php

namespace App\Filament\Resources\PurchaseOrderItems\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class PurchaseOrderItemInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('id')
                    ->label('ID'),
                TextEntry::make('purchaseOrder.supplier.name')
                    ->label('Proveedor'),
                TextEntry::make('quoteProductVariant.product.name')
                    ->label('Producto'),
                TextEntry::make('quantity')
                    ->label('Cantidad'),
                TextEntry::make('unit_price')
                    ->money('USD')
                    ->label('Precio Unitario'),
                TextEntry::make('lead_time_days')
                    ->suffix(' días')
                    ->label('Tiempo de Entrega'),
                TextEntry::make('correlation_id')
                    ->label('ID de Correlación'),
                TextEntry::make('notes')
                    ->label('Notas')
                    ->columnSpanFull(),
                TextEntry::make('created_at')
                    ->dateTime()
                    ->label('Creado'),
                TextEntry::make('updated_at')
                    ->dateTime()
                    ->label('Actualizado'),
            ]);
    }
}
