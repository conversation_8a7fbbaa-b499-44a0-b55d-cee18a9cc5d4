<?php

namespace App\Filament\Resources\PurchaseOrderItems\Schemas;

use App\Models\CustomerQuoteProductVariant;
use App\Models\PurchaseOrder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class PurchaseOrderItemForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('purchase_order_id')
                    ->relationship('purchaseOrder', 'id')
                    ->getOptionLabelFromRecordUsing(fn (PurchaseOrder $record): string => "OC #{$record->id}".($record->supplier ? " - {$record->supplier->name}" : ''))
                    ->searchable()
                    ->preload()
                    ->required()
                    ->label('Orden de Compra'),
                Select::make('quote_product_variant_id')
                    ->relationship('quoteProductVariant', 'id')
                    ->getOptionLabelFromRecordUsing(fn (CustomerQuoteProductVariant $record): string => "Variante #{$record->id} - {$record->quoteProduct->name} ({$record->label})")
                    ->searchable()
                    ->preload()
                    ->required()
                    ->label('Variante de Producto'),
                TextInput::make('quantity')
                    ->required()
                    ->numeric()
                    ->minValue(1)
                    ->label('Cantidad'),
                TextInput::make('unit_price')
                    ->numeric()
                    ->step(0.01)
                    ->prefix('$')
                    ->label('Precio Unitario'),
                TextInput::make('lead_time_days')
                    ->numeric()
                    ->minValue(0)
                    ->suffix('días')
                    ->label('Tiempo de Entrega'),
                TextInput::make('correlation_id')
                    ->required()
                    ->label('ID de Correlación'),
                Textarea::make('notes')
                    ->rows(3)
                    ->columnSpanFull()
                    ->label('Notas'),
            ]);
    }
}
