<?php

namespace App\DTOs;

final readonly class Currency
{
    public function __construct(
        public string $code,
        public string $symbol,
        public int $decimals,
        public string $thousandsSeparator,
        public string $decimalSeparator,
        public string $symbolPosition = 'before', // 'before' | 'after'
        public bool $spaceBetween = true,
    ) {}

    public static function from(string $code): self
    {
        $c = strtoupper($code);
        return match ($c) {
            'CLP' => new self('CLP', '$', 0, '.', ',', 'before', true),
            'USD' => new self('USD', '$', 2, ',', '.', 'before', true),
            'EUR' => new self('EUR', '€', 2, '.', ',', 'after', true),
            'MXN' => new self('MXN', '$', 2, ',', '.', 'before', true),
            'GBP' => new self('GBP', '£', 2, ',', '.', 'before', true),
            default => new self($c, $c, 2, ',', '.', 'after', true),
        };
    }

    public function format(float|int $amount): string
    {
        $formatted = number_format((float) $amount, $this->decimals, $this->decimalSeparator, $this->thousandsSeparator);
        $sp = $this->spaceBetween ? ' ' : '';
        return $this->symbolPosition === 'before'
            ? $this->symbol . $sp . $formatted
            : $formatted . $sp . $this->symbol;
    }
}

